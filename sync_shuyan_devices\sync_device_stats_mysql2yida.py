# -*- coding: utf-8 -*-
import os
import sys
import logging
import pymysql
import json
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import argparse
from time import sleep

import sys
sys.path.append(r'D:\yida_program')

# 设置日志级别为 WARNING，这样就不会显示 DEBUG 信息
logging.getLogger("alibabacloud_credentials").setLevel(logging.WARNING)

from alibabacloud_dingtalk.yida_2_0.client import Client as DingTalkYidaClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as yida_models
from alibabacloud_tea_util import models as util_models
from get_token import token
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client

# 导入现有的同步框架
from sync_data_mysql2yida_month import YidaFormDataClient, DataSyncClient

# 设备统计表单宜搭配置
DEVICE_STATS_YIDA_CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-E77F9E8DFC634720939D84F62E52F108HNNV'  # 需要替换为实际的设备统计表单UUID
}

# 设备统计数据库配置 - 连接到包含device_info和shop_entity_mapping表的数据库
DEVICE_STATS_DB_CONFIG = {
    'host': 'localhost',
    'port': 43306,
    'user': 'root',
    'password': 'Hxp@1987!@#',
    'database': 'mydatabase',
    'charset': 'utf8mb4'
}

# 设备统计数据字段映射
DEVICE_STATS_FIELD_MAPPING = {
    'project_name': 'textField_v5sg72e',      # 项目名称
    'shop_entity_name': 'textField_6o1owv5',     # 数衍店铺
    'device_types': 'multiSelectField_mec8jmpc',      # 设备类型（多个类型用逗号分隔）
    'device_count': 'numberField_mel9yrvt'     # 设备数量
}

# 需要比较的字段
DEVICE_STATS_COMPARE_FIELDS = [
    'device_types',     # 设备类型
    'device_count'      # 设备数量
]

class DeviceStatsYidaFormDataClient(YidaFormDataClient):
    """设备统计数据宜搭表单客户端，继承自YidaFormDataClient"""
    
    def __init__(self):
        super().__init__()
        # 使用设备统计表单配置
        self.form_uuid = DEVICE_STATS_YIDA_CONFIG['FORM_UUID']
        self.app_type = DEVICE_STATS_YIDA_CONFIG['APP_TYPE']
        self.system_token = DEVICE_STATS_YIDA_CONFIG['SYSTEM_TOKEN']
        self.user_id = DEVICE_STATS_YIDA_CONFIG['USER_ID']
        self.language = DEVICE_STATS_YIDA_CONFIG['LANGUAGE']
    
    def get_device_stats_form_data(self, page_size: int = 100, search_condition: Optional[List[Dict]] = None) -> List[Dict[str, Any]]:
        """
        获取设备统计表单数据
        
        Args:
            page_size: 每页数据条数，默认100
            search_condition: 查询条件，可选
            
        Returns:
            List[Dict[str, Any]]: 设备统计表单数据列表
        """
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    form_uuid=self.form_uuid,
                    search_condition=json.dumps(search_condition) if search_condition else None,
                    system_token=self.system_token,
                    page_size=page_size,
                    user_id=self.user_id,
                    app_type=self.app_type
                )
                
                # 记录请求参数
                logging.info(f"设备统计表单数据请求参数 - 第 {current_page} 页:")
                logging.info(f"Headers: {headers}")
                logging.info(f"Request: {request}")
                
                result = self.client.search_form_data_second_generation_no_table_field_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 记录响应结果
                logging.info(f"设备统计表单数据响应 - 第 {current_page} 页:")
                sleep(0.2)
                if not result or not result.body or not result.body.data:
                    break
                
                # 提取指定字段
                for item in result.body.data:
                    filtered_data = {
                        'formInstanceId': item.form_instance_id,
                        'formData': item.form_data
                    }
                    all_data.append(filtered_data)
                
                logging.info(f"第 {current_page} 页获取到 {len(result.body.data)} 条设备统计记录")
                
                # 如果获取的数据少于页大小，说明已经是最后一页
                if len(result.body.data) < page_size:
                    break
                    
                current_page += 1
            
            logging.info(f"设备统计表单数据查询完成，共获取到 {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            error_msg = f"获取设备统计表单数据失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            raise Exception(error_msg)
    
    def update_device_stats_form_data(self, form_instance_id: str, new_data: Dict):
        """更新设备统计表单数据"""
        try:
            client = DingTalkYidaClient(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = yida_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            request = yida_models.UpdateFormDataRequest()
            request.app_type = self.app_type
            request.system_token = self.system_token
            request.user_id = self.user_id
            request.language = self.language
            request.form_instance_id = form_instance_id
            request.form_uuid = self.form_uuid
            request.update_form_data_json = json.dumps(new_data, ensure_ascii=False)
            request.use_alias = False
            request.use_latest_version = False
            
            logging.info(f"更新设备统计表单数据请求参数: {new_data}")
            response = client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())
            logging.info(f"更新设备统计表单数据成功: {form_instance_id}")
            return response
            
        except Exception as e:
            logging.error(f"更新设备统计表单数据失败: {str(e)}")
            raise

    def batch_create_device_stats_form_data(self, data_list: List[Dict], batch_size: int = 100):
        """批量创建设备统计表单数据"""
        try:
            client = dingtalkyida_1_0Client(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 将数据列表分成多个批次
            for i in range(0, len(data_list), batch_size):
                batch_data = data_list[i:i + batch_size]
                processed_data_list = []
                
                for item in batch_data:
                    processed_item = {}
                    for key, value in item.items():
                        # 处理数值字段
                        if key.startswith('numberField_'):
                            try:
                                if value is None or value == '':
                                    value = 0
                                processed_item[f"{key}_value"] = str(value)  # 添加字符串版本
                                processed_item[key] = value  # 保持原始值
                            except ValueError as e:
                                logging.error(f"数值转换错误: {value}, {str(e)}")
                                continue
                        else:
                            processed_item[key] = value
                    processed_data_list.append(processed_item)
                
                form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in processed_data_list]
                
                request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                    no_execute_expression=True,
                    form_uuid=self.form_uuid,
                    app_type=self.app_type,
                    asynchronous_execution=True,
                    system_token=self.system_token,
                    keep_running_after_exception=True,
                    user_id=self.user_id,
                    form_data_json_list=form_data_json_list
                )
                
                try:
                    response = client.batch_save_form_data_with_options(request, headers, util_models.RuntimeOptions())
                    
                    # 记录详细的响应信息
                    logging.info(f"批量创建设备统计数据响应状态码: {response.status_code}")
                    logging.info(f"批量创建设备统计数据响应头: {response.headers}")
                    logging.info(f"批量创建设备统计数据响应体: {response.body}")
                    
                    # 检查响应是否成功
                    if response.status_code == 200:
                        if hasattr(response.body, 'result') and response.body.result:
                            logging.info(f"批量创建设备统计表单数据成功，批次 {i//batch_size + 1}，共 {len(batch_data)} 条记录")
                            logging.info(f"成功创建的数据ID: {response.body.result}")
                        else:
                            logging.warning(f"批量创建设备统计数据响应成功但未返回结果，批次 {i//batch_size + 1}")
                    else:
                        logging.error(f"批量创建设备统计表单数据失败，批次 {i//batch_size + 1}: {response.status_code}")
                        logging.error(f"错误响应: {response.body}")
                        raise Exception(f"批量创建设备统计数据失败，状态码: {response.status_code}")
                        
                except Exception as e:
                    logging.error(f"批量创建设备统计表单数据失败，批次 {i//batch_size + 1}: {str(e)}")
                    raise
                
                # 添加延时避免请求过于频繁
                time.sleep(3)
            
        except Exception as e:
            logging.error(f"批量创建设备统计表单数据失败: {str(e)}")
            raise

class DeviceStatsDataSyncClient(DataSyncClient):
    """设备统计数据同步客户端，继承自DataSyncClient"""

    def __init__(self):
        # 配置日志
        current_date = datetime.now().strftime('%Y%m%d')

        # 确保logs目录存在
        os.makedirs('logs', exist_ok=True)

        logging.basicConfig(
            filename=f'logs/sync_device_stats_mysql2yida_log_{current_date}.txt',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            encoding='utf-8'
        )

        # 同时输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(formatter)
        logging.getLogger().addHandler(console_handler)

        logging.info("设备统计数据同步客户端初始化完成")

        # 初始化MySQL连接 - 使用设备统计数据库配置
        self.mysql_conn = pymysql.connect(**DEVICE_STATS_DB_CONFIG)

        # 初始化设备统计宜搭客户端
        self.device_stats_yida_client = DeviceStatsYidaFormDataClient()

    def get_mysql_device_stats_data(self) -> List[Dict]:
        """
        从MySQL获取设备统计数据
        :return: 设备统计数据列表
        """
        try:
            cursor = self.mysql_conn.cursor(pymysql.cursors.DictCursor)

            # 构建SQL查询 - 使用用户提供的SQL语句
            sql = """
                SELECT
                    project_name AS 项目名称,
                    b.shop_entity_name AS 数衍店铺,
                    GROUP_CONCAT(DISTINCT device_type ORDER BY device_type) AS 设备类型,
                    COUNT(*) AS 数量
                FROM device_info a, shop_entity_mapping b
                WHERE is_deleted_code = 'N'
                  AND device_state_code = '0'
                  AND a.shop_entity_id = b.shop_entity_id
                GROUP BY project_name, b.shop_entity_name
                ORDER BY project_name, b.shop_entity_name;
            """
            cursor.execute(sql)
            result = cursor.fetchall()

            # 转换字段名为英文，便于后续处理
            converted_result = []
            for row in result:
                converted_row = {
                    'project_name': row['项目名称'],
                    'shop_entity_name': row['数衍店铺'],
                    'device_types': row['设备类型'],
                    'device_count': row['数量']
                }
                converted_result.append(converted_row)

            logging.info(f"MySQL设备统计数据查询成功，共获取 {len(converted_result)} 条记录")
            return converted_result
        except Exception as e:
            logging.error(f"获取MySQL设备统计数据失败: {str(e)}")
            return []

    def sync_device_stats_data(self):
        """设备统计数据同步主流程"""
        try:
            logging.info("=================开始设备统计数据同步=============")
            logging.info(f"对比字段: {DEVICE_STATS_COMPARE_FIELDS}")
            logging.info(f"使用项目名称+店铺名称作为主要对比标识")

            # 获取MySQL设备统计数据
            mysql_data = self.get_mysql_device_stats_data()
            if not mysql_data:
                logging.error("未获取到MySQL设备统计数据")
                return

            logging.info(f"开始处理 {len(mysql_data)} 条MySQL设备统计数据")

            # 按项目名称+店铺名称分组MySQL数据
            mysql_data_dict = {}
            for item in mysql_data:
                key = f"{item['project_name']}_{item['shop_entity_name']}"
                mysql_data_dict[key] = item
                logging.debug(f"MySQL设备统计数据分组 - 键: {key}, 值: {item}")

            logging.info(f"按项目+店铺分组后，共有 {len(mysql_data_dict)} 个统计项")

            # 获取所有宜搭设备统计表单数据
            yida_data = self.device_stats_yida_client.get_device_stats_form_data()
            logging.info(f"获取到 {len(yida_data)} 条宜搭设备统计表单数据")

            # 创建宜搭数据索引
            yida_data_dict = {}
            for item in yida_data:
                form_data = item['formData']
                project_name = form_data.get(DEVICE_STATS_FIELD_MAPPING['project_name'], '')
                shop_entity_name = form_data.get(DEVICE_STATS_FIELD_MAPPING['shop_entity_name'], '')

                key = f"{project_name}_{shop_entity_name}"
                yida_data_dict[key] = {
                    'form_instance_id': item['formInstanceId'],
                    'form_data': form_data
                }

                logging.debug(f"宜搭设备统计数据分组 - 键: {key}, 值: {form_data}")

            logging.info(f"宜搭数据索引完成 - 共 {len(yida_data_dict)} 个统计项")

            # 处理数据同步
            total_update_count = 0
            total_insert_count = 0
            total_error_count = 0
            insert_data_list = []

            # 按项目+店铺处理数据
            for key, mysql_item in mysql_data_dict.items():
                try:
                    if key in yida_data_dict:
                        yida_item = yida_data_dict[key]['form_data']
                        logging.debug(f"开始对比设备统计数据 - 键: {key}")
                        logging.debug(f"MySQL数据: {mysql_item}")
                        logging.debug(f"宜搭数据: {yida_item}")

                        # 比较指定字段
                        need_update, changed_fields = self.compare_device_stats_data(mysql_item, yida_item)

                        if need_update:
                            try:
                                form_data = {
                                    DEVICE_STATS_FIELD_MAPPING['project_name']: mysql_item['project_name'],
                                    DEVICE_STATS_FIELD_MAPPING['shop_entity_name']: mysql_item['shop_entity_name'],
                                    DEVICE_STATS_FIELD_MAPPING['device_types']: mysql_item['device_types'],
                                    DEVICE_STATS_FIELD_MAPPING['device_count']: mysql_item['device_count']
                                }

                                form_instance_id = yida_data_dict[key]['form_instance_id']
                                logging.info(f"开始更新设备统计记录 - 键: {key}, 表单实例ID: {form_instance_id}")
                                self.device_stats_yida_client.update_device_stats_form_data(form_instance_id, form_data)
                                total_update_count += 1
                                logging.info(f"更新设备统计记录成功，变更字段: {changed_fields}")
                            except Exception as e:
                                logging.error(f"更新设备统计记录时发生错误: {str(e)}")
                                logging.error(f"错误数据 - 键: {key}")
                                total_error_count += 1
                    else:
                        try:
                            form_data = {
                                DEVICE_STATS_FIELD_MAPPING['project_name']: mysql_item['project_name'],
                                DEVICE_STATS_FIELD_MAPPING['shop_entity_name']: mysql_item['shop_entity_name'],
                                DEVICE_STATS_FIELD_MAPPING['device_types']: mysql_item['device_types'],
                                DEVICE_STATS_FIELD_MAPPING['device_count']: mysql_item['device_count']
                            }
                            logging.debug(f"准备插入新设备统计记录 - 键: {key}, 数据: {form_data}")
                            insert_data_list.append(form_data)
                            total_insert_count += 1
                        except Exception as e:
                            logging.error(f"转换设备统计数据格式失败: {str(e)}")
                            logging.error(f"错误数据 - 键: {key}, MySQL数据: {mysql_item}")
                            total_error_count += 1
                except Exception as e:
                    logging.error(f"处理设备统计数据项失败: {str(e)}")
                    logging.error(f"错误数据 - 键: {key}, MySQL数据: {mysql_item}")
                    total_error_count += 1
                    continue

            # 批量插入新数据
            if insert_data_list:
                try:
                    logging.info(f"开始批量插入 {len(insert_data_list)} 条新设备统计记录")
                    self.device_stats_yida_client.batch_create_device_stats_form_data(insert_data_list)
                    logging.info(f"批量插入设备统计数据完成，共 {len(insert_data_list)} 条记录")
                except Exception as e:
                    logging.error(f"批量插入设备统计数据失败: {str(e)}")
                    logging.error(f"错误数据列表: {insert_data_list}")
                    total_error_count += len(insert_data_list)

            # 统计信息
            logging.info(f"设备统计数据同步完成！")
            logging.info(f"处理统计项数量: {len(mysql_data_dict)}")
            logging.info(f"更新统计项数量: {total_update_count} 条")
            logging.info(f"插入统计项数量: {total_insert_count} 条")
            logging.info(f"错误统计项数量: {total_error_count} 条")

        except Exception as e:
            logging.error(f"设备统计数据同步失败: {str(e)}")
            raise

    def compare_device_stats_data(self, mysql_item: Dict, yida_item: Dict) -> tuple:
        """
        比较MySQL和宜搭设备统计数据

        Args:
            mysql_item: MySQL设备统计数据
            yida_item: 宜搭设备统计数据

        Returns:
            tuple: (是否需要更新, 变更字段列表)
        """
        need_update = False
        changed_fields = []

        # 只比较用户指定的两个字段：设备类型和设备数量
        for field in DEVICE_STATS_COMPARE_FIELDS:
            mysql_value = mysql_item.get(field, '')
            yida_value = yida_item.get(DEVICE_STATS_FIELD_MAPPING[field], '')

            # 对于数值字段，需要进行类型转换比较
            if field == 'device_count':
                try:
                    mysql_value = int(mysql_value) if mysql_value else 0
                    yida_value = int(yida_value) if yida_value else 0
                except (ValueError, TypeError):
                    logging.warning(f"数值字段转换失败 - 字段: {field}, MySQL值: {mysql_value}, 宜搭值: {yida_value}")
                    mysql_value = str(mysql_value)
                    yida_value = str(yida_value)

            if str(mysql_value) != str(yida_value):
                need_update = True
                changed_fields.append({
                    'field': field,
                    'old_value': yida_value,
                    'new_value': mysql_value
                })
                logging.debug(f"字段 {field} 需要更新 - 旧值: {yida_value}, 新值: {mysql_value}")

        return need_update, changed_fields

    def __del__(self):
        """析构函数，确保资源正确释放"""
        try:
            if hasattr(self, 'mysql_conn') and self.mysql_conn:
                self.mysql_conn.close()
                logging.info("MySQL连接已关闭")
        except Exception as e:
            logging.error(f"关闭MySQL连接时发生错误: {str(e)}")

    def close_connections(self):
        """手动关闭连接"""
        try:
            if hasattr(self, 'mysql_conn') and self.mysql_conn:
                self.mysql_conn.close()
                logging.info("MySQL连接已手动关闭")
        except Exception as e:
            logging.error(f"手动关闭MySQL连接时发生错误: {str(e)}")

def main():
    """主函数"""
    try:
        # 初始化设备统计数据同步客户端
        client = DeviceStatsDataSyncClient()

        # 执行设备统计数据同步
        logging.info("=================开始设备统计数据同步=============")
        client.sync_device_stats_data()
        logging.info("=================设备统计数据同步完成=============")

    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
