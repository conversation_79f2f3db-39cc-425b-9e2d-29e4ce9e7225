2025-08-08 09:00:03,291 - INFO - 数据库连接成功
2025-08-08 09:00:03,525 - INFO - 获取钉钉access_token成功
2025-08-08 09:00:03,525 - INFO - 宜搭客户端初始化成功
2025-08-08 09:00:03,525 - INFO - 正在获取数据库店铺信息...
2025-08-08 09:00:03,525 - INFO - 第一步：获取基础店铺信息...
2025-08-08 09:00:03,556 - INFO - 获取基础店铺信息成功，共 1296 条记录
2025-08-08 09:00:03,556 - INFO - 第二步：获取上个月或本月有销售记录的店铺...
2025-08-08 09:00:03,666 - INFO - 上个月或本月有销售记录的店铺数量: 657
2025-08-08 09:00:03,666 - INFO - 第三步：获取上个月以前有销售记录的店铺...
2025-08-08 09:00:03,869 - INFO - 上个月以前有销售记录的店铺数量: 638
2025-08-08 09:00:03,869 - INFO - 检测到新店铺数量: 19
2025-08-08 09:00:03,869 - INFO - 新店铺编码列表: ['100101426', '100099603', '330470732', '100101317', '100101441', '100101442', '100101318', '100101169', '100099438', '100101434', '100101311', '100101431', '100101278', '100101301', '100101319', '*********', '100101443', '100101437', '100101310']
2025-08-08 09:00:03,869 - INFO - 成功获取数据库店铺信息，共 1296 条记录
2025-08-08 09:00:03,869 - INFO - 正在获取宜搭店铺信息...
2025-08-08 09:00:05,306 - INFO - 店铺 100101444 的userid值为空
2025-08-08 09:00:05,306 - INFO - 店铺 100100104 的userid值为空
2025-08-08 09:00:05,306 - INFO - 店铺 100101441 的userid值为空
2025-08-08 09:00:07,291 - INFO - 店铺 100101438 的userid值为空
2025-08-08 09:00:07,291 - INFO - 店铺 100101303 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100278 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100328 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100099216 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100369 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100099270 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100926 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100099275 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100269 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100327 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100368 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100099274 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100099290 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100099307 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100099338 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100238 的userid值为空
2025-08-08 09:00:07,306 - INFO - 店铺 100100291 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100335 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099218 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100373 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099249 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100378 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099293 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099271 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100377 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099277 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099292 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099313 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099226 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100375 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100664 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099281 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099295 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100339 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100374 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099237 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099256 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099280 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099294 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100249 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099177 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100314 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100434 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099283 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099176 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100665 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099282 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100437 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099322 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099345 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100321 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099317 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100262 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099261 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099298 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100265 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099259 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099297 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099323 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099147 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100264 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099264 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099153 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099263 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099299 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099332 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100224 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099150 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099212 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099266 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100438 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099289 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100101122 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099304 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099931 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100100361 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099231 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099243 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099265 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099272 的userid值为空
2025-08-08 09:00:08,056 - INFO - 店铺 100099303 的userid值为空
2025-08-08 09:00:09,103 - INFO - 店铺 100101312 的userid值为空
2025-08-08 09:00:09,103 - INFO - 店铺 100101313 的userid值为空
2025-08-08 09:00:09,119 - INFO - 店铺 100101434 的userid值为空
2025-08-08 09:00:09,119 - INFO - 店铺 100098598 的userid值为空
2025-08-08 09:00:09,119 - INFO - 店铺 100101275 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100101314 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100101309 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100101427 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100101428 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100101285 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100101430 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100101162 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100101426 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100100657 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100100832 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100098453 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100099214 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100098428 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100099748 的userid值为空
2025-08-08 09:00:11,666 - INFO - 店铺 100098437 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100085 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100110 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100135 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100854 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100842 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098300 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100048 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098427 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100071 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098274 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098440 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100091 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100113 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098303 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100140 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100851 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098289 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100839 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098260 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100070 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100089 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100112 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098273 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100852 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098486 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100840 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100101 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098296 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100130 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098355 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100849 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100129 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100845 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098477 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100033 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098295 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098250 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100850 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100050 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098237 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100083 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098276 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100108 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100094 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100117 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098474 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100134 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100858 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098343 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100846 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100829 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098398 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098284 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100049 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100106 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100133 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098249 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100092 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098297 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100099109 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100114 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098275 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098342 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100141 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100855 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098597 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100843 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098385 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100037 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100371 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100053 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100075 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098447 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100096 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098461 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100856 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100844 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100098384 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100035 的userid值为空
2025-08-08 09:00:12,353 - INFO - 店铺 100100052 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100074 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100098445 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100095 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100121 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100067 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100088 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100111 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100136 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100853 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100098287 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100098485 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100841 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099117 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101121 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099189 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099305 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101265 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101304 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099188 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100881 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100456 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099284 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100880 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101119 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099829 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100236 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099936 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101120 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100447 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100865 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100875 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099186 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099824 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100217 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099930 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099346 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100964 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099187 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099984 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099928 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099148 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100894 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099983 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099796 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101118 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100435 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099870 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099199 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099985 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099978 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100436 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101181 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099185 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100233 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099980 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100893 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100892 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100433 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099979 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099923 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101183 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099197 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100241 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100098583 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101173 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100944 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099803 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100098579 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100101153 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100099804 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100359 的userid值为空
2025-08-08 09:00:13,087 - INFO - 店铺 100100344 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100818 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099863 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100347 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100667 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100891 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099853 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100890 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100342 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099861 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100670 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100341 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100887 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100481 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100886 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099911 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100646 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100889 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100888 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100883 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100922 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100882 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100016 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099835 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100885 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099910 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100884 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099956 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100006 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100900 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099903 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100024 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100293 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100098391 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100044 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099198 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100061 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100098436 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 ********* 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100833 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100453 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100199 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100098358 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101129 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100379 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100229 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100663 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100042 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099192 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100098405 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100060 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100082 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099101 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100380 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101243 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101241 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099193 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101224 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100084 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101242 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101256 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100100918 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101125 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101154 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101156 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101132 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101155 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100099217 的userid值为空
2025-08-08 09:00:13,806 - INFO - 店铺 100101283 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100099190 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100101284 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100101282 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100101287 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100101296 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100101295 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100101308 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100099191 的userid值为空
2025-08-08 09:00:13,822 - INFO - 店铺 100099171 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100101123 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100466 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100101124 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099291 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100376 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098351 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100156 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099615 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099905 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098488 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098350 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099503 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100027 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099613 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100004 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100154 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099500 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100101180 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100046 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099899 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098424 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098246 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100128 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100182 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100917 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100201 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098360 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098572 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099995 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098377 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100026 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100300 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100279 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098596 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099681 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100045 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100870 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099588 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098423 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099675 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099988 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100098258 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100177 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100162 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100198 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099793 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100187 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099104 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100099816 的userid值为空
2025-08-08 09:00:14,494 - INFO - 店铺 100100904 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100210 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100099499 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100148 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100175 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100099913 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100196 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100303 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100280 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100029 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100098317 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100098397 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100860 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100255 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100661 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100098589 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100152 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100099525 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100160 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100180 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100098594 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100099119 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100099987 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100185 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100098561 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100098348 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100207 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100861 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100098559 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100098362 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100150 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100258 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100099906 的userid值为空
2025-08-08 09:00:14,509 - INFO - 店铺 100100178 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100099811 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100028 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100098588 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100143 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100320 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100099585 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100098247 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100166 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100098396 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100099797 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100047 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100810 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100192 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100460 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100101190 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100834 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100099831 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100214 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100459 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100099650 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100098367 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100478 的userid值为空
2025-08-08 09:00:15,119 - INFO - 店铺 100100261 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100101200 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100020 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100465 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100101236 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100041 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100859 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100142 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100101254 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100101246 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100056 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099794 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100189 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099629 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100101292 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100266 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100101306 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100081 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100212 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100098449 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100098268 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099636 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100838 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100260 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100030 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100480 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100462 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100461 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100146 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099553 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100172 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100365 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100040 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100195 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099541 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100251 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099847 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100216 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100055 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100098369 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099555 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100098433 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099992 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100366 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100076 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100144 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099507 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100098253 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100169 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100098448 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099806 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100097 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100194 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099506 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099587 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100847 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100100215 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 100099539 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 ********* 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 ********* 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 ********* 的userid值为空
2025-08-08 09:00:15,134 - INFO - 店铺 ********* 的userid值为空
2025-08-08 09:00:15,134 - INFO - 获取宜搭店铺信息成功，共 1294 条记录
2025-08-08 09:00:15,134 - INFO - 成功获取宜搭店铺信息，共 1294 条记录
2025-08-08 09:00:15,134 - INFO - 正在获取用户信息并处理oa_account...
2025-08-08 09:00:15,134 - INFO - 获取用户信息成功，共 89 条记录
2025-08-08 09:00:15,431 - INFO - 需要查询的手机号数量: 49
2025-08-08 09:00:22,775 - INFO - 处理oa_account完成，缓存大小: 49
2025-08-08 09:00:22,790 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-08-08 09:00:22,790 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-08-08 09:00:22,790 - INFO - 开始对比数据库和宜搭数据...
2025-08-08 09:00:22,790 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-08-08 09:00:22,790 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-08-08 09:00:22,790 - INFO - 仅在数据库存在的记录数: 2
2025-08-08 09:00:22,790 - INFO - 需要插入的记录: ['*********', '*********']
2025-08-08 09:00:22,790 - INFO - 仅在宜搭存在的记录数: 0
2025-08-08 09:00:22,790 - INFO - 店铺 ********* 存在字段差异: ['fz_store_code_diff']
2025-08-08 09:00:22,869 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-08-08 09:00:22,900 - INFO - 检测到店铺名称变更 - 店铺编码: *********, 原名称: -多经 鳄鱼恤/途袖, 新名称: 多经-鳄鱼恤/运动町
2025-08-08 09:00:22,900 - INFO - 店铺 ********* 存在字段差异: ['store_name_diff', 'status_diff']
2025-08-08 09:00:22,978 - INFO - 店铺 100101442 存在字段差异: ['is_new_store_diff']
2025-08-08 09:00:23,025 - INFO - 店铺 100100322 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,025 - INFO - 店铺 100100322 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100322 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100322 - 仅在宜搭存在的userid: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100362 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,025 - INFO - 店铺 100100362 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100362 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100362 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100323 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,025 - INFO - 店铺 100100323 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100323 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100323 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100360 存在字段差异: ['status_diff']
2025-08-08 09:00:23,025 - INFO - 店铺 100100316 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,025 - INFO - 店铺 100100316 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:23,025 - INFO - 店铺 100100316 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,040 - INFO - 店铺 100100316 - 仅在宜搭存在的userid: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:23,040 - INFO - 店铺 100101130 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,040 - INFO - 店铺 100101130 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:23,040 - INFO - 店铺 100101130 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,040 - INFO - 店铺 100101130 - 仅在宜搭存在的userid: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:23,040 - INFO - 店铺 100101108 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,040 - INFO - 店铺 100101108 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,040 - INFO - 店铺 100101108 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,040 - INFO - 店铺 100101108 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,040 - INFO - 店铺 100100248 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,040 - INFO - 店铺 100100248 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:23,040 - INFO - 店铺 100100248 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,040 - INFO - 店铺 100100248 - 仅在宜搭存在的userid: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:23,040 - INFO - 店铺 100100355 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,040 - INFO - 店铺 100100355 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322'}
2025-08-08 09:00:23,040 - INFO - 店铺 100100355 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,040 - INFO - 店铺 100100355 - 仅在宜搭存在的userid: {'1911111840fdac3db9f89204e6eb4322'}
2025-08-08 09:00:23,056 - INFO - 店铺 100100340 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,056 - INFO - 店铺 100100340 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,056 - INFO - 店铺 100100340 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,056 - INFO - 店铺 100100340 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,056 - INFO - 店铺 100100298 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,056 - INFO - 店铺 100100298 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,056 - INFO - 店铺 100100298 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:23,056 - INFO - 店铺 100100298 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:23,072 - INFO - 店铺 100101262 存在字段差异: ['userid_diff']
2025-08-08 09:00:23,072 - INFO - 店铺 100101262 userid差异 - 数据库: {'1977cc9e77b959503f06ffc47f2b250c'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322'}
2025-08-08 09:00:23,072 - INFO - 店铺 100101262 - 仅在数据库存在的userid: {'1977cc9e77b959503f06ffc47f2b250c'}
2025-08-08 09:00:23,072 - INFO - 店铺 100101262 - 仅在宜搭存在的userid: {'1911111840fdac3db9f89204e6eb4322'}
2025-08-08 09:00:23,103 - INFO - 店铺 100101443 存在字段差异: ['is_new_store_diff']
2025-08-08 09:00:23,103 - INFO - 店铺 100099603 存在字段差异: ['is_new_store_diff']
2025-08-08 09:00:23,103 - INFO - 数据对比完成：
2025-08-08 09:00:23,103 - INFO - - 需要插入的记录数: 2
2025-08-08 09:00:23,103 - INFO - - 需要更新状态为禁用的记录数: 0
2025-08-08 09:00:23,103 - INFO - - 需要更新的记录数: 18
2025-08-08 09:00:23,103 - INFO - - 店铺名称变更数: 1
2025-08-08 09:00:23,103 - INFO - 开始处理店铺名称变更 - 店铺编码: *********, 原名称: -多经 鳄鱼恤/途袖, 新名称: 多经-鳄鱼恤/运动町
2025-08-08 09:00:23,103 - INFO - 查询第一个宜搭表单 - 店铺编码: *********
2025-08-08 09:00:23,103 - INFO - Request Parameters - Page 1:
2025-08-08 09:00:23,103 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-08 09:00:23,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9nw1k6y", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-08 09:00:31,228 - ERROR - 更新第一个宜搭表单失败 - 店铺编码: *********, 错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7F1DA0DA-BCFC-701E-AE5F-A13F047F9452 Response: {'code': 'ServiceUnavailable', 'requestid': '7F1DA0DA-BCFC-701E-AE5F-A13F047F9452', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7F1DA0DA-BCFC-701E-AE5F-A13F047F9452)
2025-08-08 09:00:31,228 - INFO - 查询第二个宜搭表单 - 店铺编码: *********
2025-08-08 09:00:31,228 - INFO - Request Parameters - Page 1:
2025-08-08 09:00:31,228 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-08 09:00:31,228 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9tojheq", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-08 09:00:31,525 - INFO - Response - Page 1:
2025-08-08 09:00:31,525 - INFO - 第 1 页获取到 7 条记录
2025-08-08 09:00:31,728 - INFO - 查询完成，共获取到 7 条记录
2025-08-08 09:00:31,728 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2PF66CD1DDSXR1BYDOGY6DO6KCV43K7NS11EMH7
2025-08-08 09:00:32,150 - INFO - 更新表单数据成功: FINST-2PF66CD1DDSXR1BYDOGY6DO6KCV43K7NS11EMH7
2025-08-08 09:00:32,150 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:32,150 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-CPC66T91FC6XVNXDDNPNI8FLKDX52COR3M5DMB
2025-08-08 09:00:32,634 - INFO - 更新表单数据成功: FINST-CPC66T91FC6XVNXDDNPNI8FLKDX52COR3M5DMB
2025-08-08 09:00:32,634 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:32,634 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-EZD66RB1PWEW2UPXA0RCXBRSWHV23GL87S1CMA
2025-08-08 09:00:33,087 - INFO - 更新表单数据成功: FINST-EZD66RB1PWEW2UPXA0RCXBRSWHV23GL87S1CMA
2025-08-08 09:00:33,087 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:33,087 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-OLF66Q71B45WSIGSBDE1VBTQUAH13U9MMTOBMA6
2025-08-08 09:00:33,540 - INFO - 更新表单数据成功: FINST-OLF66Q71B45WSIGSBDE1VBTQUAH13U9MMTOBMA6
2025-08-08 09:00:33,540 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:33,540 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-K7666JC1NN5WAPGF6APOJ9Y6DQKB2V84MTOBMF1
2025-08-08 09:00:33,978 - INFO - 更新表单数据成功: FINST-K7666JC1NN5WAPGF6APOJ9Y6DQKB2V84MTOBMF1
2025-08-08 09:00:33,978 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:33,978 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-737662B1HT1WY3QHEJJUR994FH812T4MLTOBMJ71
2025-08-08 09:00:34,587 - INFO - 更新表单数据成功: FINST-737662B1HT1WY3QHEJJUR994FH812T4MLTOBMJ71
2025-08-08 09:00:34,587 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:34,587 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-U8966871X82WTGW77WGG05183OGU2PP3LTOBM8C
2025-08-08 09:00:34,994 - INFO - 更新表单数据成功: FINST-U8966871X82WTGW77WGG05183OGU2PP3LTOBM8C
2025-08-08 09:00:34,994 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:34,994 - INFO - 店铺名称变更处理完成 - 店铺编码: *********
2025-08-08 09:00:35,915 - INFO - 邮件发送成功
2025-08-08 09:00:35,915 - INFO - 店铺名称更新邮件发送成功，共 1 条记录
2025-08-08 09:00:35,915 - INFO - 生成差异报告...
2025-08-08 09:00:36,212 - INFO - 差异报告已保存到文件: data/sync_store/store_info_diff_report_20250808.xlsx
2025-08-08 09:00:36,212 - INFO - 开始更新宜搭表单...
2025-08-08 09:00:36,212 - INFO - 开始更新宜搭表单数据...
2025-08-08 09:00:36,212 - INFO - 数据库记录数: 1296
2025-08-08 09:00:36,212 - INFO - 宜搭记录数: 1294
2025-08-08 09:00:36,212 - INFO - 数据库数据框列名: ['project_code', 'project_name', 'store_code', 'store_name', 'store_status', 'oa_account', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'ifduojing', 'userid', 'is_new_store']
2025-08-08 09:00:36,212 - INFO - 宜搭数据框列名: ['store_code', 'store_name', 'userid', 'status', 'is_duojing', 'jde_customer_code', 'jde_customer_name', 'fz_store_code', 'is_new_store', 'form_instance_id']
2025-08-08 09:00:36,212 - INFO - 仅在数据库存在的记录数: 2
2025-08-08 09:00:36,212 - INFO - 需要插入的记录: ['*********', '*********']
2025-08-08 09:00:36,212 - INFO - 仅在宜搭存在的记录数: 0
2025-08-08 09:00:36,228 - INFO - 店铺 ********* 存在字段差异: ['fz_store_code_diff']
2025-08-08 09:00:36,259 - INFO - 店铺 ********* 存在字段差异: ['status_diff']
2025-08-08 09:00:36,306 - INFO - 检测到店铺名称变更 - 店铺编码: *********, 原名称: -多经 鳄鱼恤/途袖, 新名称: 多经-鳄鱼恤/运动町
2025-08-08 09:00:36,306 - INFO - 店铺 ********* 存在字段差异: ['store_name_diff', 'status_diff']
2025-08-08 09:00:36,384 - INFO - 店铺 100101442 存在字段差异: ['is_new_store_diff']
2025-08-08 09:00:36,415 - INFO - 店铺 100100322 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,415 - INFO - 店铺 100100322 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:36,415 - INFO - 店铺 100100322 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,415 - INFO - 店铺 100100322 - 仅在宜搭存在的userid: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:36,415 - INFO - 店铺 100100362 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,415 - INFO - 店铺 100100362 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,415 - INFO - 店铺 100100362 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,415 - INFO - 店铺 100100362 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,415 - INFO - 店铺 100100323 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,415 - INFO - 店铺 100100323 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,415 - INFO - 店铺 100100323 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,415 - INFO - 店铺 100100323 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,431 - INFO - 店铺 100100360 存在字段差异: ['status_diff']
2025-08-08 09:00:36,431 - INFO - 店铺 100100316 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,431 - INFO - 店铺 100100316 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:36,431 - INFO - 店铺 100100316 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,431 - INFO - 店铺 100100316 - 仅在宜搭存在的userid: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:36,431 - INFO - 店铺 100101130 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,431 - INFO - 店铺 100101130 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:36,431 - INFO - 店铺 100101130 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,431 - INFO - 店铺 100101130 - 仅在宜搭存在的userid: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:36,431 - INFO - 店铺 100101108 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,431 - INFO - 店铺 100101108 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,431 - INFO - 店铺 100101108 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,431 - INFO - 店铺 100101108 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100248 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,447 - INFO - 店铺 100100248 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100248 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100248 - 仅在宜搭存在的userid: {'1971b2e69fe08b1dcd93ff64cc28d918'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100355 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,447 - INFO - 店铺 100100355 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100355 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100355 - 仅在宜搭存在的userid: {'1911111840fdac3db9f89204e6eb4322'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100340 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,447 - INFO - 店铺 100100340 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100340 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100340 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100298 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,447 - INFO - 店铺 100100298 userid差异 - 数据库: {'18abb9e21af4e7870dbb8b64940a04ec'}, 宜搭: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100298 - 仅在数据库存在的userid: {'18abb9e21af4e7870dbb8b64940a04ec'}
2025-08-08 09:00:36,447 - INFO - 店铺 100100298 - 仅在宜搭存在的userid: {'174c4fe5c5853e3c68a4e0347338a136'}
2025-08-08 09:00:36,462 - INFO - 店铺 100101262 存在字段差异: ['userid_diff']
2025-08-08 09:00:36,462 - INFO - 店铺 100101262 userid差异 - 数据库: {'1977cc9e77b959503f06ffc47f2b250c'}, 宜搭: {'1911111840fdac3db9f89204e6eb4322'}
2025-08-08 09:00:36,462 - INFO - 店铺 100101262 - 仅在数据库存在的userid: {'1977cc9e77b959503f06ffc47f2b250c'}
2025-08-08 09:00:36,462 - INFO - 店铺 100101262 - 仅在宜搭存在的userid: {'1911111840fdac3db9f89204e6eb4322'}
2025-08-08 09:00:36,509 - INFO - 店铺 100101443 存在字段差异: ['is_new_store_diff']
2025-08-08 09:00:36,509 - INFO - 店铺 100099603 存在字段差异: ['is_new_store_diff']
2025-08-08 09:00:36,509 - INFO - 数据对比完成：
2025-08-08 09:00:36,509 - INFO - - 需要插入的记录数: 2
2025-08-08 09:00:36,509 - INFO - - 需要更新状态为禁用的记录数: 0
2025-08-08 09:00:36,509 - INFO - - 需要更新的记录数: 18
2025-08-08 09:00:36,509 - INFO - - 店铺名称变更数: 1
2025-08-08 09:00:36,509 - INFO - 开始处理店铺名称变更 - 店铺编码: *********, 原名称: -多经 鳄鱼恤/途袖, 新名称: 多经-鳄鱼恤/运动町
2025-08-08 09:00:36,509 - INFO - 查询第一个宜搭表单 - 店铺编码: *********
2025-08-08 09:00:36,509 - INFO - Request Parameters - Page 1:
2025-08-08 09:00:36,509 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-08 09:00:36,509 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9nw1k6y", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-08 09:00:44,618 - ERROR - 更新第一个宜搭表单失败 - 店铺编码: *********, 错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CDADE895-916C-763A-8D9C-6D7797DBEEDD Response: {'code': 'ServiceUnavailable', 'requestid': 'CDADE895-916C-763A-8D9C-6D7797DBEEDD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CDADE895-916C-763A-8D9C-6D7797DBEEDD)
2025-08-08 09:00:44,618 - INFO - 查询第二个宜搭表单 - 店铺编码: *********
2025-08-08 09:00:44,618 - INFO - Request Parameters - Page 1:
2025-08-08 09:00:44,618 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-08 09:00:44,618 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "textField_m9tojheq", "value": "*********", "type": "TEXT", "operator": "eq", "componentName": "TextField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-08 09:00:44,915 - INFO - Response - Page 1:
2025-08-08 09:00:44,915 - INFO - 第 1 页获取到 7 条记录
2025-08-08 09:00:45,118 - INFO - 查询完成，共获取到 7 条记录
2025-08-08 09:00:45,118 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-2PF66CD1DDSXR1BYDOGY6DO6KCV43K7NS11EMH7
2025-08-08 09:00:45,572 - INFO - 更新表单数据成功: FINST-2PF66CD1DDSXR1BYDOGY6DO6KCV43K7NS11EMH7
2025-08-08 09:00:45,572 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:45,572 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-CPC66T91FC6XVNXDDNPNI8FLKDX52COR3M5DMB
2025-08-08 09:00:46,009 - INFO - 更新表单数据成功: FINST-CPC66T91FC6XVNXDDNPNI8FLKDX52COR3M5DMB
2025-08-08 09:00:46,009 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:46,009 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-EZD66RB1PWEW2UPXA0RCXBRSWHV23GL87S1CMA
2025-08-08 09:00:46,478 - INFO - 更新表单数据成功: FINST-EZD66RB1PWEW2UPXA0RCXBRSWHV23GL87S1CMA
2025-08-08 09:00:46,478 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:46,478 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-OLF66Q71B45WSIGSBDE1VBTQUAH13U9MMTOBMA6
2025-08-08 09:00:46,962 - INFO - 更新表单数据成功: FINST-OLF66Q71B45WSIGSBDE1VBTQUAH13U9MMTOBMA6
2025-08-08 09:00:46,962 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:46,962 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-K7666JC1NN5WAPGF6APOJ9Y6DQKB2V84MTOBMF1
2025-08-08 09:00:47,493 - INFO - 更新表单数据成功: FINST-K7666JC1NN5WAPGF6APOJ9Y6DQKB2V84MTOBMF1
2025-08-08 09:00:47,493 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:47,493 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-737662B1HT1WY3QHEJJUR994FH812T4MLTOBMJ71
2025-08-08 09:00:47,947 - INFO - 更新表单数据成功: FINST-737662B1HT1WY3QHEJJUR994FH812T4MLTOBMJ71
2025-08-08 09:00:47,947 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:47,947 - INFO - 更新第二个宜搭表单 - 店铺编码: *********, 表单ID: FINST-U8966871X82WTGW77WGG05183OGU2PP3LTOBM8C
2025-08-08 09:00:48,447 - INFO - 更新表单数据成功: FINST-U8966871X82WTGW77WGG05183OGU2PP3LTOBM8C
2025-08-08 09:00:48,447 - INFO - 更新第二个宜搭表单成功 - 店铺编码: *********
2025-08-08 09:00:48,447 - INFO - 店铺名称变更处理完成 - 店铺编码: *********
2025-08-08 09:00:49,228 - INFO - 邮件发送成功
2025-08-08 09:00:49,228 - INFO - 店铺名称更新邮件发送成功，共 1 条记录
2025-08-08 09:00:49,228 - INFO - 开始处理需要插入的记录，共 2 条
2025-08-08 09:00:49,228 - INFO - 正在处理第 1 条插入记录 - store_code: *********
2025-08-08 09:00:49,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "多经-美旅", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L25CO0103", "textField_m9jkl9nx": "是"}
2025-08-08 09:00:49,228 - INFO - 正在处理第 2 条插入记录 - store_code: *********
2025-08-08 09:00:49,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州IFC国金天地", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "奈雪の茶", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10164L25CO0029", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:49,228 - INFO - 处理剩余 2 条插入记录
2025-08-08 09:00:49,400 - INFO - 批量创建表单数据成功: 2 条记录
2025-08-08 09:00:49,400 - INFO - 批量插入成功，form_instance_ids: ['FINST-YFF667C1CDUXR5238GIG6DC1VYUS2DQMD42EM5', 'FINST-YFF667C1CDUXR5238GIG6DC1VYUS2DQMD42EM6']
2025-08-08 09:00:49,400 - INFO - 发送新店铺插入邮件通知
2025-08-08 09:00:50,228 - INFO - 邮件发送成功
2025-08-08 09:00:50,228 - INFO - 新店铺插入邮件发送成功，共 2 条记录
2025-08-08 09:00:50,228 - INFO - 开始处理需要更新的记录，共 18 条
2025-08-08 09:00:50,228 - INFO - 正在处理第 1 条更新记录 - store_code: *********
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "龙井湖", "employeeField_m8e8g3lw": ["195cbfaa191990374b814d747ed92fe3", "178911c91380e3f9961bcc84847a615f", "18b0e1e4b78fd2c55dc90c74e3b9f3cc", "189f858c407c7b3d480039a4090b4c13", "18f13290b6949adbfcd0691437491a5a"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "103741", "textField_mb7rs39i": "广州市完美餐饮管理有限公司", "textField_mbc1lbzm": "P0299L25CO0102", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 2 条更新记录 - store_code: *********
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "富莉富蕾", "employeeField_m8e8g3lw": ["197e4171b5a83afebf4e3184f3fa9e6f", "18063f3df478239384b92a7416a8207f", "1823db918644667677cfbe44476b7b9d", "16340e95afb25e94fcd338840d78edb8"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111198", "textField_mb7rs39i": "广州千与千寻电子商务贸易有限公司", "textField_mbc1lbzm": "P0299L24CO0220", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 3 条更新记录 - store_code: *********
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "广州悦汇城", "textField_m911r3pn": "*********", "textField_m8e8g3lu": "多经-鳄鱼恤/运动町", "employeeField_m8e8g3lw": [], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "是", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0299L24CO0183", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 4 条更新记录 - store_code: 100101442
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉国金天地", "textField_m911r3pn": "100101442", "textField_m8e8g3lu": "趴啦", "employeeField_m8e8g3lw": ["189fd3054a18e03a44b872b495795896", "18acf80e2a353b81652ebdc4329bdcc4", "1835384a550d8f5bfad8b704c9e963f7", "18353820b37845b95ed69864fd58b885", "1847a0e687fd986b4b870904dea8430d", "16d2416ce06396ed3f55a964f9fa0c71", "18b6ef624129a1e2c9b51774b6e8ce8c"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "P0003L25CO0041", "textField_m9jkl9nx": "是"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 5 条更新记录 - store_code: 100100322
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100322", "textField_m8e8g3lu": "汉堡王", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110666", "textField_mb7rs39i": "汉堡王食品（深圳）有限公司", "textField_mbc1lbzm": "10295L23CO0058", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 6 条更新记录 - store_code: 100100362
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100362", "textField_m8e8g3lu": "拉茶王子", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111097", "textField_mb7rs39i": "佛山市拉茶王子餐饮有限公司", "textField_mbc1lbzm": "10295L24CO0005", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 7 条更新记录 - store_code: 100100323
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100323", "textField_m8e8g3lu": "奈雪の茶", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "104055", "textField_mb7rs39i": "广州市奈雪餐饮管理有限公司", "textField_mbc1lbzm": "10295L23CO0057", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 8 条更新记录 - store_code: 100100360
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100360", "textField_m8e8g3lu": "优剪", "employeeField_m8e8g3lw": ["1977cc9e77b959503f06ffc47f2b250c"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110904", "textField_mb7rs39i": "何志勇", "textField_mbc1lbzm": "10295L25CO0008", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 9 条更新记录 - store_code: 100100316
2025-08-08 09:00:50,228 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100316", "textField_m8e8g3lu": "名品乐淘", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110828", "textField_mb7rs39i": "吴志辉", "textField_mbc1lbzm": "10295L25CO0015", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,228 - INFO - 正在处理第 10 条更新记录 - store_code: 100101130
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101130", "textField_m8e8g3lu": "大可以", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111535", "textField_mb7rs39i": "佛山市南海区粤汇大可以餐饮店(个体工商户)", "textField_mbc1lbzm": "10295L24CO0121", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,243 - INFO - 正在处理第 11 条更新记录 - store_code: 100101108
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101108", "textField_m8e8g3lu": "OPPO", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111428", "textField_mb7rs39i": "范乃昌", "textField_mbc1lbzm": "10295L24CO0042", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,243 - INFO - 正在处理第 12 条更新记录 - store_code: 100100248
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100248", "textField_m8e8g3lu": "普莱斯眼镜", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110606", "textField_mb7rs39i": "查岚", "textField_mbc1lbzm": "10295L25CO0014", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,243 - INFO - 正在处理第 13 条更新记录 - store_code: 100100355
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100355", "textField_m8e8g3lu": "春天舞蹈", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110605", "textField_mb7rs39i": "陈廷东", "textField_mbc1lbzm": "10295L24CO0030", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,243 - INFO - 正在处理第 14 条更新记录 - store_code: 100100340
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100340", "textField_m8e8g3lu": "名创优品", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110395", "textField_mb7rs39i": "吴永生", "textField_mbc1lbzm": "10295L23CO0064", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,243 - INFO - 正在处理第 15 条更新记录 - store_code: 100100298
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100100298", "textField_m8e8g3lu": "苹果数码", "employeeField_m8e8g3lw": ["18abb9e21af4e7870dbb8b64940a04ec"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "110969", "textField_mb7rs39i": "佛山尚派正品科技有限公司", "textField_mbc1lbzm": "10295L23CO0076", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,243 - INFO - 正在处理第 16 条更新记录 - store_code: 100101262
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "悦汇广场·南海", "textField_m911r3pn": "100101262", "textField_m8e8g3lu": "极客玩家", "employeeField_m8e8g3lw": ["1977cc9e77b959503f06ffc47f2b250c"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "111557", "textField_mb7rs39i": "翟文晓", "textField_mbc1lbzm": "10295L25CO0006", "textField_m9jkl9nx": "否"}
2025-08-08 09:00:50,243 - INFO - 正在处理第 17 条更新记录 - store_code: 100101443
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100101443", "textField_m8e8g3lu": "吉野家", "employeeField_m8e8g3lw": ["16d2416e996837cef3e7d79485192fc3"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10630L25CO0030", "textField_m9jkl9nx": "是"}
2025-08-08 09:00:50,243 - INFO - 正在处理第 18 条更新记录 - store_code: 100099603
2025-08-08 09:00:50,243 - INFO - 转换后的数据: {"selectField_m886ps6o": "武汉星汇维港", "textField_m911r3pn": "100099603", "textField_m8e8g3lu": "对位", "employeeField_m8e8g3lw": ["18210ad76a4040e7991eed544c0ade38"], "textField_m8e8g3lx": "正常", "textField_ma0r7wp6": "否", "textField_m8e8g3lv": "", "textField_mb7rs39i": "", "textField_mbc1lbzm": "10630L25CO0029", "textField_m9jkl9nx": "是"}
2025-08-08 09:00:50,243 - INFO - 处理剩余 18 条更新记录
2025-08-08 09:00:50,868 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:51,447 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:52,290 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:52,837 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:53,431 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:54,087 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:54,603 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:55,228 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:55,947 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:56,603 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:57,228 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:57,837 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:58,431 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:59,113 - INFO - 批量更新表单数据成功: 
2025-08-08 09:00:59,785 - INFO - 批量更新表单数据成功: 
2025-08-08 09:01:00,395 - INFO - 批量更新表单数据成功: 
2025-08-08 09:01:01,145 - INFO - 批量更新表单数据成功: 
2025-08-08 09:01:01,645 - INFO - 批量更新表单数据成功: 
2025-08-08 09:01:01,645 - INFO - 批量更新成功，form_instance_ids: ['FINST-49866E71HEVVLI2WBRBVL4QQNEJL3E7HADCBMZA', 'FINST-EZD66RB1CEVVCPYS9GXGB7JWSPZE2OXHADCBMM5', 'FINST-W4G66DA138WVZ3OJDIAET4X9HGRA2TNIADCBMI3', 'FINST-NS766991CSSXUWT460ISP4776HDX3554XO0EMU', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2GDJADCBMS6', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM47', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBM67', 'FINST-MLF669B1QFUVZA99CHXIX8DA0AEZ2HDJADCBMV7', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBM84', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMO4', 'FINST-W3B66L71IAVVER4DFU5K6A4SM6YW35KJADCBMR4', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMF6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMM6', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMH7', 'FINST-VOC66Y91MGVV6QK97GF0OA6SZAMT3NQJADCBMJ7', 'FINST-PGC66MB1OHRV8LM4D56JSBRGO1G0384KADCBMOI', 'FINST-NS766991CSSXUWT460ISP4776HDX3554XO0EMV', 'FINST-NS766991CSSXUWT460ISP4776HDX3554XO0EMW']
2025-08-08 09:01:01,645 - INFO - 宜搭表单更新完成
2025-08-08 09:01:01,645 - INFO - 数据处理完成
2025-08-08 09:01:01,645 - INFO - 数据库连接已关闭
