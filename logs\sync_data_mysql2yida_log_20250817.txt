2025-08-17 01:30:33,744 - INFO - 使用默认增量同步（当天更新数据）
2025-08-17 01:30:33,744 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-17 01:30:33,744 - INFO - 查询参数: ('2025-08-17',)
2025-08-17 01:30:33,916 - INFO - MySQL查询成功，增量数据（日期: 2025-08-17），共获取 1 条记录
2025-08-17 01:30:33,916 - INFO - 获取到 1 个日期需要处理: ['2025-08-16']
2025-08-17 01:30:33,916 - INFO - 开始处理日期: 2025-08-16
2025-08-17 01:30:33,916 - INFO - Request Parameters - Page 1:
2025-08-17 01:30:33,916 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 01:30:33,916 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 01:30:42,042 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CBBC14D0-F8B7-7358-994E-833C88CE4C90 Response: {'code': 'ServiceUnavailable', 'requestid': 'CBBC14D0-F8B7-7358-994E-833C88CE4C90', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CBBC14D0-F8B7-7358-994E-833C88CE4C90)
2025-08-17 01:30:42,042 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-17 01:31:42,057 - INFO - 开始同步昨天与今天的销售数据: 2025-08-16 至 2025-08-17
2025-08-17 01:31:42,057 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-17 01:31:42,057 - INFO - 查询参数: ('2025-08-16', '2025-08-17')
2025-08-17 01:31:42,229 - INFO - MySQL查询成功，时间段: 2025-08-16 至 2025-08-17，共获取 105 条记录
2025-08-17 01:31:42,229 - INFO - 获取到 1 个日期需要处理: ['2025-08-16']
2025-08-17 01:31:42,229 - INFO - 开始处理日期: 2025-08-16
2025-08-17 01:31:42,229 - INFO - Request Parameters - Page 1:
2025-08-17 01:31:42,229 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 01:31:42,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 01:31:50,323 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C0BFAFFA-6A20-7C5C-812D-FD6E8B647C2D Response: {'code': 'ServiceUnavailable', 'requestid': 'C0BFAFFA-6A20-7C5C-812D-FD6E8B647C2D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C0BFAFFA-6A20-7C5C-812D-FD6E8B647C2D)
2025-08-17 01:31:50,323 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-17 01:31:50,323 - INFO - 同步完成
2025-08-17 04:30:33,707 - INFO - 使用默认增量同步（当天更新数据）
2025-08-17 04:30:33,707 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-17 04:30:33,707 - INFO - 查询参数: ('2025-08-17',)
2025-08-17 04:30:33,878 - INFO - MySQL查询成功，增量数据（日期: 2025-08-17），共获取 1 条记录
2025-08-17 04:30:33,878 - INFO - 获取到 1 个日期需要处理: ['2025-08-16']
2025-08-17 04:30:33,878 - INFO - 开始处理日期: 2025-08-16
2025-08-17 04:30:33,894 - INFO - Request Parameters - Page 1:
2025-08-17 04:30:33,894 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 04:30:33,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 04:30:42,030 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C00FB0A0-8D90-747B-876F-8320CDAE0A8F Response: {'code': 'ServiceUnavailable', 'requestid': 'C00FB0A0-8D90-747B-876F-8320CDAE0A8F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C00FB0A0-8D90-747B-876F-8320CDAE0A8F)
2025-08-17 04:30:42,030 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-17 04:31:42,045 - INFO - 开始同步昨天与今天的销售数据: 2025-08-16 至 2025-08-17
2025-08-17 04:31:42,045 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-17 04:31:42,045 - INFO - 查询参数: ('2025-08-16', '2025-08-17')
2025-08-17 04:31:42,201 - INFO - MySQL查询成功，时间段: 2025-08-16 至 2025-08-17，共获取 105 条记录
2025-08-17 04:31:42,201 - INFO - 获取到 1 个日期需要处理: ['2025-08-16']
2025-08-17 04:31:42,201 - INFO - 开始处理日期: 2025-08-16
2025-08-17 04:31:42,201 - INFO - Request Parameters - Page 1:
2025-08-17 04:31:42,201 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 04:31:42,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 04:31:43,061 - INFO - Response - Page 1:
2025-08-17 04:31:43,061 - INFO - 第 1 页获取到 50 条记录
2025-08-17 04:31:43,576 - INFO - Request Parameters - Page 2:
2025-08-17 04:31:43,576 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 04:31:43,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 04:31:51,686 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 718D9AA5-EEF2-7024-927F-70A2F7C4D730 Response: {'code': 'ServiceUnavailable', 'requestid': '718D9AA5-EEF2-7024-927F-70A2F7C4D730', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 718D9AA5-EEF2-7024-927F-70A2F7C4D730)
2025-08-17 04:31:51,686 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-17 04:31:51,686 - INFO - 同步完成
2025-08-17 07:30:33,895 - INFO - 使用默认增量同步（当天更新数据）
2025-08-17 07:30:33,895 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-17 07:30:33,895 - INFO - 查询参数: ('2025-08-17',)
2025-08-17 07:30:34,051 - INFO - MySQL查询成功，增量数据（日期: 2025-08-17），共获取 1 条记录
2025-08-17 07:30:34,051 - INFO - 获取到 1 个日期需要处理: ['2025-08-16']
2025-08-17 07:30:34,051 - INFO - 开始处理日期: 2025-08-16
2025-08-17 07:30:34,067 - INFO - Request Parameters - Page 1:
2025-08-17 07:30:34,067 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 07:30:34,067 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 07:30:42,176 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1E153DFC-D671-7131-8AC7-54A2447D8184 Response: {'code': 'ServiceUnavailable', 'requestid': '1E153DFC-D671-7131-8AC7-54A2447D8184', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1E153DFC-D671-7131-8AC7-54A2447D8184)
2025-08-17 07:30:42,176 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-17 07:31:42,186 - INFO - 开始同步昨天与今天的销售数据: 2025-08-16 至 2025-08-17
2025-08-17 07:31:42,186 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-17 07:31:42,186 - INFO - 查询参数: ('2025-08-16', '2025-08-17')
2025-08-17 07:31:42,343 - INFO - MySQL查询成功，时间段: 2025-08-16 至 2025-08-17，共获取 105 条记录
2025-08-17 07:31:42,343 - INFO - 获取到 1 个日期需要处理: ['2025-08-16']
2025-08-17 07:31:42,343 - INFO - 开始处理日期: 2025-08-16
2025-08-17 07:31:42,343 - INFO - Request Parameters - Page 1:
2025-08-17 07:31:42,343 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 07:31:42,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 07:31:50,468 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BD731D6C-9D7B-70BF-B13E-E7ED65066A08 Response: {'code': 'ServiceUnavailable', 'requestid': 'BD731D6C-9D7B-70BF-B13E-E7ED65066A08', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BD731D6C-9D7B-70BF-B13E-E7ED65066A08)
2025-08-17 07:31:50,468 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-17 07:31:50,468 - INFO - 同步完成
2025-08-17 10:30:33,617 - INFO - 使用默认增量同步（当天更新数据）
2025-08-17 10:30:33,617 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-17 10:30:33,617 - INFO - 查询参数: ('2025-08-17',)
2025-08-17 10:30:33,789 - INFO - MySQL查询成功，增量数据（日期: 2025-08-17），共获取 141 条记录
2025-08-17 10:30:33,789 - INFO - 获取到 2 个日期需要处理: ['2025-08-16', '2025-08-17']
2025-08-17 10:30:33,789 - INFO - 开始处理日期: 2025-08-16
2025-08-17 10:30:33,805 - INFO - Request Parameters - Page 1:
2025-08-17 10:30:33,805 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 10:30:33,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 10:30:41,925 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AF3BAC2C-286A-7950-ABDF-D2B6DBC86293 Response: {'code': 'ServiceUnavailable', 'requestid': 'AF3BAC2C-286A-7950-ABDF-D2B6DBC86293', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AF3BAC2C-286A-7950-ABDF-D2B6DBC86293)
2025-08-17 10:30:41,941 - INFO - 开始处理日期: 2025-08-17
2025-08-17 10:30:41,941 - INFO - Request Parameters - Page 1:
2025-08-17 10:30:41,941 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 10:30:41,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 10:30:47,019 - INFO - Response - Page 1:
2025-08-17 10:30:47,019 - INFO - 查询完成，共获取到 0 条记录
2025-08-17 10:30:47,019 - INFO - 获取到 0 条表单数据
2025-08-17 10:30:47,019 - INFO - 当前日期 2025-08-17 有 3 条MySQL数据需要处理
2025-08-17 10:30:47,019 - INFO - 开始批量插入 3 条新记录
2025-08-17 10:30:47,175 - INFO - 批量插入响应状态码: 200
2025-08-17 10:30:47,175 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:30:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '82C22BDB-0D1E-7F3A-87A2-CAB8B63CD4BE', 'x-acs-trace-id': 'dff8834b3ebfe2dbfa22078ff8282d32', 'etag': '1XBl5YkhE+EqP37MTEdhXIQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:30:47,175 - INFO - 批量插入响应体: {'result': ['FINST-SE766YC1VS2Y0F0D8KU4Q6DKKSOP3KH4K2FEMM1', 'FINST-SE766YC1VS2Y0F0D8KU4Q6DKKSOP3KH4K2FEMN1', 'FINST-SE766YC1VS2Y0F0D8KU4Q6DKKSOP3KH4K2FEMO1']}
2025-08-17 10:30:47,175 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-17 10:30:47,175 - INFO - 成功插入的数据ID: ['FINST-SE766YC1VS2Y0F0D8KU4Q6DKKSOP3KH4K2FEMM1', 'FINST-SE766YC1VS2Y0F0D8KU4Q6DKKSOP3KH4K2FEMN1', 'FINST-SE766YC1VS2Y0F0D8KU4Q6DKKSOP3KH4K2FEMO1']
2025-08-17 10:30:52,191 - INFO - 批量插入完成，共 3 条记录
2025-08-17 10:30:52,191 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-08-17 10:30:52,191 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 1 条
2025-08-17 10:31:52,201 - INFO - 开始同步昨天与今天的销售数据: 2025-08-16 至 2025-08-17
2025-08-17 10:31:52,201 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-17 10:31:52,201 - INFO - 查询参数: ('2025-08-16', '2025-08-17')
2025-08-17 10:31:52,373 - INFO - MySQL查询成功，时间段: 2025-08-16 至 2025-08-17，共获取 442 条记录
2025-08-17 10:31:52,373 - INFO - 获取到 2 个日期需要处理: ['2025-08-16', '2025-08-17']
2025-08-17 10:31:52,373 - INFO - 开始处理日期: 2025-08-16
2025-08-17 10:31:52,373 - INFO - Request Parameters - Page 1:
2025-08-17 10:31:52,373 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 10:31:52,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 10:31:53,217 - INFO - Response - Page 1:
2025-08-17 10:31:53,217 - INFO - 第 1 页获取到 50 条记录
2025-08-17 10:31:53,732 - INFO - Request Parameters - Page 2:
2025-08-17 10:31:53,732 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 10:31:53,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 10:31:54,232 - INFO - Response - Page 2:
2025-08-17 10:31:54,232 - INFO - 查询完成，共获取到 50 条记录
2025-08-17 10:31:54,232 - INFO - 获取到 50 条表单数据
2025-08-17 10:31:54,248 - INFO - 当前日期 2025-08-16 有 425 条MySQL数据需要处理
2025-08-17 10:31:54,248 - INFO - 开始批量插入 375 条新记录
2025-08-17 10:31:54,529 - INFO - 批量插入响应状态码: 200
2025-08-17 10:31:54,529 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:32:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4362E151-C547-70E3-8262-7D10F37D8969', 'x-acs-trace-id': 'fda860674eeb379939f012d4c3e7aa0c', 'etag': '2vKHM4wY141PzS94la5cZwQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:31:54,529 - INFO - 批量插入响应体: {'result': ['FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMFH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMGH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMHH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMIH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMJH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMKH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMLH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMMH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMNH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMOH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMPH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMQH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMRH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMSH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMTH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMUH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMVH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMWH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMXH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMYH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMZH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM0I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM1I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM2I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM3I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM4I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM5I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM6I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM7I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM8I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM9I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMAI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMBI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMCI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMDI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMEI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMFI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMGI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMHI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMII', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMJI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMKI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMLI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMMI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMNI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMOI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMPI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMQI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMRI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMSI']}
2025-08-17 10:31:54,529 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-17 10:31:54,529 - INFO - 成功插入的数据ID: ['FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMFH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMGH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMHH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMIH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMJH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMKH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMLH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMMH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMNH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMOH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMPH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMQH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMRH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMSH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMTH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMUH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMVH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMWH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMXH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMYH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMZH', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM0I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM1I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM2I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM3I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM4I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM5I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM6I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM7I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM8I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEM9I', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMAI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMBI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMCI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMDI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMEI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMFI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMGI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMHI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMII', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMJI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMKI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMLI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMMI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMNI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMOI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMPI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMQI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMRI', 'FINST-NS766991RJ2Y3J7R6KBML52NF0IZ2DGKL2FEMSI']
2025-08-17 10:31:59,779 - INFO - 批量插入响应状态码: 200
2025-08-17 10:31:59,779 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '016E27B3-F8B5-7B86-93D0-E31BC1F3D069', 'x-acs-trace-id': 'e24ee2bea477d48dfc138dc9a32e2775', 'etag': '2146AW3hO0TahUoWXFcEMlQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:31:59,779 - INFO - 批量插入响应体: {'result': ['FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM0H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM1H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM2H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM3H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM4H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM5H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM6H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM7H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM8H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM9H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMAH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMBH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMCH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMDH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMEH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMFH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMGH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMHH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMIH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMJH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMKH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMLH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMMH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMNH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMOH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMPH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMQH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMRH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMSH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMTH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMUH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMVH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMWH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMXH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMYH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMZH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM0I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM1I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM2I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM3I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM4I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM5I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM6I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM7I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM8I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM9I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMAI', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMBI', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMCI', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMDI']}
2025-08-17 10:31:59,779 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-17 10:31:59,779 - INFO - 成功插入的数据ID: ['FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM0H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM1H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM2H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM3H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM4H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM5H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM6H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM7H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM8H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM9H', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMAH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMBH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMCH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMDH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMEH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMFH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMGH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMHH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMIH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMJH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMKH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMLH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMMH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMNH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMOH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMPH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMQH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMRH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMSH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMTH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMUH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMVH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMWH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMXH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMYH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMZH', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM0I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM1I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM2I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM3I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM4I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM5I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM6I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM7I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM8I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEM9I', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMAI', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMBI', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMCI', 'FINST-NYC66LB1WL1YJ8Y58O7BVBHCG06Z1HIOL2FEMDI']
2025-08-17 10:32:05,029 - INFO - 批量插入响应状态码: 200
2025-08-17 10:32:05,029 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2E55773D-79DE-72D4-83A9-98154A87AE32', 'x-acs-trace-id': '989dea8da8a77a554c2a0f25e5715b1a', 'etag': '22Wa354l3m8X3Br4kRvjtwQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:32:05,029 - INFO - 批量插入响应体: {'result': ['FINST-LLF66J71GY1Y3M777L99P836WYRW27KSL2FEMUG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW27KSL2FEMVG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW27KSL2FEMWG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW27KSL2FEMXG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMYG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMZG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM0H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM1H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM2H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM3H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM4H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM5H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM6H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM7H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM8H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM9H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMAH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMBH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMCH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMDH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMEH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMFH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMGH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMHH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMIH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMJH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMKH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMLH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMMH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMNH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMOH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMPH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMQH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMRH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMSH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMTH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMUH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMVH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMWH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMXH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMYH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMZH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM0I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM1I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM2I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM3I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM4I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM5I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM6I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM7I']}
2025-08-17 10:32:05,029 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-17 10:32:05,029 - INFO - 成功插入的数据ID: ['FINST-LLF66J71GY1Y3M777L99P836WYRW27KSL2FEMUG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW27KSL2FEMVG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW27KSL2FEMWG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW27KSL2FEMXG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMYG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMZG', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM0H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM1H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM2H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM3H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM4H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM5H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM6H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM7H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM8H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM9H', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMAH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMBH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMCH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMDH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMEH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMFH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMGH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMHH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMIH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMJH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMKH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMLH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMMH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMNH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMOH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMPH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMQH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMRH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMSH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMTH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMUH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMVH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMWH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMXH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMYH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEMZH', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM0I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM1I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM2I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM3I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM4I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM5I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM6I', 'FINST-LLF66J71GY1Y3M777L99P836WYRW28KSL2FEM7I']
2025-08-17 10:32:10,279 - INFO - 批量插入响应状态码: 200
2025-08-17 10:32:10,279 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '09AA48E9-E7F7-78B1-B7F1-A65868268D3B', 'x-acs-trace-id': '1a49a41268879fe1176b63590f120e27', 'etag': '2rIR06eN+Luxe48JCPagl5A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:32:10,279 - INFO - 批量插入响应体: {'result': ['FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM47', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM57', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM67', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM77', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM87', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM97', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMA7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMB7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMC7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMD7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEME7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMF7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMG7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMH7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMI7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMJ7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMK7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEML7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMM7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMN7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMO7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMP7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMQ7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMR7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMS7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMT7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMU7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMV7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMW7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMX7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMY7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMZ7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM08', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM18', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM28', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM38', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM48', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM58', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM68', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM78', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM88', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM98', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMA8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMB8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMC8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMD8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEME8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMF8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMG8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMH8']}
2025-08-17 10:32:10,279 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-08-17 10:32:10,279 - INFO - 成功插入的数据ID: ['FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM47', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM57', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM67', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM77', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM87', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM97', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMA7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMB7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMC7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMD7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEME7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMF7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMG7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMH7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMI7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMJ7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMK7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEML7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMM7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMN7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMO7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMP7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMQ7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMR7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMS7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMT7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMU7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMV7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMW7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMX7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMY7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMZ7', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM08', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM18', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM28', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM38', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM48', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM58', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM68', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM78', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM88', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEM98', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMA8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMB8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMC8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMD8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEME8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMF8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMG8', 'FINST-L4E66Y61JU2YB92EAK2EHBAP9RKG2YLWL2FEMH8']
2025-08-17 10:32:15,544 - INFO - 批量插入响应状态码: 200
2025-08-17 10:32:15,544 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:32:22 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2427', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '13ECED61-E2C3-7238-897F-B6DC07B3F407', 'x-acs-trace-id': '98a68b5619b5e62b3972da873baacb8f', 'etag': '2Fmr7w+NEOKloHfOUqaWfJw7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:32:15,544 - INFO - 批量插入响应体: {'result': ['FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM1Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM2Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM3Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM4Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM5Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM6Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM7Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM8Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM9Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMAZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMBZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMCZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMDZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMEZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMFZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMGZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMHZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMIZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMJZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMKZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMLZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMMZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMNZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMOZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMPZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMQZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMRZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMSZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMTZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMUZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMVZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMWZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMXZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMYZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMZZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM001', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM101', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM201', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM301', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM401', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM501', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM601', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEM701', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEM801', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEM901', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEMA01', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEMB01', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEMC01', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEMD01', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEME01']}
2025-08-17 10:32:15,544 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-08-17 10:32:15,544 - INFO - 成功插入的数据ID: ['FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM1Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM2Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM3Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM4Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM5Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM6Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM7Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM8Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM9Z', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMAZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMBZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMCZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMDZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMEZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMFZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMGZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMHZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMIZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMJZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMKZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMLZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMMZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMNZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMOZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMPZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMQZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMRZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMSZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMTZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMUZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMVZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMWZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMXZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMYZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEMZZ', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM001', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM101', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM201', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM301', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM401', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM501', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z24O0M2FEM601', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEM701', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEM801', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEM901', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEMA01', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEMB01', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEMC01', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEMD01', 'FINST-G9G669D1SH1YAI4MD5HTACC76A4Z25O0M2FEME01']
2025-08-17 10:32:20,794 - INFO - 批量插入响应状态码: 200
2025-08-17 10:32:20,794 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:32:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '21CA2717-58DB-7349-8F16-BF1F2B5B0584', 'x-acs-trace-id': '51e0a35cb61843abeedc722378592a6b', 'etag': '2XlZl0VDKU9QvW0yvq7Ztjw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:32:20,794 - INFO - 批量插入响应体: {'result': ['FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMZL', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM0M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM1M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM2M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM3M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM4M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM5M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM6M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM7M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM8M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM9M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMAM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMBM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMCM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMDM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMEM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMFM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMGM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMHM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMIM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMJM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMKM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMLM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMMM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMNM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMOM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMPM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMQM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMRM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMSM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMTM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMUM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMVM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMWM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMXM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMYM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMZM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM0N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM1N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM2N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM3N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM4N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM5N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM6N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM7N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM8N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM9N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMAN', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMBN', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMCN']}
2025-08-17 10:32:20,794 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-08-17 10:32:20,794 - INFO - 成功插入的数据ID: ['FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMZL', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM0M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM1M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM2M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM3M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM4M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM5M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM6M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM7M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM8M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM9M', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMAM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMBM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMCM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMDM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMEM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMFM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMGM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMHM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMIM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMJM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMKM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMLM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMMM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMNM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMOM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMPM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMQM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMRM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMSM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMTM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMUM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMVM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMWM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMXM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMYM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMZM', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM0N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM1N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM2N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM3N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM4N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM5N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM6N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM7N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM8N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEM9N', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMAN', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMBN', 'FINST-R8666Q71WX1YKWBI6X6O39O7P38338Q4M2FEMCN']
2025-08-17 10:32:26,060 - INFO - 批量插入响应状态码: 200
2025-08-17 10:32:26,060 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:32:32 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '553070C2-DD30-7467-BF6B-B3ED95BCD1C9', 'x-acs-trace-id': 'ecf138d3da4e482377145e54e4b9bdd5', 'etag': '2o+FGljWHBRvxytXOpsUlkg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:32:26,060 - INFO - 批量插入响应体: {'result': ['FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMZF', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM0G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM1G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM2G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM3G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM4G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM5G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM6G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM7G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM8G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM9G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMAG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMBG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMCG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMDG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMEG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMFG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMGG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMHG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMIG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMJG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMKG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMLG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMMG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMNG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMOG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMPG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMQG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMRG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMSG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMTG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMUG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMVG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMWG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMXG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMYG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMZG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM0H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM1H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM2H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM3H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM4H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM5H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM6H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM7H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEM8H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEM9H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMAH', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMBH', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMCH']}
2025-08-17 10:32:26,060 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-08-17 10:32:26,060 - INFO - 成功插入的数据ID: ['FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMZF', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM0G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM1G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM2G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM3G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM4G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM5G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM6G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM7G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM8G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM9G', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMAG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMBG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMCG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMDG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMEG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMFG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMGG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMHG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMIG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMJG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMKG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMLG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMMG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMNG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMOG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMPG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMQG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMRG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMSG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMTG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMUG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMVG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMWG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMXG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMYG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMZG', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM0H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM1H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM2H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM3H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM4H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM5H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM6H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM7H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEM8H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEM9H', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMAH', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMBH', 'FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMCH']
2025-08-17 10:32:31,279 - INFO - 批量插入响应状态码: 200
2025-08-17 10:32:31,279 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 02:32:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1212', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CAE34F2B-24A4-77F0-B462-FA0ACDD453B1', 'x-acs-trace-id': 'e9f5aaf93fa73ba2b883fd53f8a67f56', 'etag': '1RQXYYlVkZUgAuok1lvcxwg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 10:32:31,279 - INFO - 批量插入响应体: {'result': ['FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM1J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM2J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM3J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM4J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM5J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM6J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM7J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM8J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM9J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMAJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMBJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMCJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMDJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMEJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMFJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMGJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMHJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMIJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMJJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMKJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMLJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMMJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMNJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMOJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMPJ']}
2025-08-17 10:32:31,279 - INFO - 批量插入表单数据成功，批次 8，共 25 条记录
2025-08-17 10:32:31,279 - INFO - 成功插入的数据ID: ['FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM1J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM2J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM3J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM4J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM5J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM6J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM7J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM8J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM9J', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMAJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMBJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMCJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMDJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMEJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMFJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMGJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMHJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMIJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMJJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMKJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMLJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMMJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMNJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMOJ', 'FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMPJ']
2025-08-17 10:32:36,294 - INFO - 批量插入完成，共 375 条记录
2025-08-17 10:32:36,294 - INFO - 日期 2025-08-16 处理完成 - 更新: 0 条，插入: 375 条，错误: 0 条
2025-08-17 10:32:36,294 - INFO - 开始处理日期: 2025-08-17
2025-08-17 10:32:36,294 - INFO - Request Parameters - Page 1:
2025-08-17 10:32:36,294 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 10:32:36,294 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 10:32:36,899 - INFO - Response - Page 1:
2025-08-17 10:32:36,899 - INFO - 第 1 页获取到 3 条记录
2025-08-17 10:32:37,414 - INFO - 查询完成，共获取到 3 条记录
2025-08-17 10:32:37,414 - INFO - 获取到 3 条表单数据
2025-08-17 10:32:37,414 - INFO - 当前日期 2025-08-17 有 3 条MySQL数据需要处理
2025-08-17 10:32:37,414 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 10:32:37,414 - INFO - 数据同步完成！更新: 0 条，插入: 375 条，错误: 0 条
2025-08-17 10:32:37,414 - INFO - 同步完成
2025-08-17 13:30:33,679 - INFO - 使用默认增量同步（当天更新数据）
2025-08-17 13:30:33,679 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-17 13:30:33,679 - INFO - 查询参数: ('2025-08-17',)
2025-08-17 13:30:33,866 - INFO - MySQL查询成功，增量数据（日期: 2025-08-17），共获取 171 条记录
2025-08-17 13:30:33,866 - INFO - 获取到 3 个日期需要处理: ['2025-08-15', '2025-08-16', '2025-08-17']
2025-08-17 13:30:33,866 - INFO - 开始处理日期: 2025-08-15
2025-08-17 13:30:33,866 - INFO - Request Parameters - Page 1:
2025-08-17 13:30:33,866 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 13:30:33,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 13:30:41,986 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4B3D27FE-C036-7274-988A-191DA43850D7 Response: {'code': 'ServiceUnavailable', 'requestid': '4B3D27FE-C036-7274-988A-191DA43850D7', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4B3D27FE-C036-7274-988A-191DA43850D7)
2025-08-17 13:30:41,986 - INFO - 开始处理日期: 2025-08-16
2025-08-17 13:30:41,986 - INFO - Request Parameters - Page 1:
2025-08-17 13:30:41,986 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 13:30:41,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 13:30:50,127 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7985BA9E-8245-71E8-88AA-7F292E4539AB Response: {'code': 'ServiceUnavailable', 'requestid': '7985BA9E-8245-71E8-88AA-7F292E4539AB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7985BA9E-8245-71E8-88AA-7F292E4539AB)
2025-08-17 13:30:50,127 - INFO - 开始处理日期: 2025-08-17
2025-08-17 13:30:50,127 - INFO - Request Parameters - Page 1:
2025-08-17 13:30:50,127 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 13:30:50,127 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 13:30:50,689 - INFO - Response - Page 1:
2025-08-17 13:30:50,689 - INFO - 第 1 页获取到 3 条记录
2025-08-17 13:30:51,189 - INFO - 查询完成，共获取到 3 条记录
2025-08-17 13:30:51,189 - INFO - 获取到 3 条表单数据
2025-08-17 13:30:51,189 - INFO - 当前日期 2025-08-17 有 3 条MySQL数据需要处理
2025-08-17 13:30:51,189 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 13:30:51,189 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-17 13:31:51,200 - INFO - 开始同步昨天与今天的销售数据: 2025-08-16 至 2025-08-17
2025-08-17 13:31:51,200 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-17 13:31:51,200 - INFO - 查询参数: ('2025-08-16', '2025-08-17')
2025-08-17 13:31:51,387 - INFO - MySQL查询成功，时间段: 2025-08-16 至 2025-08-17，共获取 506 条记录
2025-08-17 13:31:51,387 - INFO - 获取到 2 个日期需要处理: ['2025-08-16', '2025-08-17']
2025-08-17 13:31:51,387 - INFO - 开始处理日期: 2025-08-16
2025-08-17 13:31:51,387 - INFO - Request Parameters - Page 1:
2025-08-17 13:31:51,387 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 13:31:51,387 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 13:31:59,512 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ED96A9B4-8D11-7AD8-A407-178A245EB6B4 Response: {'code': 'ServiceUnavailable', 'requestid': 'ED96A9B4-8D11-7AD8-A407-178A245EB6B4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ED96A9B4-8D11-7AD8-A407-178A245EB6B4)
2025-08-17 13:31:59,512 - INFO - 开始处理日期: 2025-08-17
2025-08-17 13:31:59,512 - INFO - Request Parameters - Page 1:
2025-08-17 13:31:59,512 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 13:31:59,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 13:32:00,059 - INFO - Response - Page 1:
2025-08-17 13:32:00,059 - INFO - 第 1 页获取到 3 条记录
2025-08-17 13:32:00,575 - INFO - 查询完成，共获取到 3 条记录
2025-08-17 13:32:00,575 - INFO - 获取到 3 条表单数据
2025-08-17 13:32:00,575 - INFO - 当前日期 2025-08-17 有 3 条MySQL数据需要处理
2025-08-17 13:32:00,575 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 13:32:00,575 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-17 13:32:00,575 - INFO - 同步完成
2025-08-17 16:30:33,646 - INFO - 使用默认增量同步（当天更新数据）
2025-08-17 16:30:33,646 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-17 16:30:33,646 - INFO - 查询参数: ('2025-08-17',)
2025-08-17 16:30:33,818 - INFO - MySQL查询成功，增量数据（日期: 2025-08-17），共获取 175 条记录
2025-08-17 16:30:33,818 - INFO - 获取到 4 个日期需要处理: ['2025-08-14', '2025-08-15', '2025-08-16', '2025-08-17']
2025-08-17 16:30:33,818 - INFO - 开始处理日期: 2025-08-14
2025-08-17 16:30:33,834 - INFO - Request Parameters - Page 1:
2025-08-17 16:30:33,834 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:33,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:41,943 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B45EC45F-84A4-7C47-AB56-F2DC4F0829FC Response: {'code': 'ServiceUnavailable', 'requestid': 'B45EC45F-84A4-7C47-AB56-F2DC4F0829FC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B45EC45F-84A4-7C47-AB56-F2DC4F0829FC)
2025-08-17 16:30:41,943 - INFO - 开始处理日期: 2025-08-15
2025-08-17 16:30:41,943 - INFO - Request Parameters - Page 1:
2025-08-17 16:30:41,943 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:41,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:50,047 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ADD491D4-35F5-786B-9BD4-D08BCE8FBA6C Response: {'code': 'ServiceUnavailable', 'requestid': 'ADD491D4-35F5-786B-9BD4-D08BCE8FBA6C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ADD491D4-35F5-786B-9BD4-D08BCE8FBA6C)
2025-08-17 16:30:50,047 - INFO - 开始处理日期: 2025-08-16
2025-08-17 16:30:50,047 - INFO - Request Parameters - Page 1:
2025-08-17 16:30:50,047 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:50,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:50,751 - INFO - Response - Page 1:
2025-08-17 16:30:50,751 - INFO - 第 1 页获取到 50 条记录
2025-08-17 16:30:51,266 - INFO - Request Parameters - Page 2:
2025-08-17 16:30:51,266 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:51,266 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:51,969 - INFO - Response - Page 2:
2025-08-17 16:30:51,969 - INFO - 第 2 页获取到 50 条记录
2025-08-17 16:30:52,485 - INFO - Request Parameters - Page 3:
2025-08-17 16:30:52,485 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:52,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:53,204 - INFO - Response - Page 3:
2025-08-17 16:30:53,204 - INFO - 第 3 页获取到 50 条记录
2025-08-17 16:30:53,719 - INFO - Request Parameters - Page 4:
2025-08-17 16:30:53,719 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:53,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:54,485 - INFO - Response - Page 4:
2025-08-17 16:30:54,485 - INFO - 第 4 页获取到 50 条记录
2025-08-17 16:30:54,985 - INFO - Request Parameters - Page 5:
2025-08-17 16:30:54,985 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:54,985 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:55,751 - INFO - Response - Page 5:
2025-08-17 16:30:55,751 - INFO - 第 5 页获取到 50 条记录
2025-08-17 16:30:56,251 - INFO - Request Parameters - Page 6:
2025-08-17 16:30:56,251 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:56,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:57,032 - INFO - Response - Page 6:
2025-08-17 16:30:57,032 - INFO - 第 6 页获取到 50 条记录
2025-08-17 16:30:57,532 - INFO - Request Parameters - Page 7:
2025-08-17 16:30:57,532 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:57,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:58,297 - INFO - Response - Page 7:
2025-08-17 16:30:58,297 - INFO - 第 7 页获取到 50 条记录
2025-08-17 16:30:58,813 - INFO - Request Parameters - Page 8:
2025-08-17 16:30:58,813 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:30:58,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:30:59,516 - INFO - Response - Page 8:
2025-08-17 16:30:59,516 - INFO - 第 8 页获取到 50 条记录
2025-08-17 16:31:00,032 - INFO - Request Parameters - Page 9:
2025-08-17 16:31:00,032 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:31:00,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:31:00,704 - INFO - Response - Page 9:
2025-08-17 16:31:00,704 - INFO - 第 9 页获取到 25 条记录
2025-08-17 16:31:01,219 - INFO - 查询完成，共获取到 425 条记录
2025-08-17 16:31:01,219 - INFO - 获取到 425 条表单数据
2025-08-17 16:31:01,219 - INFO - 当前日期 2025-08-16 有 169 条MySQL数据需要处理
2025-08-17 16:31:01,219 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEM9H
2025-08-17 16:31:01,782 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEM9H
2025-08-17 16:31:01,782 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 80208.93}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 80208.93}, {'field': 'order_count', 'old_value': 0, 'new_value': 35}]
2025-08-17 16:31:01,782 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMBJ
2025-08-17 16:31:02,344 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMBJ
2025-08-17 16:31:02,344 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2262.33}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2262.33}, {'field': 'order_count', 'old_value': 0, 'new_value': 97}]
2025-08-17 16:31:02,344 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM7H
2025-08-17 16:31:02,875 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM7H
2025-08-17 16:31:02,875 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 17099.96}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 17099.96}, {'field': 'order_count', 'old_value': 0, 'new_value': 25}]
2025-08-17 16:31:02,875 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMCJ
2025-08-17 16:31:03,438 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMCJ
2025-08-17 16:31:03,438 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 826.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 826.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 25}]
2025-08-17 16:31:03,438 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMAJ
2025-08-17 16:31:04,000 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMAJ
2025-08-17 16:31:04,000 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5837.84}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5837.84}, {'field': 'order_count', 'old_value': 0, 'new_value': 215}]
2025-08-17 16:31:04,000 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMJJ
2025-08-17 16:31:04,563 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMJJ
2025-08-17 16:31:04,563 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 429.5}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 429.5}, {'field': 'order_count', 'old_value': 0, 'new_value': 38}]
2025-08-17 16:31:04,563 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMIJ
2025-08-17 16:31:05,250 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMIJ
2025-08-17 16:31:05,250 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 18301.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 18301.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 12}]
2025-08-17 16:31:05,250 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMHJ
2025-08-17 16:31:05,704 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMHJ
2025-08-17 16:31:05,704 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 14930.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 14930.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-08-17 16:31:05,704 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMCH
2025-08-17 16:31:06,297 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMCH
2025-08-17 16:31:06,297 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 399.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-17 16:31:06,297 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM3J
2025-08-17 16:31:07,625 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM3J
2025-08-17 16:31:07,625 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6400.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6400.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 50}]
2025-08-17 16:31:07,625 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM7J
2025-08-17 16:31:08,188 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM7J
2025-08-17 16:31:08,188 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 45000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 45000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-17 16:31:08,188 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM8J
2025-08-17 16:31:08,750 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM8J
2025-08-17 16:31:08,750 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4620.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4620.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 503}]
2025-08-17 16:31:08,750 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM5H
2025-08-17 16:31:09,375 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM5H
2025-08-17 16:31:09,375 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 15464.0, 'new_value': 13500.0}, {'field': 'total_amount', 'old_value': 15464.0, 'new_value': 13500.0}, {'field': 'order_count', 'old_value': 111, 'new_value': 350}]
2025-08-17 16:31:09,375 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMBH
2025-08-17 16:31:09,907 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS428S8M2FEMBH
2025-08-17 16:31:09,907 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 21335.98}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 21335.98}, {'field': 'order_count', 'old_value': 0, 'new_value': 176}]
2025-08-17 16:31:09,907 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM6H
2025-08-17 16:31:10,454 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM6H
2025-08-17 16:31:10,454 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 2173.6}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 2173.6}, {'field': 'order_count', 'old_value': 0, 'new_value': 379}]
2025-08-17 16:31:10,454 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMEJ
2025-08-17 16:31:11,110 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMEJ
2025-08-17 16:31:11,110 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6056.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6056.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 559}]
2025-08-17 16:31:11,110 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM2J
2025-08-17 16:31:11,688 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEM2J
2025-08-17 16:31:11,688 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5156.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5156.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 36}]
2025-08-17 16:31:11,688 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMGJ
2025-08-17 16:31:12,313 - INFO - 更新表单数据成功: FINST-AI866781PY1YVB99CG4R5DR5H6S03KTCM2FEMGJ
2025-08-17 16:31:12,313 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 24}]
2025-08-17 16:31:12,313 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMNG
2025-08-17 16:31:12,860 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMNG
2025-08-17 16:31:12,860 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 699.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 699.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-17 16:31:12,860 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMQG
2025-08-17 16:31:13,422 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMQG
2025-08-17 16:31:13,422 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 8800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 8800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-17 16:31:13,422 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMTG
2025-08-17 16:31:14,047 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMTG
2025-08-17 16:31:14,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 17500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 17500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-08-17 16:31:14,047 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMSG
2025-08-17 16:31:14,610 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMSG
2025-08-17 16:31:14,610 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4302.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4302.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-17 16:31:14,610 - INFO - 开始批量插入 32 条新记录
2025-08-17 16:31:14,828 - INFO - 批量插入响应状态码: 200
2025-08-17 16:31:14,828 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 08:31:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1548', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'ABADE940-8361-7123-AD1F-7C08F0E3598E', 'x-acs-trace-id': '387ec1d8c73db795757c1b4d44995522', 'etag': '1PSMO372GqpnXTMlUGuEPqA8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 16:31:14,828 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM3A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM4A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM5A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM6A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM7A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM8A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM9A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMAA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMBA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMCA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMDA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMEA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMFA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMGA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMHA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMIA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMJA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMKA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMLA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMMA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMNA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMOA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMPA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMQA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMRA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMSA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMTA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMUA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMVA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMWA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMXA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMYA']}
2025-08-17 16:31:14,828 - INFO - 批量插入表单数据成功，批次 1，共 32 条记录
2025-08-17 16:31:14,828 - INFO - 成功插入的数据ID: ['FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM3A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM4A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM5A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM6A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM7A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM8A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEM9A', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMAA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMBA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMCA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMDA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMEA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMFA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMGA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMHA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMIA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMJA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMKA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMLA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMMA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMNA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMOA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMPA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMQA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMRA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMSA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMTA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMUA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMVA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMWA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMXA', 'FINST-XMC66R91EU2YCQP1CS1QGCV71F5I22MOFFFEMYA']
2025-08-17 16:31:19,844 - INFO - 批量插入完成，共 32 条记录
2025-08-17 16:31:19,844 - INFO - 日期 2025-08-16 处理完成 - 更新: 22 条，插入: 32 条，错误: 0 条
2025-08-17 16:31:19,844 - INFO - 开始处理日期: 2025-08-17
2025-08-17 16:31:19,844 - INFO - Request Parameters - Page 1:
2025-08-17 16:31:19,844 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:31:19,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:31:20,360 - INFO - Response - Page 1:
2025-08-17 16:31:20,360 - INFO - 第 1 页获取到 3 条记录
2025-08-17 16:31:20,875 - INFO - 查询完成，共获取到 3 条记录
2025-08-17 16:31:20,875 - INFO - 获取到 3 条表单数据
2025-08-17 16:31:20,875 - INFO - 当前日期 2025-08-17 有 3 条MySQL数据需要处理
2025-08-17 16:31:20,875 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 16:31:20,875 - INFO - 数据同步完成！更新: 22 条，插入: 32 条，错误: 2 条
2025-08-17 16:32:20,886 - INFO - 开始同步昨天与今天的销售数据: 2025-08-16 至 2025-08-17
2025-08-17 16:32:20,886 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-17 16:32:20,886 - INFO - 查询参数: ('2025-08-16', '2025-08-17')
2025-08-17 16:32:21,058 - INFO - MySQL查询成功，时间段: 2025-08-16 至 2025-08-17，共获取 508 条记录
2025-08-17 16:32:21,058 - INFO - 获取到 2 个日期需要处理: ['2025-08-16', '2025-08-17']
2025-08-17 16:32:21,073 - INFO - 开始处理日期: 2025-08-16
2025-08-17 16:32:21,073 - INFO - Request Parameters - Page 1:
2025-08-17 16:32:21,073 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:21,073 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:21,886 - INFO - Response - Page 1:
2025-08-17 16:32:21,886 - INFO - 第 1 页获取到 50 条记录
2025-08-17 16:32:22,401 - INFO - Request Parameters - Page 2:
2025-08-17 16:32:22,401 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:22,401 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:23,120 - INFO - Response - Page 2:
2025-08-17 16:32:23,120 - INFO - 第 2 页获取到 50 条记录
2025-08-17 16:32:23,620 - INFO - Request Parameters - Page 3:
2025-08-17 16:32:23,620 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:23,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:24,354 - INFO - Response - Page 3:
2025-08-17 16:32:24,354 - INFO - 第 3 页获取到 50 条记录
2025-08-17 16:32:24,870 - INFO - Request Parameters - Page 4:
2025-08-17 16:32:24,870 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:24,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:25,604 - INFO - Response - Page 4:
2025-08-17 16:32:25,604 - INFO - 第 4 页获取到 50 条记录
2025-08-17 16:32:26,104 - INFO - Request Parameters - Page 5:
2025-08-17 16:32:26,104 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:26,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:26,839 - INFO - Response - Page 5:
2025-08-17 16:32:26,839 - INFO - 第 5 页获取到 50 条记录
2025-08-17 16:32:27,354 - INFO - Request Parameters - Page 6:
2025-08-17 16:32:27,354 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:27,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:28,104 - INFO - Response - Page 6:
2025-08-17 16:32:28,104 - INFO - 第 6 页获取到 50 条记录
2025-08-17 16:32:28,620 - INFO - Request Parameters - Page 7:
2025-08-17 16:32:28,620 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:28,620 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:29,354 - INFO - Response - Page 7:
2025-08-17 16:32:29,354 - INFO - 第 7 页获取到 50 条记录
2025-08-17 16:32:29,870 - INFO - Request Parameters - Page 8:
2025-08-17 16:32:29,870 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:29,870 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:30,589 - INFO - Response - Page 8:
2025-08-17 16:32:30,589 - INFO - 第 8 页获取到 50 条记录
2025-08-17 16:32:31,104 - INFO - Request Parameters - Page 9:
2025-08-17 16:32:31,104 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:31,104 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:31,854 - INFO - Response - Page 9:
2025-08-17 16:32:31,854 - INFO - 第 9 页获取到 50 条记录
2025-08-17 16:32:32,354 - INFO - Request Parameters - Page 10:
2025-08-17 16:32:32,354 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:32,354 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:32,917 - INFO - Response - Page 10:
2025-08-17 16:32:32,932 - INFO - 第 10 页获取到 7 条记录
2025-08-17 16:32:33,432 - INFO - 查询完成，共获取到 457 条记录
2025-08-17 16:32:33,432 - INFO - 获取到 457 条表单数据
2025-08-17 16:32:33,432 - INFO - 当前日期 2025-08-16 有 491 条MySQL数据需要处理
2025-08-17 16:32:33,448 - INFO - 开始批量插入 34 条新记录
2025-08-17 16:32:33,682 - INFO - 批量插入响应状态码: 200
2025-08-17 16:32:33,682 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 08:32:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1644', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C79C8741-C75E-7885-8C68-C1FB41006C31', 'x-acs-trace-id': 'fd25d13d1dfda99196c8ec67a36df19b', 'etag': '1lUku/YhbhPY/fmaBsqiREA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 16:32:33,682 - INFO - 批量插入响应体: {'result': ['FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMOC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMPC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMQC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMRC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMSC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMTC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMUC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMVC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMWC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMXC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMYC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMZC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM0D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM1D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM2D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM3D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM4D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM5D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM6D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM7D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM8D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM9D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMAD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMBD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMCD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMDD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMED', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMFD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMGD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMHD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMID', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMJD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMKD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMLD']}
2025-08-17 16:32:33,682 - INFO - 批量插入表单数据成功，批次 1，共 34 条记录
2025-08-17 16:32:33,682 - INFO - 成功插入的数据ID: ['FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMOC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMPC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMQC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMRC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMSC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMTC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMUC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMVC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMWC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMXC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMYC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMZC', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM0D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM1D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM2D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM3D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM4D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM5D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM6D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM7D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM8D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEM9D', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMAD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMBD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMCD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMDD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMED', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMFD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMGD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMHD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMID', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMJD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMKD', 'FINST-QUA66S71GZ2YNASNBI0YQDCJMQ0B2EGDHFFEMLD']
2025-08-17 16:32:38,698 - INFO - 批量插入完成，共 34 条记录
2025-08-17 16:32:38,698 - INFO - 日期 2025-08-16 处理完成 - 更新: 0 条，插入: 34 条，错误: 0 条
2025-08-17 16:32:38,698 - INFO - 开始处理日期: 2025-08-17
2025-08-17 16:32:38,698 - INFO - Request Parameters - Page 1:
2025-08-17 16:32:38,698 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 16:32:38,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 16:32:39,245 - INFO - Response - Page 1:
2025-08-17 16:32:39,245 - INFO - 第 1 页获取到 3 条记录
2025-08-17 16:32:39,745 - INFO - 查询完成，共获取到 3 条记录
2025-08-17 16:32:39,745 - INFO - 获取到 3 条表单数据
2025-08-17 16:32:39,745 - INFO - 当前日期 2025-08-17 有 3 条MySQL数据需要处理
2025-08-17 16:32:39,745 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 16:32:39,745 - INFO - 数据同步完成！更新: 0 条，插入: 34 条，错误: 0 条
2025-08-17 16:32:39,745 - INFO - 同步完成
2025-08-17 19:30:33,905 - INFO - 使用默认增量同步（当天更新数据）
2025-08-17 19:30:33,905 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-17 19:30:33,905 - INFO - 查询参数: ('2025-08-17',)
2025-08-17 19:30:34,077 - INFO - MySQL查询成功，增量数据（日期: 2025-08-17），共获取 175 条记录
2025-08-17 19:30:34,077 - INFO - 获取到 4 个日期需要处理: ['2025-08-14', '2025-08-15', '2025-08-16', '2025-08-17']
2025-08-17 19:30:34,077 - INFO - 开始处理日期: 2025-08-14
2025-08-17 19:30:34,092 - INFO - Request Parameters - Page 1:
2025-08-17 19:30:34,092 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:34,092 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:42,202 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E7FFA6EC-0C30-7A9E-A2D2-DDBBE738AC88 Response: {'code': 'ServiceUnavailable', 'requestid': 'E7FFA6EC-0C30-7A9E-A2D2-DDBBE738AC88', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E7FFA6EC-0C30-7A9E-A2D2-DDBBE738AC88)
2025-08-17 19:30:42,202 - INFO - 开始处理日期: 2025-08-15
2025-08-17 19:30:42,202 - INFO - Request Parameters - Page 1:
2025-08-17 19:30:42,202 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:42,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:50,327 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7DC717FB-8315-7975-BFBD-A0F06260362E Response: {'code': 'ServiceUnavailable', 'requestid': '7DC717FB-8315-7975-BFBD-A0F06260362E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7DC717FB-8315-7975-BFBD-A0F06260362E)
2025-08-17 19:30:50,327 - INFO - 开始处理日期: 2025-08-16
2025-08-17 19:30:50,327 - INFO - Request Parameters - Page 1:
2025-08-17 19:30:50,327 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:50,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:51,123 - INFO - Response - Page 1:
2025-08-17 19:30:51,123 - INFO - 第 1 页获取到 50 条记录
2025-08-17 19:30:51,623 - INFO - Request Parameters - Page 2:
2025-08-17 19:30:51,623 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:51,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:52,327 - INFO - Response - Page 2:
2025-08-17 19:30:52,327 - INFO - 第 2 页获取到 50 条记录
2025-08-17 19:30:52,827 - INFO - Request Parameters - Page 3:
2025-08-17 19:30:52,827 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:52,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:53,592 - INFO - Response - Page 3:
2025-08-17 19:30:53,592 - INFO - 第 3 页获取到 50 条记录
2025-08-17 19:30:54,108 - INFO - Request Parameters - Page 4:
2025-08-17 19:30:54,108 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:54,108 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:54,858 - INFO - Response - Page 4:
2025-08-17 19:30:54,858 - INFO - 第 4 页获取到 50 条记录
2025-08-17 19:30:55,358 - INFO - Request Parameters - Page 5:
2025-08-17 19:30:55,358 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:55,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:56,108 - INFO - Response - Page 5:
2025-08-17 19:30:56,108 - INFO - 第 5 页获取到 50 条记录
2025-08-17 19:30:56,623 - INFO - Request Parameters - Page 6:
2025-08-17 19:30:56,623 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:56,623 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:57,400 - INFO - Response - Page 6:
2025-08-17 19:30:57,400 - INFO - 第 6 页获取到 50 条记录
2025-08-17 19:30:57,915 - INFO - Request Parameters - Page 7:
2025-08-17 19:30:57,915 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:57,915 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:58,634 - INFO - Response - Page 7:
2025-08-17 19:30:58,634 - INFO - 第 7 页获取到 50 条记录
2025-08-17 19:30:59,150 - INFO - Request Parameters - Page 8:
2025-08-17 19:30:59,150 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:30:59,150 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:30:59,931 - INFO - Response - Page 8:
2025-08-17 19:30:59,931 - INFO - 第 8 页获取到 50 条记录
2025-08-17 19:31:00,431 - INFO - Request Parameters - Page 9:
2025-08-17 19:31:00,431 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:31:00,431 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:31:01,119 - INFO - Response - Page 9:
2025-08-17 19:31:01,119 - INFO - 第 9 页获取到 50 条记录
2025-08-17 19:31:01,619 - INFO - Request Parameters - Page 10:
2025-08-17 19:31:01,619 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:31:01,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:31:02,306 - INFO - Response - Page 10:
2025-08-17 19:31:02,306 - INFO - 第 10 页获取到 41 条记录
2025-08-17 19:31:02,822 - INFO - 查询完成，共获取到 491 条记录
2025-08-17 19:31:02,822 - INFO - 获取到 491 条表单数据
2025-08-17 19:31:02,822 - INFO - 当前日期 2025-08-16 有 169 条MySQL数据需要处理
2025-08-17 19:31:02,837 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM2H
2025-08-17 19:31:03,306 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEM2H
2025-08-17 19:31:03,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4780.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4780.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-17 19:31:03,306 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMLG
2025-08-17 19:31:03,806 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMLG
2025-08-17 19:31:03,806 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5740.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5740.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-08-17 19:31:03,806 - INFO - 开始更新记录 - 表单实例ID: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMPG
2025-08-17 19:31:04,306 - INFO - 更新表单数据成功: FINST-NDC66NB1PS2Y9EKG6QF05CAS7XS427S8M2FEMPG
2025-08-17 19:31:04,306 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7560.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7560.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-17 19:31:04,306 - INFO - 日期 2025-08-16 处理完成 - 更新: 3 条，插入: 0 条，错误: 0 条
2025-08-17 19:31:04,306 - INFO - 开始处理日期: 2025-08-17
2025-08-17 19:31:04,306 - INFO - Request Parameters - Page 1:
2025-08-17 19:31:04,306 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:31:04,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:31:04,837 - INFO - Response - Page 1:
2025-08-17 19:31:04,837 - INFO - 第 1 页获取到 3 条记录
2025-08-17 19:31:05,353 - INFO - 查询完成，共获取到 3 条记录
2025-08-17 19:31:05,353 - INFO - 获取到 3 条表单数据
2025-08-17 19:31:05,353 - INFO - 当前日期 2025-08-17 有 3 条MySQL数据需要处理
2025-08-17 19:31:05,353 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 19:31:05,353 - INFO - 数据同步完成！更新: 3 条，插入: 0 条，错误: 2 条
2025-08-17 19:32:05,363 - INFO - 开始同步昨天与今天的销售数据: 2025-08-16 至 2025-08-17
2025-08-17 19:32:05,363 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-17 19:32:05,363 - INFO - 查询参数: ('2025-08-16', '2025-08-17')
2025-08-17 19:32:05,535 - INFO - MySQL查询成功，时间段: 2025-08-16 至 2025-08-17，共获取 508 条记录
2025-08-17 19:32:05,535 - INFO - 获取到 2 个日期需要处理: ['2025-08-16', '2025-08-17']
2025-08-17 19:32:05,551 - INFO - 开始处理日期: 2025-08-16
2025-08-17 19:32:05,551 - INFO - Request Parameters - Page 1:
2025-08-17 19:32:05,551 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:05,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:06,348 - INFO - Response - Page 1:
2025-08-17 19:32:06,348 - INFO - 第 1 页获取到 50 条记录
2025-08-17 19:32:06,863 - INFO - Request Parameters - Page 2:
2025-08-17 19:32:06,863 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:06,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:07,613 - INFO - Response - Page 2:
2025-08-17 19:32:07,613 - INFO - 第 2 页获取到 50 条记录
2025-08-17 19:32:08,113 - INFO - Request Parameters - Page 3:
2025-08-17 19:32:08,113 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:08,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:08,894 - INFO - Response - Page 3:
2025-08-17 19:32:08,894 - INFO - 第 3 页获取到 50 条记录
2025-08-17 19:32:09,410 - INFO - Request Parameters - Page 4:
2025-08-17 19:32:09,410 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:09,410 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:10,223 - INFO - Response - Page 4:
2025-08-17 19:32:10,223 - INFO - 第 4 页获取到 50 条记录
2025-08-17 19:32:10,723 - INFO - Request Parameters - Page 5:
2025-08-17 19:32:10,723 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:10,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:11,457 - INFO - Response - Page 5:
2025-08-17 19:32:11,457 - INFO - 第 5 页获取到 50 条记录
2025-08-17 19:32:11,973 - INFO - Request Parameters - Page 6:
2025-08-17 19:32:11,973 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:11,973 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:12,723 - INFO - Response - Page 6:
2025-08-17 19:32:12,723 - INFO - 第 6 页获取到 50 条记录
2025-08-17 19:32:13,223 - INFO - Request Parameters - Page 7:
2025-08-17 19:32:13,223 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:13,223 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:13,957 - INFO - Response - Page 7:
2025-08-17 19:32:13,957 - INFO - 第 7 页获取到 50 条记录
2025-08-17 19:32:14,472 - INFO - Request Parameters - Page 8:
2025-08-17 19:32:14,472 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:14,472 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:15,223 - INFO - Response - Page 8:
2025-08-17 19:32:15,223 - INFO - 第 8 页获取到 50 条记录
2025-08-17 19:32:15,738 - INFO - Request Parameters - Page 9:
2025-08-17 19:32:15,738 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:15,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:16,457 - INFO - Response - Page 9:
2025-08-17 19:32:16,457 - INFO - 第 9 页获取到 50 条记录
2025-08-17 19:32:16,972 - INFO - Request Parameters - Page 10:
2025-08-17 19:32:16,972 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:16,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:17,707 - INFO - Response - Page 10:
2025-08-17 19:32:17,707 - INFO - 第 10 页获取到 41 条记录
2025-08-17 19:32:18,207 - INFO - 查询完成，共获取到 491 条记录
2025-08-17 19:32:18,207 - INFO - 获取到 491 条表单数据
2025-08-17 19:32:18,207 - INFO - 当前日期 2025-08-16 有 491 条MySQL数据需要处理
2025-08-17 19:32:18,222 - INFO - 日期 2025-08-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 19:32:18,222 - INFO - 开始处理日期: 2025-08-17
2025-08-17 19:32:18,222 - INFO - Request Parameters - Page 1:
2025-08-17 19:32:18,222 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 19:32:18,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 19:32:18,738 - INFO - Response - Page 1:
2025-08-17 19:32:18,738 - INFO - 第 1 页获取到 3 条记录
2025-08-17 19:32:19,254 - INFO - 查询完成，共获取到 3 条记录
2025-08-17 19:32:19,254 - INFO - 获取到 3 条表单数据
2025-08-17 19:32:19,254 - INFO - 当前日期 2025-08-17 有 3 条MySQL数据需要处理
2025-08-17 19:32:19,254 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 19:32:19,254 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 19:32:19,254 - INFO - 同步完成
2025-08-17 22:30:33,201 - INFO - 使用默认增量同步（当天更新数据）
2025-08-17 22:30:33,201 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-17 22:30:33,201 - INFO - 查询参数: ('2025-08-17',)
2025-08-17 22:30:33,373 - INFO - MySQL查询成功，增量数据（日期: 2025-08-17），共获取 192 条记录
2025-08-17 22:30:33,373 - INFO - 获取到 4 个日期需要处理: ['2025-08-14', '2025-08-15', '2025-08-16', '2025-08-17']
2025-08-17 22:30:33,388 - INFO - 开始处理日期: 2025-08-14
2025-08-17 22:30:33,388 - INFO - Request Parameters - Page 1:
2025-08-17 22:30:33,388 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:30:33,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:30:41,498 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 119D3877-90BF-72BB-9537-E7D73340F489 Response: {'code': 'ServiceUnavailable', 'requestid': '119D3877-90BF-72BB-9537-E7D73340F489', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 119D3877-90BF-72BB-9537-E7D73340F489)
2025-08-17 22:30:41,498 - INFO - 开始处理日期: 2025-08-15
2025-08-17 22:30:41,498 - INFO - Request Parameters - Page 1:
2025-08-17 22:30:41,498 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:30:41,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:30:42,404 - INFO - Response - Page 1:
2025-08-17 22:30:42,404 - INFO - 第 1 页获取到 50 条记录
2025-08-17 22:30:42,919 - INFO - Request Parameters - Page 2:
2025-08-17 22:30:42,919 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:30:42,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:30:51,044 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D51D2963-D562-745A-8C46-2D8F3A8A9E93 Response: {'code': 'ServiceUnavailable', 'requestid': 'D51D2963-D562-745A-8C46-2D8F3A8A9E93', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D51D2963-D562-745A-8C46-2D8F3A8A9E93)
2025-08-17 22:30:51,044 - INFO - 开始处理日期: 2025-08-16
2025-08-17 22:30:51,044 - INFO - Request Parameters - Page 1:
2025-08-17 22:30:51,044 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:30:51,044 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:30:56,607 - INFO - Response - Page 1:
2025-08-17 22:30:56,607 - INFO - 第 1 页获取到 50 条记录
2025-08-17 22:30:57,122 - INFO - Request Parameters - Page 2:
2025-08-17 22:30:57,122 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:30:57,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:30:57,857 - INFO - Response - Page 2:
2025-08-17 22:30:57,872 - INFO - 第 2 页获取到 50 条记录
2025-08-17 22:30:58,372 - INFO - Request Parameters - Page 3:
2025-08-17 22:30:58,372 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:30:58,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:30:59,154 - INFO - Response - Page 3:
2025-08-17 22:30:59,154 - INFO - 第 3 页获取到 50 条记录
2025-08-17 22:30:59,654 - INFO - Request Parameters - Page 4:
2025-08-17 22:30:59,654 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:30:59,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:31:00,404 - INFO - Response - Page 4:
2025-08-17 22:31:00,404 - INFO - 第 4 页获取到 50 条记录
2025-08-17 22:31:00,904 - INFO - Request Parameters - Page 5:
2025-08-17 22:31:00,904 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:31:00,904 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:31:01,711 - INFO - Response - Page 5:
2025-08-17 22:31:01,711 - INFO - 第 5 页获取到 50 条记录
2025-08-17 22:31:02,227 - INFO - Request Parameters - Page 6:
2025-08-17 22:31:02,227 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:31:02,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:31:02,992 - INFO - Response - Page 6:
2025-08-17 22:31:02,992 - INFO - 第 6 页获取到 50 条记录
2025-08-17 22:31:03,492 - INFO - Request Parameters - Page 7:
2025-08-17 22:31:03,492 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:31:03,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:31:04,196 - INFO - Response - Page 7:
2025-08-17 22:31:04,196 - INFO - 第 7 页获取到 50 条记录
2025-08-17 22:31:04,696 - INFO - Request Parameters - Page 8:
2025-08-17 22:31:04,696 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:31:04,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:31:05,399 - INFO - Response - Page 8:
2025-08-17 22:31:05,399 - INFO - 第 8 页获取到 50 条记录
2025-08-17 22:31:05,914 - INFO - Request Parameters - Page 9:
2025-08-17 22:31:05,914 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:31:05,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:31:06,617 - INFO - Response - Page 9:
2025-08-17 22:31:06,617 - INFO - 第 9 页获取到 50 条记录
2025-08-17 22:31:07,117 - INFO - Request Parameters - Page 10:
2025-08-17 22:31:07,117 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:31:07,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:31:07,883 - INFO - Response - Page 10:
2025-08-17 22:31:07,883 - INFO - 第 10 页获取到 41 条记录
2025-08-17 22:31:08,383 - INFO - 查询完成，共获取到 491 条记录
2025-08-17 22:31:08,383 - INFO - 获取到 491 条表单数据
2025-08-17 22:31:08,383 - INFO - 当前日期 2025-08-16 有 169 条MySQL数据需要处理
2025-08-17 22:31:08,383 - INFO - 日期 2025-08-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 22:31:08,383 - INFO - 开始处理日期: 2025-08-17
2025-08-17 22:31:08,383 - INFO - Request Parameters - Page 1:
2025-08-17 22:31:08,383 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:31:08,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:31:08,883 - INFO - Response - Page 1:
2025-08-17 22:31:08,883 - INFO - 第 1 页获取到 3 条记录
2025-08-17 22:31:09,399 - INFO - 查询完成，共获取到 3 条记录
2025-08-17 22:31:09,399 - INFO - 获取到 3 条表单数据
2025-08-17 22:31:09,399 - INFO - 当前日期 2025-08-17 有 19 条MySQL数据需要处理
2025-08-17 22:31:09,399 - INFO - 开始批量插入 16 条新记录
2025-08-17 22:31:09,555 - INFO - 批量插入响应状态码: 200
2025-08-17 22:31:09,555 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 17 Aug 2025 14:31:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '796', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1A99844A-B7D9-7A7C-940F-2CCA29346044', 'x-acs-trace-id': 'd1c991b763f584319c8f29634ff3d24f', 'etag': '7MSkab/8yjoCjXNIkvD+qzA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-17 22:31:09,555 - INFO - 批量插入响应体: {'result': ['FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM331', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM431', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM531', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM631', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM731', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM831', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM931', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMA31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMB31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMC31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMD31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEME31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMF31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMG31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMH31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMI31']}
2025-08-17 22:31:09,555 - INFO - 批量插入表单数据成功，批次 1，共 16 条记录
2025-08-17 22:31:09,555 - INFO - 成功插入的数据ID: ['FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM331', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM431', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM531', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM631', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM731', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM831', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEM931', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMA31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMB31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMC31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMD31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEME31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMF31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMG31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMH31', 'FINST-MKF66PA1EY1Y5H08DPI955UADEGD317EASFEMI31']
2025-08-17 22:31:14,571 - INFO - 批量插入完成，共 16 条记录
2025-08-17 22:31:14,571 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 16 条，错误: 0 条
2025-08-17 22:31:14,571 - INFO - 数据同步完成！更新: 0 条，插入: 16 条，错误: 2 条
2025-08-17 22:32:14,581 - INFO - 开始同步昨天与今天的销售数据: 2025-08-16 至 2025-08-17
2025-08-17 22:32:14,581 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-17 22:32:14,581 - INFO - 查询参数: ('2025-08-16', '2025-08-17')
2025-08-17 22:32:14,768 - INFO - MySQL查询成功，时间段: 2025-08-16 至 2025-08-17，共获取 525 条记录
2025-08-17 22:32:14,768 - INFO - 获取到 2 个日期需要处理: ['2025-08-16', '2025-08-17']
2025-08-17 22:32:14,768 - INFO - 开始处理日期: 2025-08-16
2025-08-17 22:32:14,768 - INFO - Request Parameters - Page 1:
2025-08-17 22:32:14,768 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:14,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:15,518 - INFO - Response - Page 1:
2025-08-17 22:32:15,518 - INFO - 第 1 页获取到 50 条记录
2025-08-17 22:32:16,034 - INFO - Request Parameters - Page 2:
2025-08-17 22:32:16,034 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:16,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:16,753 - INFO - Response - Page 2:
2025-08-17 22:32:16,753 - INFO - 第 2 页获取到 50 条记录
2025-08-17 22:32:17,268 - INFO - Request Parameters - Page 3:
2025-08-17 22:32:17,268 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:17,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:18,050 - INFO - Response - Page 3:
2025-08-17 22:32:18,050 - INFO - 第 3 页获取到 50 条记录
2025-08-17 22:32:18,550 - INFO - Request Parameters - Page 4:
2025-08-17 22:32:18,550 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:18,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:19,284 - INFO - Response - Page 4:
2025-08-17 22:32:19,284 - INFO - 第 4 页获取到 50 条记录
2025-08-17 22:32:19,784 - INFO - Request Parameters - Page 5:
2025-08-17 22:32:19,784 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:19,784 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:20,550 - INFO - Response - Page 5:
2025-08-17 22:32:20,550 - INFO - 第 5 页获取到 50 条记录
2025-08-17 22:32:21,065 - INFO - Request Parameters - Page 6:
2025-08-17 22:32:21,065 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:21,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:21,831 - INFO - Response - Page 6:
2025-08-17 22:32:21,831 - INFO - 第 6 页获取到 50 条记录
2025-08-17 22:32:22,346 - INFO - Request Parameters - Page 7:
2025-08-17 22:32:22,346 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:22,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:23,065 - INFO - Response - Page 7:
2025-08-17 22:32:23,065 - INFO - 第 7 页获取到 50 条记录
2025-08-17 22:32:23,581 - INFO - Request Parameters - Page 8:
2025-08-17 22:32:23,581 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:23,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:24,300 - INFO - Response - Page 8:
2025-08-17 22:32:24,300 - INFO - 第 8 页获取到 50 条记录
2025-08-17 22:32:24,815 - INFO - Request Parameters - Page 9:
2025-08-17 22:32:24,815 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:24,815 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:25,471 - INFO - Response - Page 9:
2025-08-17 22:32:25,487 - INFO - 第 9 页获取到 50 条记录
2025-08-17 22:32:25,987 - INFO - Request Parameters - Page 10:
2025-08-17 22:32:25,987 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:25,987 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:26,737 - INFO - Response - Page 10:
2025-08-17 22:32:26,737 - INFO - 第 10 页获取到 41 条记录
2025-08-17 22:32:27,253 - INFO - 查询完成，共获取到 491 条记录
2025-08-17 22:32:27,253 - INFO - 获取到 491 条表单数据
2025-08-17 22:32:27,253 - INFO - 当前日期 2025-08-16 有 491 条MySQL数据需要处理
2025-08-17 22:32:27,268 - INFO - 日期 2025-08-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 22:32:27,268 - INFO - 开始处理日期: 2025-08-17
2025-08-17 22:32:27,268 - INFO - Request Parameters - Page 1:
2025-08-17 22:32:27,268 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-17 22:32:27,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-17 22:32:27,940 - INFO - Response - Page 1:
2025-08-17 22:32:27,940 - INFO - 第 1 页获取到 19 条记录
2025-08-17 22:32:28,456 - INFO - 查询完成，共获取到 19 条记录
2025-08-17 22:32:28,456 - INFO - 获取到 19 条表单数据
2025-08-17 22:32:28,456 - INFO - 当前日期 2025-08-17 有 19 条MySQL数据需要处理
2025-08-17 22:32:28,456 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 22:32:28,456 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-17 22:32:28,456 - INFO - 同步完成
