2025-08-19 01:30:33,396 - INFO - 使用默认增量同步（当天更新数据）
2025-08-19 01:30:33,396 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-19 01:30:33,396 - INFO - 查询参数: ('2025-08-19',)
2025-08-19 01:30:33,568 - INFO - MySQL查询成功，增量数据（日期: 2025-08-19），共获取 2 条记录
2025-08-19 01:30:33,568 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 01:30:33,568 - INFO - 开始处理日期: 2025-08-18
2025-08-19 01:30:33,568 - INFO - Request Parameters - Page 1:
2025-08-19 01:30:33,568 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 01:30:33,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 01:30:41,677 - ERROR - 处理日期 2025-08-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1C00C0EF-D13E-7F20-BBCF-4169608B03BA Response: {'code': 'ServiceUnavailable', 'requestid': '1C00C0EF-D13E-7F20-BBCF-4169608B03BA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1C00C0EF-D13E-7F20-BBCF-4169608B03BA)
2025-08-19 01:30:41,677 - INFO - 开始处理日期: 2025-08-19
2025-08-19 01:30:41,677 - INFO - Request Parameters - Page 1:
2025-08-19 01:30:41,677 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 01:30:41,677 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 01:30:49,781 - ERROR - 处理日期 2025-08-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EC4B9C21-9E6E-7FE1-8788-49C6FAE3619D Response: {'code': 'ServiceUnavailable', 'requestid': 'EC4B9C21-9E6E-7FE1-8788-49C6FAE3619D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EC4B9C21-9E6E-7FE1-8788-49C6FAE3619D)
2025-08-19 01:30:49,781 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-19 01:31:49,789 - INFO - 开始同步昨天与今天的销售数据: 2025-08-18 至 2025-08-19
2025-08-19 01:31:49,789 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-19 01:31:49,789 - INFO - 查询参数: ('2025-08-18', '2025-08-19')
2025-08-19 01:31:49,945 - INFO - MySQL查询成功，时间段: 2025-08-18 至 2025-08-19，共获取 59 条记录
2025-08-19 01:31:49,945 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 01:31:49,945 - INFO - 开始处理日期: 2025-08-18
2025-08-19 01:31:49,945 - INFO - Request Parameters - Page 1:
2025-08-19 01:31:49,945 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 01:31:49,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 01:31:50,632 - INFO - Response - Page 1:
2025-08-19 01:31:50,632 - INFO - 第 1 页获取到 10 条记录
2025-08-19 01:31:51,148 - INFO - 查询完成，共获取到 10 条记录
2025-08-19 01:31:51,148 - INFO - 获取到 10 条表单数据
2025-08-19 01:31:51,148 - INFO - 当前日期 2025-08-18 有 56 条MySQL数据需要处理
2025-08-19 01:31:51,148 - INFO - 开始批量插入 46 条新记录
2025-08-19 01:31:51,367 - INFO - 批量插入响应状态码: 200
2025-08-19 01:31:51,367 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 18 Aug 2025 17:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2220', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4EFE0902-1816-77CF-BE94-C4C07AEE6263', 'x-acs-trace-id': '37bf7f510d075d37ab8143b5f952a86a', 'etag': '24Go1Q8WPE6gku5Z7hG+RJQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 01:31:51,367 - INFO - 批量插入响应体: {'result': ['FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM0A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM1A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM2A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM3A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM4A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM5A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM6A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM7A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM8A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM9A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMAA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMBA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMCA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMDA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMEA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMFA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMGA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMHA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMIA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMJA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMKA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMLA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMMA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMNA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMOA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMPA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMQA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMRA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMSA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMTA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMUA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMVA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMWA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMXA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMYA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMZA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM0B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM1B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM2B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM3B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM4B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM5B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM6B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM7B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM8B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM9B']}
2025-08-19 01:31:51,367 - INFO - 批量插入表单数据成功，批次 1，共 46 条记录
2025-08-19 01:31:51,367 - INFO - 成功插入的数据ID: ['FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM0A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM1A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM2A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM3A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM4A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM5A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM6A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM7A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM8A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM9A', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMAA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMBA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMCA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMDA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMEA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMFA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMGA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMHA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMIA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMJA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMKA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMLA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMMA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMNA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMOA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMPA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMQA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMRA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMSA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMTA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMUA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMVA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMWA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMXA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMYA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEMZA', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM0B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM1B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM2B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM3B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM4B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM5B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM6B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM7B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM8B', 'FINST-K4B66Q71CS4YPAQ7C8O4M9EYQP3K28QM6EHEM9B']
2025-08-19 01:31:56,382 - INFO - 批量插入完成，共 46 条记录
2025-08-19 01:31:56,382 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 46 条，错误: 0 条
2025-08-19 01:31:56,382 - INFO - 开始处理日期: 2025-08-19
2025-08-19 01:31:56,382 - INFO - Request Parameters - Page 1:
2025-08-19 01:31:56,382 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 01:31:56,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 01:32:04,507 - ERROR - 处理日期 2025-08-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3D4392F2-EBF9-7D3C-8814-5D88FACCA47E Response: {'code': 'ServiceUnavailable', 'requestid': '3D4392F2-EBF9-7D3C-8814-5D88FACCA47E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3D4392F2-EBF9-7D3C-8814-5D88FACCA47E)
2025-08-19 01:32:04,507 - INFO - 数据同步完成！更新: 0 条，插入: 46 条，错误: 1 条
2025-08-19 01:32:04,507 - INFO - 同步完成
2025-08-19 04:30:33,414 - INFO - 使用默认增量同步（当天更新数据）
2025-08-19 04:30:33,414 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-19 04:30:33,414 - INFO - 查询参数: ('2025-08-19',)
2025-08-19 04:30:33,601 - INFO - MySQL查询成功，增量数据（日期: 2025-08-19），共获取 3 条记录
2025-08-19 04:30:33,601 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 04:30:33,601 - INFO - 开始处理日期: 2025-08-18
2025-08-19 04:30:33,601 - INFO - Request Parameters - Page 1:
2025-08-19 04:30:33,601 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 04:30:33,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 04:30:41,726 - ERROR - 处理日期 2025-08-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A8CD4307-5951-7A07-ADDF-0DC678FF7147 Response: {'code': 'ServiceUnavailable', 'requestid': 'A8CD4307-5951-7A07-ADDF-0DC678FF7147', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A8CD4307-5951-7A07-ADDF-0DC678FF7147)
2025-08-19 04:30:41,726 - INFO - 开始处理日期: 2025-08-19
2025-08-19 04:30:41,726 - INFO - Request Parameters - Page 1:
2025-08-19 04:30:41,726 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 04:30:41,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 04:30:47,069 - INFO - Response - Page 1:
2025-08-19 04:30:47,069 - INFO - 查询完成，共获取到 0 条记录
2025-08-19 04:30:47,069 - INFO - 获取到 0 条表单数据
2025-08-19 04:30:47,069 - INFO - 当前日期 2025-08-19 有 1 条MySQL数据需要处理
2025-08-19 04:30:47,069 - INFO - 开始批量插入 1 条新记录
2025-08-19 04:30:47,225 - INFO - 批量插入响应状态码: 200
2025-08-19 04:30:47,225 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 18 Aug 2025 20:30:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '61', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4E4E1EEC-0C74-726D-BFA6-07A58EB5CE1B', 'x-acs-trace-id': 'f9ef71a5d2972df7220a7b7e1262fc19', 'etag': '6O8LM0C5THEaSVCL+RM+gUQ1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 04:30:47,225 - INFO - 批量插入响应体: {'result': ['FINST-AAG66KB1DI2YPNWIFUWSNCRMV7I32R0RKKHEM481']}
2025-08-19 04:30:47,225 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-19 04:30:47,225 - INFO - 成功插入的数据ID: ['FINST-AAG66KB1DI2YPNWIFUWSNCRMV7I32R0RKKHEM481']
2025-08-19 04:30:52,236 - INFO - 批量插入完成，共 1 条记录
2025-08-19 04:30:52,236 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-19 04:30:52,236 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-08-19 04:31:52,243 - INFO - 开始同步昨天与今天的销售数据: 2025-08-18 至 2025-08-19
2025-08-19 04:31:52,243 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-19 04:31:52,243 - INFO - 查询参数: ('2025-08-18', '2025-08-19')
2025-08-19 04:31:52,399 - INFO - MySQL查询成功，时间段: 2025-08-18 至 2025-08-19，共获取 60 条记录
2025-08-19 04:31:52,399 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 04:31:52,399 - INFO - 开始处理日期: 2025-08-18
2025-08-19 04:31:52,399 - INFO - Request Parameters - Page 1:
2025-08-19 04:31:52,399 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 04:31:52,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 04:31:53,274 - INFO - Response - Page 1:
2025-08-19 04:31:53,274 - INFO - 第 1 页获取到 50 条记录
2025-08-19 04:31:53,790 - INFO - Request Parameters - Page 2:
2025-08-19 04:31:53,790 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 04:31:53,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 04:31:54,368 - INFO - Response - Page 2:
2025-08-19 04:31:54,368 - INFO - 第 2 页获取到 6 条记录
2025-08-19 04:31:54,868 - INFO - 查询完成，共获取到 56 条记录
2025-08-19 04:31:54,868 - INFO - 获取到 56 条表单数据
2025-08-19 04:31:54,868 - INFO - 当前日期 2025-08-18 有 57 条MySQL数据需要处理
2025-08-19 04:31:54,868 - INFO - 开始批量插入 1 条新记录
2025-08-19 04:31:55,008 - INFO - 批量插入响应状态码: 200
2025-08-19 04:31:55,008 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Mon, 18 Aug 2025 20:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '20ED3A5E-E384-7042-9536-CD149FA1B92F', 'x-acs-trace-id': '061216bd968b3216e516c76fe77db71e', 'etag': '6LMmE1IobxPpIPUWJC6Gc5Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 04:31:55,008 - INFO - 批量插入响应体: {'result': ['FINST-8P666U91GJ4YPUR5A910TB586G6L3XB7MKHEMR5']}
2025-08-19 04:31:55,008 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-19 04:31:55,008 - INFO - 成功插入的数据ID: ['FINST-8P666U91GJ4YPUR5A910TB586G6L3XB7MKHEMR5']
2025-08-19 04:32:00,023 - INFO - 批量插入完成，共 1 条记录
2025-08-19 04:32:00,023 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-19 04:32:00,023 - INFO - 开始处理日期: 2025-08-19
2025-08-19 04:32:00,023 - INFO - Request Parameters - Page 1:
2025-08-19 04:32:00,023 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 04:32:00,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 04:32:08,179 - ERROR - 处理日期 2025-08-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 80CCE3FA-123C-7992-AEC6-A158A8CCAF7B Response: {'code': 'ServiceUnavailable', 'requestid': '80CCE3FA-123C-7992-AEC6-A158A8CCAF7B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 80CCE3FA-123C-7992-AEC6-A158A8CCAF7B)
2025-08-19 04:32:08,179 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-08-19 04:32:08,179 - INFO - 同步完成
2025-08-19 07:30:33,521 - INFO - 使用默认增量同步（当天更新数据）
2025-08-19 07:30:33,521 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-19 07:30:33,521 - INFO - 查询参数: ('2025-08-19',)
2025-08-19 07:30:33,692 - INFO - MySQL查询成功，增量数据（日期: 2025-08-19），共获取 3 条记录
2025-08-19 07:30:33,692 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 07:30:33,692 - INFO - 开始处理日期: 2025-08-18
2025-08-19 07:30:33,692 - INFO - Request Parameters - Page 1:
2025-08-19 07:30:33,692 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 07:30:33,692 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 07:30:41,802 - ERROR - 处理日期 2025-08-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EE80036F-AD27-7272-8863-0100610EB950 Response: {'code': 'ServiceUnavailable', 'requestid': 'EE80036F-AD27-7272-8863-0100610EB950', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EE80036F-AD27-7272-8863-0100610EB950)
2025-08-19 07:30:41,802 - INFO - 开始处理日期: 2025-08-19
2025-08-19 07:30:41,802 - INFO - Request Parameters - Page 1:
2025-08-19 07:30:41,802 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 07:30:41,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 07:30:49,911 - ERROR - 处理日期 2025-08-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2F7AF16F-0705-75B8-95AE-147B5E6386AD Response: {'code': 'ServiceUnavailable', 'requestid': '2F7AF16F-0705-75B8-95AE-147B5E6386AD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2F7AF16F-0705-75B8-95AE-147B5E6386AD)
2025-08-19 07:30:49,911 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-19 07:31:49,921 - INFO - 开始同步昨天与今天的销售数据: 2025-08-18 至 2025-08-19
2025-08-19 07:31:49,921 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-19 07:31:49,921 - INFO - 查询参数: ('2025-08-18', '2025-08-19')
2025-08-19 07:31:50,093 - INFO - MySQL查询成功，时间段: 2025-08-18 至 2025-08-19，共获取 60 条记录
2025-08-19 07:31:50,093 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 07:31:50,093 - INFO - 开始处理日期: 2025-08-18
2025-08-19 07:31:50,093 - INFO - Request Parameters - Page 1:
2025-08-19 07:31:50,093 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 07:31:50,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 07:31:50,937 - INFO - Response - Page 1:
2025-08-19 07:31:50,937 - INFO - 第 1 页获取到 50 条记录
2025-08-19 07:31:51,437 - INFO - Request Parameters - Page 2:
2025-08-19 07:31:51,437 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 07:31:51,437 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 07:31:52,015 - INFO - Response - Page 2:
2025-08-19 07:31:52,015 - INFO - 第 2 页获取到 7 条记录
2025-08-19 07:31:52,531 - INFO - 查询完成，共获取到 57 条记录
2025-08-19 07:31:52,531 - INFO - 获取到 57 条表单数据
2025-08-19 07:31:52,531 - INFO - 当前日期 2025-08-18 有 57 条MySQL数据需要处理
2025-08-19 07:31:52,531 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 07:31:52,531 - INFO - 开始处理日期: 2025-08-19
2025-08-19 07:31:52,531 - INFO - Request Parameters - Page 1:
2025-08-19 07:31:52,531 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 07:31:52,531 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 07:31:53,156 - INFO - Response - Page 1:
2025-08-19 07:31:53,156 - INFO - 第 1 页获取到 1 条记录
2025-08-19 07:31:53,671 - INFO - 查询完成，共获取到 1 条记录
2025-08-19 07:31:53,671 - INFO - 获取到 1 条表单数据
2025-08-19 07:31:53,671 - INFO - 当前日期 2025-08-19 有 1 条MySQL数据需要处理
2025-08-19 07:31:53,671 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 07:31:53,671 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 07:31:53,671 - INFO - 同步完成
2025-08-19 10:30:33,535 - INFO - 使用默认增量同步（当天更新数据）
2025-08-19 10:30:33,535 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-19 10:30:33,535 - INFO - 查询参数: ('2025-08-19',)
2025-08-19 10:30:33,723 - INFO - MySQL查询成功，增量数据（日期: 2025-08-19），共获取 145 条记录
2025-08-19 10:30:33,723 - INFO - 获取到 3 个日期需要处理: ['2025-08-16', '2025-08-18', '2025-08-19']
2025-08-19 10:30:33,723 - INFO - 开始处理日期: 2025-08-16
2025-08-19 10:30:33,723 - INFO - Request Parameters - Page 1:
2025-08-19 10:30:33,723 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 10:30:33,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 10:30:41,894 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 394D921D-5AAA-792A-BC95-2EF13F3B8B20 Response: {'code': 'ServiceUnavailable', 'requestid': '394D921D-5AAA-792A-BC95-2EF13F3B8B20', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 394D921D-5AAA-792A-BC95-2EF13F3B8B20)
2025-08-19 10:30:41,894 - INFO - 开始处理日期: 2025-08-18
2025-08-19 10:30:41,894 - INFO - Request Parameters - Page 1:
2025-08-19 10:30:41,894 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 10:30:41,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 10:30:50,035 - ERROR - 处理日期 2025-08-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BFF9F062-A035-71B6-8E4D-0E59D9A85D86 Response: {'code': 'ServiceUnavailable', 'requestid': 'BFF9F062-A035-71B6-8E4D-0E59D9A85D86', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BFF9F062-A035-71B6-8E4D-0E59D9A85D86)
2025-08-19 10:30:50,035 - INFO - 开始处理日期: 2025-08-19
2025-08-19 10:30:50,035 - INFO - Request Parameters - Page 1:
2025-08-19 10:30:50,035 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 10:30:50,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 10:30:51,316 - INFO - Response - Page 1:
2025-08-19 10:30:51,316 - INFO - 第 1 页获取到 1 条记录
2025-08-19 10:30:51,832 - INFO - 查询完成，共获取到 1 条记录
2025-08-19 10:30:51,832 - INFO - 获取到 1 条表单数据
2025-08-19 10:30:51,832 - INFO - 当前日期 2025-08-19 有 3 条MySQL数据需要处理
2025-08-19 10:30:51,832 - INFO - 开始批量插入 2 条新记录
2025-08-19 10:30:52,004 - INFO - 批量插入响应状态码: 200
2025-08-19 10:30:52,004 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:30:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '108', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '886C5FA2-527C-736B-BA8D-DA98070E488E', 'x-acs-trace-id': '064110b35209aa65034190392d700ea2', 'etag': '18UsusvASy1eBN2Tqx0v/8Q8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:30:52,004 - INFO - 批量插入响应体: {'result': ['FINST-74766M71JJ4Y5R4MBBTTN4LF7RUQ3XLQFXHEMQ4', 'FINST-74766M71JJ4Y5R4MBBTTN4LF7RUQ3XLQFXHEMR4']}
2025-08-19 10:30:52,004 - INFO - 批量插入表单数据成功，批次 1，共 2 条记录
2025-08-19 10:30:52,004 - INFO - 成功插入的数据ID: ['FINST-74766M71JJ4Y5R4MBBTTN4LF7RUQ3XLQFXHEMQ4', 'FINST-74766M71JJ4Y5R4MBBTTN4LF7RUQ3XLQFXHEMR4']
2025-08-19 10:30:57,019 - INFO - 批量插入完成，共 2 条记录
2025-08-19 10:30:57,019 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 2 条，错误: 0 条
2025-08-19 10:30:57,019 - INFO - 数据同步完成！更新: 0 条，插入: 2 条，错误: 2 条
2025-08-19 10:31:57,030 - INFO - 开始同步昨天与今天的销售数据: 2025-08-18 至 2025-08-19
2025-08-19 10:31:57,030 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-19 10:31:57,030 - INFO - 查询参数: ('2025-08-18', '2025-08-19')
2025-08-19 10:31:57,217 - INFO - MySQL查询成功，时间段: 2025-08-18 至 2025-08-19，共获取 510 条记录
2025-08-19 10:31:57,217 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 10:31:57,217 - INFO - 开始处理日期: 2025-08-18
2025-08-19 10:31:57,217 - INFO - Request Parameters - Page 1:
2025-08-19 10:31:57,217 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 10:31:57,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 10:31:57,998 - INFO - Response - Page 1:
2025-08-19 10:31:57,998 - INFO - 第 1 页获取到 50 条记录
2025-08-19 10:31:58,498 - INFO - Request Parameters - Page 2:
2025-08-19 10:31:58,498 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 10:31:58,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 10:31:59,077 - INFO - Response - Page 2:
2025-08-19 10:31:59,077 - INFO - 第 2 页获取到 7 条记录
2025-08-19 10:31:59,577 - INFO - 查询完成，共获取到 57 条记录
2025-08-19 10:31:59,577 - INFO - 获取到 57 条表单数据
2025-08-19 10:31:59,577 - INFO - 当前日期 2025-08-18 有 488 条MySQL数据需要处理
2025-08-19 10:31:59,577 - INFO - 开始更新记录 - 表单实例ID: FINST-AI8667817M4YFGJUEGQAM79KP4A928K71IGEM8
2025-08-19 10:32:00,186 - INFO - 更新表单数据成功: FINST-AI8667817M4YFGJUEGQAM79KP4A928K71IGEM8
2025-08-19 10:32:00,186 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4122.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 8964.0, 'new_value': 15588.0}, {'field': 'total_amount', 'old_value': 13086.0, 'new_value': 15588.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/2760c8d059e243c1bde6486e5997f168.jpg?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=eK0ySXRxsyTZ5qwXm5Ja41kGS6o%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/ec491a8eeea44221a57d1764b25e81af.jpg?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=j8wl4SaM%2B41CL1z2JF2aYiDEBAQ%3D'}]
2025-08-19 10:32:00,186 - INFO - 开始更新记录 - 表单实例ID: FINST-K4B66Q71121YEJ3F6MVPX77B1FBT33P1RYFEMRF1
2025-08-19 10:32:00,764 - INFO - 更新表单数据成功: FINST-K4B66Q71121YEJ3F6MVPX77B1FBT33P1RYFEMRF1
2025-08-19 10:32:00,764 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3874.89, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 2801.88, 'new_value': 3917.68}, {'field': 'total_amount', 'old_value': 6676.77, 'new_value': 3917.68}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/460c30d095b34baca40c4c7b7270a5e4.jpg?Expires=2070523207&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=XtkzLBHysV5Y3pytr6%2FpUSS%2BvOQ%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/898d720490ba468d8bc6f18d6e3f6e66.jpg?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=n1VnlrIRVHDqrLtG5gfSnzghHHU%3D'}]
2025-08-19 10:32:00,764 - INFO - 开始批量插入 431 条新记录
2025-08-19 10:32:01,030 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:01,030 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '89EF2193-DDF1-7742-AA50-43B12617A08F', 'x-acs-trace-id': 'f9dd8b4ddb8ea20eacf1511c30c44169', 'etag': '2Gw8qKW3PIStfORQc891kYA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:01,030 - INFO - 批量插入响应体: {'result': ['FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMOA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMPA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMQA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMRA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMSA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMTA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMUA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMVA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMWA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMXA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMYA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMZA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM0B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM1B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM2B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM3B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM4B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM5B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM6B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM7B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM8B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM9B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMAB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMBB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMCB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMDB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMEB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMFB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMGB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMHB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMIB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMJB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMKB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMLB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMMB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMNB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMOB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMPB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMQB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMRB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMSB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMTB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMUB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMVB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMWB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMXB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMYB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMZB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM0C', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM1C']}
2025-08-19 10:32:01,030 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-19 10:32:01,030 - INFO - 成功插入的数据ID: ['FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMOA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMPA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMQA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMRA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMSA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMTA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMUA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMVA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMWA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMXA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMYA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMZA', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM0B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM1B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM2B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM3B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM4B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM5B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM6B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM7B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM8B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM9B', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMAB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMBB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMCB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMDB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMEB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMFB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMGB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMHB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMIB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMJB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMKB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMLB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMMB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMNB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMOB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMPB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMQB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMRB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMSB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMTB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMUB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMVB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMWB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMXB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMYB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMZB', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM0C', 'FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEM1C']
2025-08-19 10:32:06,290 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:06,290 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C3EC6874-2BCC-7296-808B-C1D4E6D6DFDE', 'x-acs-trace-id': 'c7217c449bada016f04b23f0b0b34eb2', 'etag': '215G2QUf2JcOks3O+lwBmVA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:06,290 - INFO - 批量插入响应体: {'result': ['FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMU2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMV2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMW2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMX2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMY2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMZ2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM03', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM13', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM23', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM33', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM43', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM53', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM63', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM73', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM83', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM93', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMA3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMB3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMC3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMD3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEME3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMF3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMG3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMH3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMI3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMJ3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMK3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEML3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMM3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMN3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMO3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMP3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMQ3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMR3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMS3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMT3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMU3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMV3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMW3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMX3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMY3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMZ3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM04', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM14', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM24', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM34', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM44', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM54', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM64', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM74']}
2025-08-19 10:32:06,290 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-19 10:32:06,290 - INFO - 成功插入的数据ID: ['FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMU2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMV2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMW2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMX2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMY2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMZ2', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM03', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM13', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM23', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM33', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM43', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM53', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM63', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM73', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM83', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM93', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMA3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMB3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMC3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMD3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEME3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMF3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMG3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMH3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMI3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMJ3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMK3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEML3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMM3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMN3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMO3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMP3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMQ3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMR3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMS3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMT3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMU3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMV3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMW3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMX3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMY3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMZ3', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM04', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM14', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM24', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM34', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM44', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM54', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM64', 'FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEM74']
2025-08-19 10:32:11,540 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:11,540 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:32:09 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '36C44DA8-0F27-7799-B9A1-EE96C19F5805', 'x-acs-trace-id': 'e0e544fce213ff9282da62fbf4a27dd4', 'etag': '25JunZmD3o0Y96GJFjEw0SQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:11,540 - INFO - 批量插入响应体: {'result': ['FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMKS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMLS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMMS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMNS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMOS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMPS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMQS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMRS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMSS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMTS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMUS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMVS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMWS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMXS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMYS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMZS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM0T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM1T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM2T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM3T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM4T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM5T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM6T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM7T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM8T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM9T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMAT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMBT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMCT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMDT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMET1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMFT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMGT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMHT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMIT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMJT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMKT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMLT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMMT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMNT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMOT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMPT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMQT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMRT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMST1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMTT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMUT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMVT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMWT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMXT1']}
2025-08-19 10:32:11,540 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-19 10:32:11,540 - INFO - 成功插入的数据ID: ['FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMKS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMLS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMMS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMNS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMOS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMPS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMQS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMRS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMSS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMTS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMUS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMVS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMWS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMXS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMYS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMZS1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM0T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM1T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM2T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM3T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM4T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM5T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM6T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM7T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM8T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEM9T1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMAT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMBT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMCT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMDT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMET1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMFT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMGT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMHT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMIT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMJT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMKT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMLT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMMT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMNT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMOT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMPT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMQT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMRT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMST1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMTT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMUT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMVT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMWT1', 'FINST-SFD66081OH1YGI849N2LSB30FEAM39ZFHXHEMXT1']
2025-08-19 10:32:16,775 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:16,775 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:32:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2407', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BDCC8286-C9AE-779F-9673-BD9813519D56', 'x-acs-trace-id': '788e1947392769441cb282931dd6a09d', 'etag': '2cJYaz2czPMuTOvTT1cxLQQ7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:16,775 - INFO - 批量插入响应体: {'result': ['FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMV', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMW', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMX', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMY', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMZ', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM01', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM11', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM21', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM31', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM41', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM51', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM61', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM71', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM81', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM91', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMA1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMB1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMC1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMD1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEME1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMF1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMG1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMH1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMI1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMJ1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMK1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEML1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMM1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMN1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMO1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMP1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMQ1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMR1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMS1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMT1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMU1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMV1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMW1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMX1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMY1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMZ1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM02', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM12', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM22', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM32', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM42', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM52', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM62', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM72', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM82']}
2025-08-19 10:32:16,775 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-08-19 10:32:16,775 - INFO - 成功插入的数据ID: ['FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMV', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMW', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMX', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMY', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMZ', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM01', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM11', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM21', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM31', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM41', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM51', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM61', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM71', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM81', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM91', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMA1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMB1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMC1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMD1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEME1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMF1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMG1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMH1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMI1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMJ1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMK1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEML1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMM1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMN1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMO1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMP1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMQ1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMR1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMS1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMT1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMU1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMV1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMW1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMX1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMY1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEMZ1', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM02', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM12', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM22', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM32', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM42', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM52', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM62', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM72', 'FINST-6FC66GB1CZ4YXP7PARJ9E7YQC41W2T0KHXHEM82']
2025-08-19 10:32:22,040 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:22,040 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '51D6C3A0-3AC6-77DE-8C5B-39DE9790C4E1', 'x-acs-trace-id': '0b4abad9cd7f8ace5b45cee9589baf8e', 'etag': '2rtfh8dkhsldexNylaMcp7Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:22,040 - INFO - 批量插入响应体: {'result': ['FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMK3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEML3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMM3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMN3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMO3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMP3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMQ3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMR3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMS3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMT3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMU3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMV3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMW3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMX3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMY3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMZ3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM04', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM14', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM24', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM34', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM44', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM54', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM64', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM74', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM84', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM94', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMA4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMB4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMC4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMD4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEME4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMF4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMG4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMH4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMI4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMJ4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMK4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEML4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMM4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMN4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMO4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMP4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMQ4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMR4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMS4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMT4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMU4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMV4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMW4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMX4']}
2025-08-19 10:32:22,040 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-08-19 10:32:22,040 - INFO - 成功插入的数据ID: ['FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMK3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEML3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMM3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMN3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMO3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMP3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMQ3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMR3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMS3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMT3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMU3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMV3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMW3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMX3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMY3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMZ3', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM04', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM14', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM24', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM34', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM44', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM54', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM64', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM74', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM84', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEM94', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMA4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3Q2OHXHEMB4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMC4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMD4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEME4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMF4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMG4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMH4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMI4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMJ4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMK4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEML4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMM4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMN4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMO4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMP4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMQ4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMR4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMS4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMT4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMU4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMV4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMW4', 'FINST-YQ966PD1NK4YP6RJFAQR17847AUW3R2OHXHEMX4']
2025-08-19 10:32:27,259 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:27,259 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2382', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3A58D552-D7FE-7129-B6A6-FCC23B8F1D47', 'x-acs-trace-id': '6c7cad7fb500195a79b947fe98736faf', 'etag': '2GvwZ3aYBnuD3iFkuou0OOg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:27,259 - INFO - 批量插入响应体: {'result': ['FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM6', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM7', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM8', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM9', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMA', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMB', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMC', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMD', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEME', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMF', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMG', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMH', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMI', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMJ', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMK', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEML', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMM', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMN', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMO', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMP', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMQ', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMR', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMS', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMT', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMU', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMV', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMW', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMX', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMY', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMZ', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM01', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM11', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM21', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM31', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM41', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM51', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM61', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM71', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM81', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM91', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMA1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMB1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMC1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMD1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEME1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMF1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMG1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMH1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMI1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMJ1']}
2025-08-19 10:32:27,259 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-08-19 10:32:27,259 - INFO - 成功插入的数据ID: ['FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM6', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM7', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM8', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM9', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMA', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMB', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMC', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMD', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEME', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMF', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMG', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMH', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMI', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMJ', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMK', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEML', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMM', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMN', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMO', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMP', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMQ', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMR', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMS', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMT', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMU', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMV', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMW', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMX', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMY', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMZ', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM01', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM11', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM21', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM31', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM41', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM51', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM61', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM71', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM81', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEM91', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMA1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMB1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMC1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMD1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEME1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMF1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMG1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMH1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMI1', 'FINST-NYC66LB1PL5YSXYXB6RUKD8JK0ZX304SHXHEMJ1']
2025-08-19 10:32:32,478 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:32,478 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F197B836-CD19-7248-9C3F-289546766A92', 'x-acs-trace-id': '5165a2023348d2ee84c72f956027011d', 'etag': '2TlI5eTn88vLVbYO96Rw6og2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:32,478 - INFO - 批量插入响应体: {'result': ['FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM3J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM4J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM5J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM6J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM7J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM8J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM9J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMAJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMBJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMCJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMDJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMEJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMFJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMGJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMHJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMIJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMJJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMKJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMLJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMMJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMNJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMOJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMPJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMQJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMRJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMSJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMTJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMUJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMVJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMWJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMXJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMYJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMZJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM0K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM1K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM2K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM3K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM4K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM5K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM6K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM7K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM8K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM9K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMAK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMBK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMCK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMDK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMEK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMFK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMGK']}
2025-08-19 10:32:32,478 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-08-19 10:32:32,478 - INFO - 成功插入的数据ID: ['FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM3J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM4J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM5J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM6J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM7J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM8J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM9J', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMAJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMBJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMCJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMDJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMEJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMFJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMGJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMHJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMIJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMJJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMKJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMLJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMMJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMNJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMOJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMPJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMQJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMRJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMSJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMTJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMUJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMVJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMWJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMXJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMYJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMZJ', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM0K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM1K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM2K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM3K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM4K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM5K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM6K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM7K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM8K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEM9K', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMAK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3R4WHXHEMBK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMCK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMDK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMEK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMFK', 'FINST-NU966I81AN4Y23IKBTLAR800S33O3S4WHXHEMGK']
2025-08-19 10:32:37,728 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:37,728 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:32:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '598F3950-AA33-7030-9B88-1D41D983283C', 'x-acs-trace-id': 'a2d6b167e3c66b196f8ff9a681b792f8', 'etag': '2HvcsPeZDSUjE2m8XV8M1Eg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:37,728 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMO5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMP5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMQ5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMR5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMS5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMT5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMU5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMV5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMW5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMX5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMY5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMZ5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM06', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM16', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM26', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM36', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM46', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM56', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM66', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM76', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM86', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM96', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMA6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMB6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMC6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMD6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEME6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMF6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMG6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMH6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMI6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMJ6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMK6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEML6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMM6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMN6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMO6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMP6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMQ6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMR6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMS6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMT6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMU6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMV6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMW6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMX6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMY6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMZ6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEM07', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEM17']}
2025-08-19 10:32:37,728 - INFO - 批量插入表单数据成功，批次 8，共 50 条记录
2025-08-19 10:32:37,728 - INFO - 成功插入的数据ID: ['FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMO5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMP5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMQ5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMR5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMS5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMT5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMU5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMV5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMW5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMX5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMY5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMZ5', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM06', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM16', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM26', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM36', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM46', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM56', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM66', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM76', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM86', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEM96', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMA6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMB6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMC6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMD6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEME6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMF6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMG6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMH6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMI6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMJ6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMK6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEML6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMM6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMN6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMO6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMP6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMQ6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMR6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMS6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMT6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMU6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMV6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMW6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMX6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMY6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEMZ6', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEM07', 'FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3N60IXHEM17']
2025-08-19 10:32:42,962 - INFO - 批量插入响应状态码: 200
2025-08-19 10:32:42,962 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 02:32:40 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1500', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2192311C-6041-7127-B7F5-8A2D42AD0BAA', 'x-acs-trace-id': '940e56a86f2f87365ac14d6c24c8b0b2', 'etag': '119lY+kGXbN1oDhQJnSu1yw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 10:32:42,962 - INFO - 批量插入响应体: {'result': ['FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM56', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM66', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM76', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM86', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM96', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMA6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMB6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMC6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMD6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEME6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMF6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMG6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMH6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMI6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMJ6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMK6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEML6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMM6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMN6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMO6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMP6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMQ6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMR6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMS6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMT6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMU6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMV6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMW6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMX6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMY6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMZ6']}
2025-08-19 10:32:42,962 - INFO - 批量插入表单数据成功，批次 9，共 31 条记录
2025-08-19 10:32:42,962 - INFO - 成功插入的数据ID: ['FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM56', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM66', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM76', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM86', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEM96', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMA6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMB6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMC6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMD6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEME6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMF6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMG6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMH6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMI6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMJ6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMK6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEML6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMM6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMN6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMO6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMP6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMQ6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMR6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMS6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMT6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMU6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMV6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMW6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMX6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMY6', 'FINST-AVF66WB12O4Y1ALBF43MZ6QZYEDD3884IXHEMZ6']
2025-08-19 10:32:47,978 - INFO - 批量插入完成，共 431 条记录
2025-08-19 10:32:47,978 - INFO - 日期 2025-08-18 处理完成 - 更新: 2 条，插入: 431 条，错误: 0 条
2025-08-19 10:32:47,978 - INFO - 开始处理日期: 2025-08-19
2025-08-19 10:32:47,978 - INFO - Request Parameters - Page 1:
2025-08-19 10:32:47,978 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 10:32:47,978 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 10:32:48,493 - INFO - Response - Page 1:
2025-08-19 10:32:48,493 - INFO - 第 1 页获取到 3 条记录
2025-08-19 10:32:49,009 - INFO - 查询完成，共获取到 3 条记录
2025-08-19 10:32:49,009 - INFO - 获取到 3 条表单数据
2025-08-19 10:32:49,009 - INFO - 当前日期 2025-08-19 有 3 条MySQL数据需要处理
2025-08-19 10:32:49,009 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 10:32:49,009 - INFO - 数据同步完成！更新: 2 条，插入: 431 条，错误: 0 条
2025-08-19 10:32:49,009 - INFO - 同步完成
2025-08-19 13:30:33,487 - INFO - 使用默认增量同步（当天更新数据）
2025-08-19 13:30:33,487 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-19 13:30:33,487 - INFO - 查询参数: ('2025-08-19',)
2025-08-19 13:30:33,675 - INFO - MySQL查询成功，增量数据（日期: 2025-08-19），共获取 153 条记录
2025-08-19 13:30:33,675 - INFO - 获取到 4 个日期需要处理: ['2025-08-16', '2025-08-17', '2025-08-18', '2025-08-19']
2025-08-19 13:30:33,675 - INFO - 开始处理日期: 2025-08-16
2025-08-19 13:30:33,675 - INFO - Request Parameters - Page 1:
2025-08-19 13:30:33,675 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:30:33,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:30:41,800 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 856077F6-F343-7CEE-8BE9-D29FB967B615 Response: {'code': 'ServiceUnavailable', 'requestid': '856077F6-F343-7CEE-8BE9-D29FB967B615', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 856077F6-F343-7CEE-8BE9-D29FB967B615)
2025-08-19 13:30:41,800 - INFO - 开始处理日期: 2025-08-17
2025-08-19 13:30:41,800 - INFO - Request Parameters - Page 1:
2025-08-19 13:30:41,800 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:30:41,800 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:30:49,753 - INFO - Response - Page 1:
2025-08-19 13:30:49,753 - INFO - 第 1 页获取到 50 条记录
2025-08-19 13:30:50,268 - INFO - Request Parameters - Page 2:
2025-08-19 13:30:50,268 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:30:50,268 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:30:58,378 - ERROR - 处理日期 2025-08-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C9E0CB6C-F54A-7048-866F-5AEA82F7B582 Response: {'code': 'ServiceUnavailable', 'requestid': 'C9E0CB6C-F54A-7048-866F-5AEA82F7B582', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C9E0CB6C-F54A-7048-866F-5AEA82F7B582)
2025-08-19 13:30:58,378 - INFO - 开始处理日期: 2025-08-18
2025-08-19 13:30:58,378 - INFO - Request Parameters - Page 1:
2025-08-19 13:30:58,378 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:30:58,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:00,081 - INFO - Response - Page 1:
2025-08-19 13:31:00,081 - INFO - 第 1 页获取到 50 条记录
2025-08-19 13:31:00,596 - INFO - Request Parameters - Page 2:
2025-08-19 13:31:00,596 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:00,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:01,331 - INFO - Response - Page 2:
2025-08-19 13:31:01,331 - INFO - 第 2 页获取到 50 条记录
2025-08-19 13:31:01,831 - INFO - Request Parameters - Page 3:
2025-08-19 13:31:01,831 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:01,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:02,581 - INFO - Response - Page 3:
2025-08-19 13:31:02,581 - INFO - 第 3 页获取到 50 条记录
2025-08-19 13:31:03,096 - INFO - Request Parameters - Page 4:
2025-08-19 13:31:03,096 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:03,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:03,831 - INFO - Response - Page 4:
2025-08-19 13:31:03,831 - INFO - 第 4 页获取到 50 条记录
2025-08-19 13:31:04,346 - INFO - Request Parameters - Page 5:
2025-08-19 13:31:04,346 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:04,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:05,143 - INFO - Response - Page 5:
2025-08-19 13:31:05,143 - INFO - 第 5 页获取到 50 条记录
2025-08-19 13:31:05,659 - INFO - Request Parameters - Page 6:
2025-08-19 13:31:05,659 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:05,659 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:06,466 - INFO - Response - Page 6:
2025-08-19 13:31:06,466 - INFO - 第 6 页获取到 50 条记录
2025-08-19 13:31:06,982 - INFO - Request Parameters - Page 7:
2025-08-19 13:31:06,982 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:06,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:07,716 - INFO - Response - Page 7:
2025-08-19 13:31:07,716 - INFO - 第 7 页获取到 50 条记录
2025-08-19 13:31:08,232 - INFO - Request Parameters - Page 8:
2025-08-19 13:31:08,232 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:08,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:08,998 - INFO - Response - Page 8:
2025-08-19 13:31:08,998 - INFO - 第 8 页获取到 50 条记录
2025-08-19 13:31:09,513 - INFO - Request Parameters - Page 9:
2025-08-19 13:31:09,513 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:09,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:10,216 - INFO - Response - Page 9:
2025-08-19 13:31:10,216 - INFO - 第 9 页获取到 50 条记录
2025-08-19 13:31:10,732 - INFO - Request Parameters - Page 10:
2025-08-19 13:31:10,732 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:10,732 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:11,498 - INFO - Response - Page 10:
2025-08-19 13:31:11,498 - INFO - 第 10 页获取到 38 条记录
2025-08-19 13:31:12,013 - INFO - 查询完成，共获取到 488 条记录
2025-08-19 13:31:12,013 - INFO - 获取到 488 条表单数据
2025-08-19 13:31:12,013 - INFO - 当前日期 2025-08-18 有 143 条MySQL数据需要处理
2025-08-19 13:31:12,013 - INFO - 开始更新记录 - 表单实例ID: FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMBB
2025-08-19 13:31:12,669 - INFO - 更新表单数据成功: FINST-BTF66DB12M4YC3LG90QF743KHSXB3IV7HXHEMBB
2025-08-19 13:31:12,669 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6500.0, 'new_value': 16500.0}, {'field': 'total_amount', 'old_value': 6500.0, 'new_value': 16500.0}, {'field': 'order_count', 'old_value': 1, 'new_value': 3}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-19 13:31:12,669 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMQ6
2025-08-19 13:31:13,248 - INFO - 更新表单数据成功: FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMQ6
2025-08-19 13:31:13,248 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 129.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 129.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-19 13:31:13,248 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMN6
2025-08-19 13:31:13,888 - INFO - 更新表单数据成功: FINST-K7666JC12M4Y52ZG73JJL6NGSX5H3M60IXHEMN6
2025-08-19 13:31:13,888 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 25421.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 25421.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 58}]
2025-08-19 13:31:13,888 - INFO - 开始批量插入 4 条新记录
2025-08-19 13:31:14,060 - INFO - 批量插入响应状态码: 200
2025-08-19 13:31:14,060 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 05:31:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EE0F6FE1-3503-75D0-8DE5-6F0DEAF5B5B4', 'x-acs-trace-id': 'eddb136fef0e6732707587119bd5cf5c', 'etag': '2M7BctNUgZTCGjX6FAOWF7w4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 13:31:14,060 - INFO - 批量插入响应体: {'result': ['FINST-0O9664D11L4YMD64AL6R7D8FWF7V3T0PV3IEMV6', 'FINST-0O9664D11L4YMD64AL6R7D8FWF7V3T0PV3IEMW6', 'FINST-0O9664D11L4YMD64AL6R7D8FWF7V3T0PV3IEMX6', 'FINST-0O9664D11L4YMD64AL6R7D8FWF7V3T0PV3IEMY6']}
2025-08-19 13:31:14,060 - INFO - 批量插入表单数据成功，批次 1，共 4 条记录
2025-08-19 13:31:14,060 - INFO - 成功插入的数据ID: ['FINST-0O9664D11L4YMD64AL6R7D8FWF7V3T0PV3IEMV6', 'FINST-0O9664D11L4YMD64AL6R7D8FWF7V3T0PV3IEMW6', 'FINST-0O9664D11L4YMD64AL6R7D8FWF7V3T0PV3IEMX6', 'FINST-0O9664D11L4YMD64AL6R7D8FWF7V3T0PV3IEMY6']
2025-08-19 13:31:19,076 - INFO - 批量插入完成，共 4 条记录
2025-08-19 13:31:19,076 - INFO - 日期 2025-08-18 处理完成 - 更新: 3 条，插入: 4 条，错误: 0 条
2025-08-19 13:31:19,076 - INFO - 开始处理日期: 2025-08-19
2025-08-19 13:31:19,076 - INFO - Request Parameters - Page 1:
2025-08-19 13:31:19,076 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:31:19,076 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:31:19,638 - INFO - Response - Page 1:
2025-08-19 13:31:19,638 - INFO - 第 1 页获取到 3 条记录
2025-08-19 13:31:20,154 - INFO - 查询完成，共获取到 3 条记录
2025-08-19 13:31:20,154 - INFO - 获取到 3 条表单数据
2025-08-19 13:31:20,154 - INFO - 当前日期 2025-08-19 有 3 条MySQL数据需要处理
2025-08-19 13:31:20,154 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 13:31:20,154 - INFO - 数据同步完成！更新: 3 条，插入: 4 条，错误: 2 条
2025-08-19 13:32:20,164 - INFO - 开始同步昨天与今天的销售数据: 2025-08-18 至 2025-08-19
2025-08-19 13:32:20,164 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-19 13:32:20,164 - INFO - 查询参数: ('2025-08-18', '2025-08-19')
2025-08-19 13:32:20,336 - INFO - MySQL查询成功，时间段: 2025-08-18 至 2025-08-19，共获取 516 条记录
2025-08-19 13:32:20,352 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 13:32:20,352 - INFO - 开始处理日期: 2025-08-18
2025-08-19 13:32:20,352 - INFO - Request Parameters - Page 1:
2025-08-19 13:32:20,352 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:20,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:21,195 - INFO - Response - Page 1:
2025-08-19 13:32:21,195 - INFO - 第 1 页获取到 50 条记录
2025-08-19 13:32:21,711 - INFO - Request Parameters - Page 2:
2025-08-19 13:32:21,711 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:21,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:22,445 - INFO - Response - Page 2:
2025-08-19 13:32:22,445 - INFO - 第 2 页获取到 50 条记录
2025-08-19 13:32:22,961 - INFO - Request Parameters - Page 3:
2025-08-19 13:32:22,961 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:22,961 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:23,711 - INFO - Response - Page 3:
2025-08-19 13:32:23,711 - INFO - 第 3 页获取到 50 条记录
2025-08-19 13:32:24,227 - INFO - Request Parameters - Page 4:
2025-08-19 13:32:24,227 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:24,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:24,961 - INFO - Response - Page 4:
2025-08-19 13:32:24,961 - INFO - 第 4 页获取到 50 条记录
2025-08-19 13:32:25,477 - INFO - Request Parameters - Page 5:
2025-08-19 13:32:25,477 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:25,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:26,258 - INFO - Response - Page 5:
2025-08-19 13:32:26,258 - INFO - 第 5 页获取到 50 条记录
2025-08-19 13:32:26,758 - INFO - Request Parameters - Page 6:
2025-08-19 13:32:26,758 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:26,758 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:27,461 - INFO - Response - Page 6:
2025-08-19 13:32:27,461 - INFO - 第 6 页获取到 50 条记录
2025-08-19 13:32:27,977 - INFO - Request Parameters - Page 7:
2025-08-19 13:32:27,977 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:27,977 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:28,773 - INFO - Response - Page 7:
2025-08-19 13:32:28,773 - INFO - 第 7 页获取到 50 条记录
2025-08-19 13:32:29,289 - INFO - Request Parameters - Page 8:
2025-08-19 13:32:29,289 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:29,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:30,070 - INFO - Response - Page 8:
2025-08-19 13:32:30,070 - INFO - 第 8 页获取到 50 条记录
2025-08-19 13:32:30,570 - INFO - Request Parameters - Page 9:
2025-08-19 13:32:30,570 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:30,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:31,320 - INFO - Response - Page 9:
2025-08-19 13:32:31,320 - INFO - 第 9 页获取到 50 条记录
2025-08-19 13:32:31,836 - INFO - Request Parameters - Page 10:
2025-08-19 13:32:31,836 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:31,836 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:32,555 - INFO - Response - Page 10:
2025-08-19 13:32:32,555 - INFO - 第 10 页获取到 42 条记录
2025-08-19 13:32:33,070 - INFO - 查询完成，共获取到 492 条记录
2025-08-19 13:32:33,070 - INFO - 获取到 492 条表单数据
2025-08-19 13:32:33,070 - INFO - 当前日期 2025-08-18 有 493 条MySQL数据需要处理
2025-08-19 13:32:33,086 - INFO - 开始批量插入 1 条新记录
2025-08-19 13:32:33,289 - INFO - 批量插入响应状态码: 200
2025-08-19 13:32:33,289 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 05:32:30 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '42F6B996-E2A0-7C84-B758-D793640050E7', 'x-acs-trace-id': '64fb2bad1662c18f3c780ccdcdf71500', 'etag': '6ksRcLh1wb6x+bngZYzNKaQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 13:32:33,289 - INFO - 批量插入响应体: {'result': ['FINST-LLF66J71EN4Y6U8W9N2NA6CH1OX83N5EX3IEM6F']}
2025-08-19 13:32:33,289 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-19 13:32:33,289 - INFO - 成功插入的数据ID: ['FINST-LLF66J71EN4Y6U8W9N2NA6CH1OX83N5EX3IEM6F']
2025-08-19 13:32:38,305 - INFO - 批量插入完成，共 1 条记录
2025-08-19 13:32:38,305 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-19 13:32:38,305 - INFO - 开始处理日期: 2025-08-19
2025-08-19 13:32:38,305 - INFO - Request Parameters - Page 1:
2025-08-19 13:32:38,305 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 13:32:38,305 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 13:32:38,867 - INFO - Response - Page 1:
2025-08-19 13:32:38,867 - INFO - 第 1 页获取到 3 条记录
2025-08-19 13:32:39,367 - INFO - 查询完成，共获取到 3 条记录
2025-08-19 13:32:39,367 - INFO - 获取到 3 条表单数据
2025-08-19 13:32:39,367 - INFO - 当前日期 2025-08-19 有 3 条MySQL数据需要处理
2025-08-19 13:32:39,367 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 13:32:39,367 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-08-19 13:32:39,367 - INFO - 同步完成
2025-08-19 16:30:33,548 - INFO - 使用默认增量同步（当天更新数据）
2025-08-19 16:30:33,548 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-19 16:30:33,548 - INFO - 查询参数: ('2025-08-19',)
2025-08-19 16:30:33,720 - INFO - MySQL查询成功，增量数据（日期: 2025-08-19），共获取 170 条记录
2025-08-19 16:30:33,720 - INFO - 获取到 4 个日期需要处理: ['2025-08-16', '2025-08-17', '2025-08-18', '2025-08-19']
2025-08-19 16:30:33,720 - INFO - 开始处理日期: 2025-08-16
2025-08-19 16:30:33,736 - INFO - Request Parameters - Page 1:
2025-08-19 16:30:33,736 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:33,736 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:41,845 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 30EED46A-2224-7B07-8487-B551736B135B Response: {'code': 'ServiceUnavailable', 'requestid': '30EED46A-2224-7B07-8487-B551736B135B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 30EED46A-2224-7B07-8487-B551736B135B)
2025-08-19 16:30:41,845 - INFO - 开始处理日期: 2025-08-17
2025-08-19 16:30:41,845 - INFO - Request Parameters - Page 1:
2025-08-19 16:30:41,845 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:41,845 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:49,970 - ERROR - 处理日期 2025-08-17 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 633A10BD-1C1B-7F46-9F57-D6315DCABEBB Response: {'code': 'ServiceUnavailable', 'requestid': '633A10BD-1C1B-7F46-9F57-D6315DCABEBB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 633A10BD-1C1B-7F46-9F57-D6315DCABEBB)
2025-08-19 16:30:49,970 - INFO - 开始处理日期: 2025-08-18
2025-08-19 16:30:49,970 - INFO - Request Parameters - Page 1:
2025-08-19 16:30:49,970 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:49,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:51,861 - INFO - Response - Page 1:
2025-08-19 16:30:51,861 - INFO - 第 1 页获取到 50 条记录
2025-08-19 16:30:52,361 - INFO - Request Parameters - Page 2:
2025-08-19 16:30:52,361 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:52,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:53,283 - INFO - Response - Page 2:
2025-08-19 16:30:53,283 - INFO - 第 2 页获取到 50 条记录
2025-08-19 16:30:53,798 - INFO - Request Parameters - Page 3:
2025-08-19 16:30:53,798 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:53,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:54,501 - INFO - Response - Page 3:
2025-08-19 16:30:54,501 - INFO - 第 3 页获取到 50 条记录
2025-08-19 16:30:55,001 - INFO - Request Parameters - Page 4:
2025-08-19 16:30:55,001 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:55,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:55,783 - INFO - Response - Page 4:
2025-08-19 16:30:55,783 - INFO - 第 4 页获取到 50 条记录
2025-08-19 16:30:56,298 - INFO - Request Parameters - Page 5:
2025-08-19 16:30:56,298 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:56,298 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:57,158 - INFO - Response - Page 5:
2025-08-19 16:30:57,158 - INFO - 第 5 页获取到 50 条记录
2025-08-19 16:30:57,658 - INFO - Request Parameters - Page 6:
2025-08-19 16:30:57,658 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:57,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:58,423 - INFO - Response - Page 6:
2025-08-19 16:30:58,423 - INFO - 第 6 页获取到 50 条记录
2025-08-19 16:30:58,939 - INFO - Request Parameters - Page 7:
2025-08-19 16:30:58,939 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:30:58,939 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:30:59,689 - INFO - Response - Page 7:
2025-08-19 16:30:59,689 - INFO - 第 7 页获取到 50 条记录
2025-08-19 16:31:00,205 - INFO - Request Parameters - Page 8:
2025-08-19 16:31:00,205 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:31:00,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:31:01,001 - INFO - Response - Page 8:
2025-08-19 16:31:01,001 - INFO - 第 8 页获取到 50 条记录
2025-08-19 16:31:01,501 - INFO - Request Parameters - Page 9:
2025-08-19 16:31:01,501 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:31:01,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:31:02,283 - INFO - Response - Page 9:
2025-08-19 16:31:02,283 - INFO - 第 9 页获取到 50 条记录
2025-08-19 16:31:02,798 - INFO - Request Parameters - Page 10:
2025-08-19 16:31:02,798 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:31:02,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:31:03,548 - INFO - Response - Page 10:
2025-08-19 16:31:03,548 - INFO - 第 10 页获取到 43 条记录
2025-08-19 16:31:04,048 - INFO - 查询完成，共获取到 493 条记录
2025-08-19 16:31:04,048 - INFO - 获取到 493 条表单数据
2025-08-19 16:31:04,048 - INFO - 当前日期 2025-08-18 有 157 条MySQL数据需要处理
2025-08-19 16:31:04,048 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMO3
2025-08-19 16:31:04,517 - INFO - 更新表单数据成功: FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMO3
2025-08-19 16:31:04,517 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 41.0, 'new_value': 7124.0}, {'field': 'total_amount', 'old_value': 41.0, 'new_value': 7124.0}, {'field': 'order_count', 'old_value': 7125, 'new_value': 41}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-19 16:31:04,517 - INFO - 开始批量插入 13 条新记录
2025-08-19 16:31:04,658 - INFO - 批量插入响应状态码: 200
2025-08-19 16:31:04,658 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 08:31:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0D9C2EE6-EB04-7464-9533-FEE2AD73A975', 'x-acs-trace-id': 'f92aded94a8761a6bd746868e03d8796', 'etag': '6KHwVMuc2UXueRnT4Em6+sA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 16:31:04,658 - INFO - 批量插入响应体: {'result': ['FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMBB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMCB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMDB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMEB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMFB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMGB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMHB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMIB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMJB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMKB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMLB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMMB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMNB']}
2025-08-19 16:31:04,658 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-08-19 16:31:04,658 - INFO - 成功插入的数据ID: ['FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMBB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMCB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMDB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMEB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMFB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMGB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMHB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMIB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMJB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMKB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMLB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMMB', 'FINST-L4E66Y61105YNTOICUDI38EZ266F3X5ZAAIEMNB']
2025-08-19 16:31:09,673 - INFO - 批量插入完成，共 13 条记录
2025-08-19 16:31:09,673 - INFO - 日期 2025-08-18 处理完成 - 更新: 1 条，插入: 13 条，错误: 0 条
2025-08-19 16:31:09,673 - INFO - 开始处理日期: 2025-08-19
2025-08-19 16:31:09,673 - INFO - Request Parameters - Page 1:
2025-08-19 16:31:09,673 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:31:09,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:31:10,204 - INFO - Response - Page 1:
2025-08-19 16:31:10,204 - INFO - 第 1 页获取到 3 条记录
2025-08-19 16:31:10,715 - INFO - 查询完成，共获取到 3 条记录
2025-08-19 16:31:10,715 - INFO - 获取到 3 条表单数据
2025-08-19 16:31:10,715 - INFO - 当前日期 2025-08-19 有 4 条MySQL数据需要处理
2025-08-19 16:31:10,715 - INFO - 开始批量插入 1 条新记录
2025-08-19 16:31:10,856 - INFO - 批量插入响应状态码: 200
2025-08-19 16:31:10,856 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 08:31:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8904E009-D4EF-79C3-8DDC-C3DD8B83360F', 'x-acs-trace-id': '717a0c87e13fe2b97ec19fd227d46a4f', 'etag': '62u4qR4VFXpp8kk4ALzdwUQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 16:31:10,856 - INFO - 批量插入响应体: {'result': ['FINST-QUA66S71UM4Y6ZKC69FLV60V4X9A32Y3BAIEMQD']}
2025-08-19 16:31:10,856 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-19 16:31:10,856 - INFO - 成功插入的数据ID: ['FINST-QUA66S71UM4Y6ZKC69FLV60V4X9A32Y3BAIEMQD']
2025-08-19 16:31:15,871 - INFO - 批量插入完成，共 1 条记录
2025-08-19 16:31:15,871 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-19 16:31:15,871 - INFO - 数据同步完成！更新: 1 条，插入: 14 条，错误: 2 条
2025-08-19 16:32:15,882 - INFO - 开始同步昨天与今天的销售数据: 2025-08-18 至 2025-08-19
2025-08-19 16:32:15,882 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-19 16:32:15,882 - INFO - 查询参数: ('2025-08-18', '2025-08-19')
2025-08-19 16:32:16,069 - INFO - MySQL查询成功，时间段: 2025-08-18 至 2025-08-19，共获取 554 条记录
2025-08-19 16:32:16,069 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 16:32:16,069 - INFO - 开始处理日期: 2025-08-18
2025-08-19 16:32:16,069 - INFO - Request Parameters - Page 1:
2025-08-19 16:32:16,069 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:16,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:16,882 - INFO - Response - Page 1:
2025-08-19 16:32:16,882 - INFO - 第 1 页获取到 50 条记录
2025-08-19 16:32:17,397 - INFO - Request Parameters - Page 2:
2025-08-19 16:32:17,397 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:17,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:18,210 - INFO - Response - Page 2:
2025-08-19 16:32:18,210 - INFO - 第 2 页获取到 50 条记录
2025-08-19 16:32:18,710 - INFO - Request Parameters - Page 3:
2025-08-19 16:32:18,710 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:18,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:19,475 - INFO - Response - Page 3:
2025-08-19 16:32:19,475 - INFO - 第 3 页获取到 50 条记录
2025-08-19 16:32:19,991 - INFO - Request Parameters - Page 4:
2025-08-19 16:32:19,991 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:19,991 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:20,741 - INFO - Response - Page 4:
2025-08-19 16:32:20,741 - INFO - 第 4 页获取到 50 条记录
2025-08-19 16:32:21,257 - INFO - Request Parameters - Page 5:
2025-08-19 16:32:21,257 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:21,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:21,991 - INFO - Response - Page 5:
2025-08-19 16:32:21,991 - INFO - 第 5 页获取到 50 条记录
2025-08-19 16:32:22,507 - INFO - Request Parameters - Page 6:
2025-08-19 16:32:22,507 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:22,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:23,241 - INFO - Response - Page 6:
2025-08-19 16:32:23,241 - INFO - 第 6 页获取到 50 条记录
2025-08-19 16:32:23,741 - INFO - Request Parameters - Page 7:
2025-08-19 16:32:23,741 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:23,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:24,569 - INFO - Response - Page 7:
2025-08-19 16:32:24,569 - INFO - 第 7 页获取到 50 条记录
2025-08-19 16:32:25,069 - INFO - Request Parameters - Page 8:
2025-08-19 16:32:25,069 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:25,069 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:25,882 - INFO - Response - Page 8:
2025-08-19 16:32:25,882 - INFO - 第 8 页获取到 50 条记录
2025-08-19 16:32:26,382 - INFO - Request Parameters - Page 9:
2025-08-19 16:32:26,382 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:26,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:27,147 - INFO - Response - Page 9:
2025-08-19 16:32:27,147 - INFO - 第 9 页获取到 50 条记录
2025-08-19 16:32:27,647 - INFO - Request Parameters - Page 10:
2025-08-19 16:32:27,647 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:27,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:28,413 - INFO - Response - Page 10:
2025-08-19 16:32:28,413 - INFO - 第 10 页获取到 50 条记录
2025-08-19 16:32:28,913 - INFO - Request Parameters - Page 11:
2025-08-19 16:32:28,913 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:28,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:29,491 - INFO - Response - Page 11:
2025-08-19 16:32:29,491 - INFO - 第 11 页获取到 6 条记录
2025-08-19 16:32:29,991 - INFO - 查询完成，共获取到 506 条记录
2025-08-19 16:32:29,991 - INFO - 获取到 506 条表单数据
2025-08-19 16:32:29,991 - INFO - 当前日期 2025-08-18 有 526 条MySQL数据需要处理
2025-08-19 16:32:30,007 - INFO - 开始批量插入 20 条新记录
2025-08-19 16:32:30,210 - INFO - 批量插入响应状态码: 200
2025-08-19 16:32:30,210 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 08:32:27 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '992', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B13124E0-8355-745F-A2EA-B9304C3089E4', 'x-acs-trace-id': '31a0fb6a7d5774963ea4b8001b07f06f', 'etag': '900VFTqDcXd2Prb63P7ZKlQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 16:32:30,210 - INFO - 批量插入响应体: {'result': ['FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMJK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMKK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMLK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMMK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMNK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMOK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMPK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMQK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMRK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMSK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMTK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMUK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMVK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMWK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMXK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMYK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMZK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEM0L1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEM1L1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEM2L1']}
2025-08-19 16:32:30,210 - INFO - 批量插入表单数据成功，批次 1，共 20 条记录
2025-08-19 16:32:30,210 - INFO - 成功插入的数据ID: ['FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMJK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMKK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMLK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMMK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMNK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMOK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMPK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMQK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMRK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMSK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMTK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMUK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMVK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMWK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMXK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMYK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEMZK1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEM0L1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEM1L1', 'FINST-49866E71HY1Y24JTDZTB2DEYEOEO206TCAIEM2L1']
2025-08-19 16:32:35,225 - INFO - 批量插入完成，共 20 条记录
2025-08-19 16:32:35,225 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 20 条，错误: 0 条
2025-08-19 16:32:35,225 - INFO - 开始处理日期: 2025-08-19
2025-08-19 16:32:35,225 - INFO - Request Parameters - Page 1:
2025-08-19 16:32:35,225 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 16:32:35,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 16:32:35,819 - INFO - Response - Page 1:
2025-08-19 16:32:35,819 - INFO - 第 1 页获取到 4 条记录
2025-08-19 16:32:36,335 - INFO - 查询完成，共获取到 4 条记录
2025-08-19 16:32:36,335 - INFO - 获取到 4 条表单数据
2025-08-19 16:32:36,335 - INFO - 当前日期 2025-08-19 有 4 条MySQL数据需要处理
2025-08-19 16:32:36,335 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 16:32:36,335 - INFO - 数据同步完成！更新: 0 条，插入: 20 条，错误: 0 条
2025-08-19 16:32:36,335 - INFO - 同步完成
2025-08-19 19:30:33,526 - INFO - 使用默认增量同步（当天更新数据）
2025-08-19 19:30:33,526 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-19 19:30:33,526 - INFO - 查询参数: ('2025-08-19',)
2025-08-19 19:30:33,698 - INFO - MySQL查询成功，增量数据（日期: 2025-08-19），共获取 173 条记录
2025-08-19 19:30:33,698 - INFO - 获取到 5 个日期需要处理: ['2025-08-13', '2025-08-16', '2025-08-17', '2025-08-18', '2025-08-19']
2025-08-19 19:30:33,698 - INFO - 开始处理日期: 2025-08-13
2025-08-19 19:30:33,714 - INFO - Request Parameters - Page 1:
2025-08-19 19:30:33,714 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:30:33,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:30:41,839 - ERROR - 处理日期 2025-08-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3417093F-61CC-7550-8843-4F3FFF03B5CA Response: {'code': 'ServiceUnavailable', 'requestid': '3417093F-61CC-7550-8843-4F3FFF03B5CA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3417093F-61CC-7550-8843-4F3FFF03B5CA)
2025-08-19 19:30:41,839 - INFO - 开始处理日期: 2025-08-16
2025-08-19 19:30:41,839 - INFO - Request Parameters - Page 1:
2025-08-19 19:30:41,839 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:30:41,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:30:49,950 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C22E329F-D92B-79CD-B135-A6486244F062 Response: {'code': 'ServiceUnavailable', 'requestid': 'C22E329F-D92B-79CD-B135-A6486244F062', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C22E329F-D92B-79CD-B135-A6486244F062)
2025-08-19 19:30:49,950 - INFO - 开始处理日期: 2025-08-17
2025-08-19 19:30:49,950 - INFO - Request Parameters - Page 1:
2025-08-19 19:30:49,950 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:30:49,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:30:57,481 - INFO - Response - Page 1:
2025-08-19 19:30:57,481 - INFO - 第 1 页获取到 50 条记录
2025-08-19 19:30:57,982 - INFO - Request Parameters - Page 2:
2025-08-19 19:30:57,982 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:30:57,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:30:58,732 - INFO - Response - Page 2:
2025-08-19 19:30:58,732 - INFO - 第 2 页获取到 50 条记录
2025-08-19 19:30:59,232 - INFO - Request Parameters - Page 3:
2025-08-19 19:30:59,232 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:30:59,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:30:59,982 - INFO - Response - Page 3:
2025-08-19 19:30:59,982 - INFO - 第 3 页获取到 50 条记录
2025-08-19 19:31:00,497 - INFO - Request Parameters - Page 4:
2025-08-19 19:31:00,497 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:00,497 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:01,216 - INFO - Response - Page 4:
2025-08-19 19:31:01,216 - INFO - 第 4 页获取到 50 条记录
2025-08-19 19:31:01,716 - INFO - Request Parameters - Page 5:
2025-08-19 19:31:01,716 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:01,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:02,435 - INFO - Response - Page 5:
2025-08-19 19:31:02,435 - INFO - 第 5 页获取到 50 条记录
2025-08-19 19:31:02,951 - INFO - Request Parameters - Page 6:
2025-08-19 19:31:02,951 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:02,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:03,732 - INFO - Response - Page 6:
2025-08-19 19:31:03,732 - INFO - 第 6 页获取到 50 条记录
2025-08-19 19:31:04,232 - INFO - Request Parameters - Page 7:
2025-08-19 19:31:04,232 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:04,232 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:04,967 - INFO - Response - Page 7:
2025-08-19 19:31:04,967 - INFO - 第 7 页获取到 50 条记录
2025-08-19 19:31:05,482 - INFO - Request Parameters - Page 8:
2025-08-19 19:31:05,482 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:05,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:06,279 - INFO - Response - Page 8:
2025-08-19 19:31:06,279 - INFO - 第 8 页获取到 50 条记录
2025-08-19 19:31:06,795 - INFO - Request Parameters - Page 9:
2025-08-19 19:31:06,795 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:06,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:07,592 - INFO - Response - Page 9:
2025-08-19 19:31:07,592 - INFO - 第 9 页获取到 50 条记录
2025-08-19 19:31:08,107 - INFO - Request Parameters - Page 10:
2025-08-19 19:31:08,107 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:08,107 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:08,873 - INFO - Response - Page 10:
2025-08-19 19:31:08,873 - INFO - 第 10 页获取到 50 条记录
2025-08-19 19:31:09,373 - INFO - Request Parameters - Page 11:
2025-08-19 19:31:09,373 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:09,373 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:10,139 - INFO - Response - Page 11:
2025-08-19 19:31:10,139 - INFO - 第 11 页获取到 50 条记录
2025-08-19 19:31:10,655 - INFO - Request Parameters - Page 12:
2025-08-19 19:31:10,655 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:10,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:11,248 - INFO - Response - Page 12:
2025-08-19 19:31:11,248 - INFO - 第 12 页获取到 4 条记录
2025-08-19 19:31:11,764 - INFO - 查询完成，共获取到 554 条记录
2025-08-19 19:31:11,764 - INFO - 获取到 554 条表单数据
2025-08-19 19:31:11,764 - INFO - 当前日期 2025-08-17 有 2 条MySQL数据需要处理
2025-08-19 19:31:11,764 - INFO - 开始更新记录 - 表单实例ID: FINST-MLF66JA17K4YXMZRABNECDU7O4063GRY0IGEMY1
2025-08-19 19:31:12,405 - INFO - 更新表单数据成功: FINST-MLF66JA17K4YXMZRABNECDU7O4063GRY0IGEMY1
2025-08-19 19:31:12,405 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7260.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7260.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-19 19:31:12,405 - INFO - 开始更新记录 - 表单实例ID: FINST-SI7661818N4YBKUXC20SICBXJ7NT38R21IGEM0
2025-08-19 19:31:13,005 - INFO - 更新表单数据成功: FINST-SI7661818N4YBKUXC20SICBXJ7NT38R21IGEM0
2025-08-19 19:31:13,006 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 800.0, 'new_value': 874.0}, {'field': 'total_amount', 'old_value': 800.0, 'new_value': 874.0}, {'field': 'order_count', 'old_value': 20, 'new_value': 26}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/299eb31d6d0149a0a7d8dfee6c17381e.png?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=hGkXgxnotz2TuiBvHwpB5RJgmDQ%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/623638b5c33e4e6d9e94d0e219b7260b.jpg?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=gmNKIQ%2BjTs5cJNCpx%2BGU3qVqzBo%3D'}]
2025-08-19 19:31:13,006 - INFO - 日期 2025-08-17 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-08-19 19:31:13,006 - INFO - 开始处理日期: 2025-08-18
2025-08-19 19:31:13,006 - INFO - Request Parameters - Page 1:
2025-08-19 19:31:13,006 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:13,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:13,775 - INFO - Response - Page 1:
2025-08-19 19:31:13,775 - INFO - 第 1 页获取到 50 条记录
2025-08-19 19:31:14,291 - INFO - Request Parameters - Page 2:
2025-08-19 19:31:14,291 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:14,291 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:15,119 - INFO - Response - Page 2:
2025-08-19 19:31:15,119 - INFO - 第 2 页获取到 50 条记录
2025-08-19 19:31:15,635 - INFO - Request Parameters - Page 3:
2025-08-19 19:31:15,635 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:15,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:16,447 - INFO - Response - Page 3:
2025-08-19 19:31:16,447 - INFO - 第 3 页获取到 50 条记录
2025-08-19 19:31:16,963 - INFO - Request Parameters - Page 4:
2025-08-19 19:31:16,963 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:16,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:17,697 - INFO - Response - Page 4:
2025-08-19 19:31:17,697 - INFO - 第 4 页获取到 50 条记录
2025-08-19 19:31:18,213 - INFO - Request Parameters - Page 5:
2025-08-19 19:31:18,213 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:18,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:18,994 - INFO - Response - Page 5:
2025-08-19 19:31:18,994 - INFO - 第 5 页获取到 50 条记录
2025-08-19 19:31:19,494 - INFO - Request Parameters - Page 6:
2025-08-19 19:31:19,494 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:19,494 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:20,260 - INFO - Response - Page 6:
2025-08-19 19:31:20,260 - INFO - 第 6 页获取到 50 条记录
2025-08-19 19:31:20,776 - INFO - Request Parameters - Page 7:
2025-08-19 19:31:20,776 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:20,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:21,510 - INFO - Response - Page 7:
2025-08-19 19:31:21,510 - INFO - 第 7 页获取到 50 条记录
2025-08-19 19:31:22,010 - INFO - Request Parameters - Page 8:
2025-08-19 19:31:22,010 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:22,010 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:22,776 - INFO - Response - Page 8:
2025-08-19 19:31:22,776 - INFO - 第 8 页获取到 50 条记录
2025-08-19 19:31:23,292 - INFO - Request Parameters - Page 9:
2025-08-19 19:31:23,292 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:23,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:24,042 - INFO - Response - Page 9:
2025-08-19 19:31:24,042 - INFO - 第 9 页获取到 50 条记录
2025-08-19 19:31:24,542 - INFO - Request Parameters - Page 10:
2025-08-19 19:31:24,542 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:24,542 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:25,323 - INFO - Response - Page 10:
2025-08-19 19:31:25,323 - INFO - 第 10 页获取到 50 条记录
2025-08-19 19:31:25,839 - INFO - Request Parameters - Page 11:
2025-08-19 19:31:25,839 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:25,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:26,479 - INFO - Response - Page 11:
2025-08-19 19:31:26,479 - INFO - 第 11 页获取到 26 条记录
2025-08-19 19:31:26,995 - INFO - 查询完成，共获取到 526 条记录
2025-08-19 19:31:26,995 - INFO - 获取到 526 条表单数据
2025-08-19 19:31:26,995 - INFO - 当前日期 2025-08-18 有 157 条MySQL数据需要处理
2025-08-19 19:31:26,995 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 19:31:26,995 - INFO - 开始处理日期: 2025-08-19
2025-08-19 19:31:26,995 - INFO - Request Parameters - Page 1:
2025-08-19 19:31:26,995 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:31:26,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:31:27,589 - INFO - Response - Page 1:
2025-08-19 19:31:27,589 - INFO - 第 1 页获取到 4 条记录
2025-08-19 19:31:28,104 - INFO - 查询完成，共获取到 4 条记录
2025-08-19 19:31:28,104 - INFO - 获取到 4 条表单数据
2025-08-19 19:31:28,104 - INFO - 当前日期 2025-08-19 有 4 条MySQL数据需要处理
2025-08-19 19:31:28,104 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 19:31:28,104 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 2 条
2025-08-19 19:32:28,121 - INFO - 开始同步昨天与今天的销售数据: 2025-08-18 至 2025-08-19
2025-08-19 19:32:28,121 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-19 19:32:28,121 - INFO - 查询参数: ('2025-08-18', '2025-08-19')
2025-08-19 19:32:28,308 - INFO - MySQL查询成功，时间段: 2025-08-18 至 2025-08-19，共获取 554 条记录
2025-08-19 19:32:28,308 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 19:32:28,308 - INFO - 开始处理日期: 2025-08-18
2025-08-19 19:32:28,308 - INFO - Request Parameters - Page 1:
2025-08-19 19:32:28,308 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:28,308 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:29,074 - INFO - Response - Page 1:
2025-08-19 19:32:29,074 - INFO - 第 1 页获取到 50 条记录
2025-08-19 19:32:29,590 - INFO - Request Parameters - Page 2:
2025-08-19 19:32:29,590 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:29,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:30,324 - INFO - Response - Page 2:
2025-08-19 19:32:30,324 - INFO - 第 2 页获取到 50 条记录
2025-08-19 19:32:30,840 - INFO - Request Parameters - Page 3:
2025-08-19 19:32:30,840 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:30,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:31,574 - INFO - Response - Page 3:
2025-08-19 19:32:31,574 - INFO - 第 3 页获取到 50 条记录
2025-08-19 19:32:32,090 - INFO - Request Parameters - Page 4:
2025-08-19 19:32:32,090 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:32,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:32,746 - INFO - Response - Page 4:
2025-08-19 19:32:32,746 - INFO - 第 4 页获取到 50 条记录
2025-08-19 19:32:33,246 - INFO - Request Parameters - Page 5:
2025-08-19 19:32:33,246 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:33,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:34,012 - INFO - Response - Page 5:
2025-08-19 19:32:34,012 - INFO - 第 5 页获取到 50 条记录
2025-08-19 19:32:34,512 - INFO - Request Parameters - Page 6:
2025-08-19 19:32:34,512 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:34,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:35,293 - INFO - Response - Page 6:
2025-08-19 19:32:35,293 - INFO - 第 6 页获取到 50 条记录
2025-08-19 19:32:35,809 - INFO - Request Parameters - Page 7:
2025-08-19 19:32:35,809 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:35,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:36,590 - INFO - Response - Page 7:
2025-08-19 19:32:36,606 - INFO - 第 7 页获取到 50 条记录
2025-08-19 19:32:37,106 - INFO - Request Parameters - Page 8:
2025-08-19 19:32:37,106 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:37,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:37,840 - INFO - Response - Page 8:
2025-08-19 19:32:37,840 - INFO - 第 8 页获取到 50 条记录
2025-08-19 19:32:38,356 - INFO - Request Parameters - Page 9:
2025-08-19 19:32:38,356 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:38,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:39,137 - INFO - Response - Page 9:
2025-08-19 19:32:39,137 - INFO - 第 9 页获取到 50 条记录
2025-08-19 19:32:39,653 - INFO - Request Parameters - Page 10:
2025-08-19 19:32:39,653 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:39,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:40,450 - INFO - Response - Page 10:
2025-08-19 19:32:40,450 - INFO - 第 10 页获取到 50 条记录
2025-08-19 19:32:40,966 - INFO - Request Parameters - Page 11:
2025-08-19 19:32:40,966 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:40,966 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:41,653 - INFO - Response - Page 11:
2025-08-19 19:32:41,669 - INFO - 第 11 页获取到 26 条记录
2025-08-19 19:32:42,184 - INFO - 查询完成，共获取到 526 条记录
2025-08-19 19:32:42,184 - INFO - 获取到 526 条表单数据
2025-08-19 19:32:42,184 - INFO - 当前日期 2025-08-18 有 526 条MySQL数据需要处理
2025-08-19 19:32:42,200 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 19:32:42,200 - INFO - 开始处理日期: 2025-08-19
2025-08-19 19:32:42,200 - INFO - Request Parameters - Page 1:
2025-08-19 19:32:42,200 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 19:32:42,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 19:32:42,825 - INFO - Response - Page 1:
2025-08-19 19:32:42,825 - INFO - 第 1 页获取到 4 条记录
2025-08-19 19:32:43,341 - INFO - 查询完成，共获取到 4 条记录
2025-08-19 19:32:43,341 - INFO - 获取到 4 条表单数据
2025-08-19 19:32:43,341 - INFO - 当前日期 2025-08-19 有 4 条MySQL数据需要处理
2025-08-19 19:32:43,341 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 19:32:43,341 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 19:32:43,341 - INFO - 同步完成
2025-08-19 22:30:33,531 - INFO - 使用默认增量同步（当天更新数据）
2025-08-19 22:30:33,531 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-19 22:30:33,531 - INFO - 查询参数: ('2025-08-19',)
2025-08-19 22:30:33,718 - INFO - MySQL查询成功，增量数据（日期: 2025-08-19），共获取 185 条记录
2025-08-19 22:30:33,718 - INFO - 获取到 5 个日期需要处理: ['2025-08-13', '2025-08-16', '2025-08-17', '2025-08-18', '2025-08-19']
2025-08-19 22:30:33,718 - INFO - 开始处理日期: 2025-08-13
2025-08-19 22:30:33,718 - INFO - Request Parameters - Page 1:
2025-08-19 22:30:33,718 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:33,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:41,844 - ERROR - 处理日期 2025-08-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4D78CA2E-107A-7D9A-9085-74F2298E0DFC Response: {'code': 'ServiceUnavailable', 'requestid': '4D78CA2E-107A-7D9A-9085-74F2298E0DFC', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4D78CA2E-107A-7D9A-9085-74F2298E0DFC)
2025-08-19 22:30:41,844 - INFO - 开始处理日期: 2025-08-16
2025-08-19 22:30:41,844 - INFO - Request Parameters - Page 1:
2025-08-19 22:30:41,844 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:41,844 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:42,907 - INFO - Response - Page 1:
2025-08-19 22:30:42,907 - INFO - 第 1 页获取到 50 条记录
2025-08-19 22:30:43,407 - INFO - Request Parameters - Page 2:
2025-08-19 22:30:43,407 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:43,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:44,141 - INFO - Response - Page 2:
2025-08-19 22:30:44,141 - INFO - 第 2 页获取到 50 条记录
2025-08-19 22:30:44,657 - INFO - Request Parameters - Page 3:
2025-08-19 22:30:44,657 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:44,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:52,751 - ERROR - 处理日期 2025-08-16 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0E22CF97-939D-73E1-8CB9-6FC98FA8B2B6 Response: {'code': 'ServiceUnavailable', 'requestid': '0E22CF97-939D-73E1-8CB9-6FC98FA8B2B6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0E22CF97-939D-73E1-8CB9-6FC98FA8B2B6)
2025-08-19 22:30:52,767 - INFO - 开始处理日期: 2025-08-17
2025-08-19 22:30:52,767 - INFO - Request Parameters - Page 1:
2025-08-19 22:30:52,767 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:52,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:53,455 - INFO - Response - Page 1:
2025-08-19 22:30:53,455 - INFO - 第 1 页获取到 50 条记录
2025-08-19 22:30:53,970 - INFO - Request Parameters - Page 2:
2025-08-19 22:30:53,970 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:53,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:54,736 - INFO - Response - Page 2:
2025-08-19 22:30:54,736 - INFO - 第 2 页获取到 50 条记录
2025-08-19 22:30:55,252 - INFO - Request Parameters - Page 3:
2025-08-19 22:30:55,252 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:55,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:57,330 - INFO - Response - Page 3:
2025-08-19 22:30:57,330 - INFO - 第 3 页获取到 50 条记录
2025-08-19 22:30:57,846 - INFO - Request Parameters - Page 4:
2025-08-19 22:30:57,846 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:57,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:58,580 - INFO - Response - Page 4:
2025-08-19 22:30:58,580 - INFO - 第 4 页获取到 50 条记录
2025-08-19 22:30:59,096 - INFO - Request Parameters - Page 5:
2025-08-19 22:30:59,096 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:30:59,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:30:59,814 - INFO - Response - Page 5:
2025-08-19 22:30:59,814 - INFO - 第 5 页获取到 50 条记录
2025-08-19 22:31:00,330 - INFO - Request Parameters - Page 6:
2025-08-19 22:31:00,330 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:00,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:01,096 - INFO - Response - Page 6:
2025-08-19 22:31:01,096 - INFO - 第 6 页获取到 50 条记录
2025-08-19 22:31:01,611 - INFO - Request Parameters - Page 7:
2025-08-19 22:31:01,611 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:01,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:02,408 - INFO - Response - Page 7:
2025-08-19 22:31:02,408 - INFO - 第 7 页获取到 50 条记录
2025-08-19 22:31:02,924 - INFO - Request Parameters - Page 8:
2025-08-19 22:31:02,924 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:02,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:03,737 - INFO - Response - Page 8:
2025-08-19 22:31:03,737 - INFO - 第 8 页获取到 50 条记录
2025-08-19 22:31:04,237 - INFO - Request Parameters - Page 9:
2025-08-19 22:31:04,237 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:04,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:05,049 - INFO - Response - Page 9:
2025-08-19 22:31:05,049 - INFO - 第 9 页获取到 50 条记录
2025-08-19 22:31:05,549 - INFO - Request Parameters - Page 10:
2025-08-19 22:31:05,549 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:05,549 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:06,299 - INFO - Response - Page 10:
2025-08-19 22:31:06,299 - INFO - 第 10 页获取到 50 条记录
2025-08-19 22:31:06,799 - INFO - Request Parameters - Page 11:
2025-08-19 22:31:06,799 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:06,799 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:07,581 - INFO - Response - Page 11:
2025-08-19 22:31:07,581 - INFO - 第 11 页获取到 50 条记录
2025-08-19 22:31:08,096 - INFO - Request Parameters - Page 12:
2025-08-19 22:31:08,096 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:08,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755360000000, 1755446399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:08,659 - INFO - Response - Page 12:
2025-08-19 22:31:08,659 - INFO - 第 12 页获取到 4 条记录
2025-08-19 22:31:09,175 - INFO - 查询完成，共获取到 554 条记录
2025-08-19 22:31:09,175 - INFO - 获取到 554 条表单数据
2025-08-19 22:31:09,175 - INFO - 当前日期 2025-08-17 有 2 条MySQL数据需要处理
2025-08-19 22:31:09,175 - INFO - 日期 2025-08-17 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 22:31:09,175 - INFO - 开始处理日期: 2025-08-18
2025-08-19 22:31:09,175 - INFO - Request Parameters - Page 1:
2025-08-19 22:31:09,175 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:09,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:09,940 - INFO - Response - Page 1:
2025-08-19 22:31:09,940 - INFO - 第 1 页获取到 50 条记录
2025-08-19 22:31:10,456 - INFO - Request Parameters - Page 2:
2025-08-19 22:31:10,456 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:10,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:11,206 - INFO - Response - Page 2:
2025-08-19 22:31:11,206 - INFO - 第 2 页获取到 50 条记录
2025-08-19 22:31:11,722 - INFO - Request Parameters - Page 3:
2025-08-19 22:31:11,722 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:11,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:12,487 - INFO - Response - Page 3:
2025-08-19 22:31:12,487 - INFO - 第 3 页获取到 50 条记录
2025-08-19 22:31:12,988 - INFO - Request Parameters - Page 4:
2025-08-19 22:31:12,988 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:12,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:13,706 - INFO - Response - Page 4:
2025-08-19 22:31:13,706 - INFO - 第 4 页获取到 50 条记录
2025-08-19 22:31:14,222 - INFO - Request Parameters - Page 5:
2025-08-19 22:31:14,222 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:14,222 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:14,878 - INFO - Response - Page 5:
2025-08-19 22:31:14,878 - INFO - 第 5 页获取到 50 条记录
2025-08-19 22:31:15,394 - INFO - Request Parameters - Page 6:
2025-08-19 22:31:15,394 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:15,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:16,160 - INFO - Response - Page 6:
2025-08-19 22:31:16,160 - INFO - 第 6 页获取到 50 条记录
2025-08-19 22:31:16,675 - INFO - Request Parameters - Page 7:
2025-08-19 22:31:16,675 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:16,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:17,441 - INFO - Response - Page 7:
2025-08-19 22:31:17,441 - INFO - 第 7 页获取到 50 条记录
2025-08-19 22:31:17,957 - INFO - Request Parameters - Page 8:
2025-08-19 22:31:17,957 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:17,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:18,733 - INFO - Response - Page 8:
2025-08-19 22:31:18,733 - INFO - 第 8 页获取到 50 条记录
2025-08-19 22:31:19,249 - INFO - Request Parameters - Page 9:
2025-08-19 22:31:19,249 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:19,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:19,999 - INFO - Response - Page 9:
2025-08-19 22:31:19,999 - INFO - 第 9 页获取到 50 条记录
2025-08-19 22:31:20,499 - INFO - Request Parameters - Page 10:
2025-08-19 22:31:20,499 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:20,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:21,280 - INFO - Response - Page 10:
2025-08-19 22:31:21,280 - INFO - 第 10 页获取到 50 条记录
2025-08-19 22:31:21,796 - INFO - Request Parameters - Page 11:
2025-08-19 22:31:21,796 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:21,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:22,577 - INFO - Response - Page 11:
2025-08-19 22:31:22,577 - INFO - 第 11 页获取到 26 条记录
2025-08-19 22:31:23,093 - INFO - 查询完成，共获取到 526 条记录
2025-08-19 22:31:23,093 - INFO - 获取到 526 条表单数据
2025-08-19 22:31:23,093 - INFO - 当前日期 2025-08-18 有 157 条MySQL数据需要处理
2025-08-19 22:31:23,093 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 22:31:23,093 - INFO - 开始处理日期: 2025-08-19
2025-08-19 22:31:23,093 - INFO - Request Parameters - Page 1:
2025-08-19 22:31:23,093 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:31:23,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:31:23,671 - INFO - Response - Page 1:
2025-08-19 22:31:23,671 - INFO - 第 1 页获取到 4 条记录
2025-08-19 22:31:24,171 - INFO - 查询完成，共获取到 4 条记录
2025-08-19 22:31:24,171 - INFO - 获取到 4 条表单数据
2025-08-19 22:31:24,171 - INFO - 当前日期 2025-08-19 有 16 条MySQL数据需要处理
2025-08-19 22:31:24,171 - INFO - 开始批量插入 12 条新记录
2025-08-19 22:31:24,327 - INFO - 批量插入响应状态码: 200
2025-08-19 22:31:24,327 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Tue, 19 Aug 2025 14:31:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '588', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B85A9567-68D0-7D2B-AD7F-D1780430543D', 'x-acs-trace-id': 'e7fc337798deeb58b67b3a773a13cfb0', 'etag': '5F0MWwpUUDeQg8dSUrWzyNQ8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-19 22:31:24,327 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM42', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM52', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM62', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM72', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM82', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM92', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEMA2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEMB2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEMC2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEMD2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEME2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEMF2']}
2025-08-19 22:31:24,327 - INFO - 批量插入表单数据成功，批次 1，共 12 条记录
2025-08-19 22:31:24,327 - INFO - 成功插入的数据ID: ['FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM42', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM52', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM62', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM72', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM82', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEM92', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK391E6NIEMA2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEMB2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEMC2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEMD2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEME2', 'FINST-WBF66B812Y5Y6WASE437D90TFVKK3A1E6NIEMF2']
2025-08-19 22:31:29,343 - INFO - 批量插入完成，共 12 条记录
2025-08-19 22:31:29,343 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 12 条，错误: 0 条
2025-08-19 22:31:29,343 - INFO - 数据同步完成！更新: 0 条，插入: 12 条，错误: 2 条
2025-08-19 22:32:29,360 - INFO - 开始同步昨天与今天的销售数据: 2025-08-18 至 2025-08-19
2025-08-19 22:32:29,360 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-19 22:32:29,360 - INFO - 查询参数: ('2025-08-18', '2025-08-19')
2025-08-19 22:32:29,547 - INFO - MySQL查询成功，时间段: 2025-08-18 至 2025-08-19，共获取 566 条记录
2025-08-19 22:32:29,547 - INFO - 获取到 2 个日期需要处理: ['2025-08-18', '2025-08-19']
2025-08-19 22:32:29,547 - INFO - 开始处理日期: 2025-08-18
2025-08-19 22:32:29,547 - INFO - Request Parameters - Page 1:
2025-08-19 22:32:29,547 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:29,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:30,360 - INFO - Response - Page 1:
2025-08-19 22:32:30,360 - INFO - 第 1 页获取到 50 条记录
2025-08-19 22:32:30,875 - INFO - Request Parameters - Page 2:
2025-08-19 22:32:30,875 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:30,875 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:31,641 - INFO - Response - Page 2:
2025-08-19 22:32:31,641 - INFO - 第 2 页获取到 50 条记录
2025-08-19 22:32:32,157 - INFO - Request Parameters - Page 3:
2025-08-19 22:32:32,157 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:32,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:32,922 - INFO - Response - Page 3:
2025-08-19 22:32:32,922 - INFO - 第 3 页获取到 50 条记录
2025-08-19 22:32:33,438 - INFO - Request Parameters - Page 4:
2025-08-19 22:32:33,438 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:33,438 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:34,204 - INFO - Response - Page 4:
2025-08-19 22:32:34,204 - INFO - 第 4 页获取到 50 条记录
2025-08-19 22:32:34,719 - INFO - Request Parameters - Page 5:
2025-08-19 22:32:34,719 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:34,719 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:35,438 - INFO - Response - Page 5:
2025-08-19 22:32:35,438 - INFO - 第 5 页获取到 50 条记录
2025-08-19 22:32:35,954 - INFO - Request Parameters - Page 6:
2025-08-19 22:32:35,954 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:35,954 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:36,688 - INFO - Response - Page 6:
2025-08-19 22:32:36,688 - INFO - 第 6 页获取到 50 条记录
2025-08-19 22:32:37,204 - INFO - Request Parameters - Page 7:
2025-08-19 22:32:37,204 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:37,204 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:37,954 - INFO - Response - Page 7:
2025-08-19 22:32:37,954 - INFO - 第 7 页获取到 50 条记录
2025-08-19 22:32:38,470 - INFO - Request Parameters - Page 8:
2025-08-19 22:32:38,470 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:38,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:39,204 - INFO - Response - Page 8:
2025-08-19 22:32:39,204 - INFO - 第 8 页获取到 50 条记录
2025-08-19 22:32:39,720 - INFO - Request Parameters - Page 9:
2025-08-19 22:32:39,720 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:39,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:40,486 - INFO - Response - Page 9:
2025-08-19 22:32:40,486 - INFO - 第 9 页获取到 50 条记录
2025-08-19 22:32:41,001 - INFO - Request Parameters - Page 10:
2025-08-19 22:32:41,001 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:41,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:41,736 - INFO - Response - Page 10:
2025-08-19 22:32:41,736 - INFO - 第 10 页获取到 50 条记录
2025-08-19 22:32:42,251 - INFO - Request Parameters - Page 11:
2025-08-19 22:32:42,251 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:42,251 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:42,908 - INFO - Response - Page 11:
2025-08-19 22:32:42,908 - INFO - 第 11 页获取到 26 条记录
2025-08-19 22:32:43,408 - INFO - 查询完成，共获取到 526 条记录
2025-08-19 22:32:43,408 - INFO - 获取到 526 条表单数据
2025-08-19 22:32:43,408 - INFO - 当前日期 2025-08-18 有 526 条MySQL数据需要处理
2025-08-19 22:32:43,423 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 22:32:43,423 - INFO - 开始处理日期: 2025-08-19
2025-08-19 22:32:43,423 - INFO - Request Parameters - Page 1:
2025-08-19 22:32:43,423 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-19 22:32:43,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-19 22:32:44,048 - INFO - Response - Page 1:
2025-08-19 22:32:44,048 - INFO - 第 1 页获取到 16 条记录
2025-08-19 22:32:44,564 - INFO - 查询完成，共获取到 16 条记录
2025-08-19 22:32:44,564 - INFO - 获取到 16 条表单数据
2025-08-19 22:32:44,564 - INFO - 当前日期 2025-08-19 有 16 条MySQL数据需要处理
2025-08-19 22:32:44,564 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 22:32:44,564 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-19 22:32:44,564 - INFO - 同步完成
