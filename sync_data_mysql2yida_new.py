# -*- coding: utf-8 -*-
import os
import sys
import logging
import pymysql
import json
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import argparse
from time import sleep

# 设置日志级别为 WARNING，这样就不会显示 DEBUG 信息
logging.getLogger("alibabacloud_credentials").setLevel(logging.WARNING)

from alibabacloud_dingtalk.yida_2_0.client import Client as DingTalkYidaClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as yida_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from get_token import token
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client

# 数据库配置
DB_CONFIG = {
    'host': '**************',
    'port': 3306,  # MySQL默认端口
    'user': 'c_hxp_ro_prod',
    'password': 'xm9P06O7ezGi6PZt',
    'database': 'yx_business',
    'charset': 'utf8mb4',
    'cursorclass': pymysql.cursors.DictCursor
}

# 宜搭配置
YIDA_CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30'
}

# 字段映射
FIELD_MAPPING = {
    'project_code': 'textField_m9nw1k6w',
    'project_name': 'textField_m9nw1k6x', 
    'store_code': 'textField_m9nw1k6y',
    'store_name': 'textField_m9nw1k6z',
    'sale_date': 'dateField_m9nw1k71',
    'online_amount': 'numberField_m9nw1k73',
    'offline_amount': 'numberField_m9nw1k75', 
    'total_amount': 'numberField_m9nw1k77',
    'order_count': 'numberField_m9nw1k79',
    'report_source': 'textField_mbnv3d7h',
    'url': 'textField_maq9pxw4'
}

# 需要比较的字段
COMPARE_FIELDS = [
    'online_amount',    # 线上销售额
    'offline_amount',   # 线下销售额
    'total_amount',     # 总销售额
    'order_count',      # 订单数量
    'report_source',    # 上报来源
    'url',               # 附件
    'store_name'         # 店铺名称
]

class YidaFormDataClient:
    def __init__(self):
        # 配置日志文件
        # current_date = datetime.now().strftime('%Y%m%d')
        # logging.basicConfig(
        #     filename=f'yida_form_log_{current_date}.txt',
        #     level=logging.DEBUG,
        #     format='%(asctime)s - %(levelname)s - %(message)s',
        #     encoding='utf-8'
        # )
        
        self.access_token = token.get_token()
        self.client = self._create_client()

    def _create_client(self) -> dingtalkyida_1_0Client:
        """
        初始化宜搭客户端
        
        Returns:
            dingtalkyida_1_0Client: 宜搭客户端实例
        """
        config = open_api_models.Config(
            protocol='https',
            region_id='central'
        )
        return dingtalkyida_1_0Client(config)

    def get_form_data(self, page_size: int = 50, search_condition: Optional[List[Dict]] = None) -> List[Dict[str, Any]]:
        """
        获取宜搭表单数据
        
        Args:
            page_size: 每页数据条数，默认100
            search_condition: 查询条件，可选
            
        Returns:
            List[Dict[str, Any]]: 表单数据列表
        """
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    form_uuid=YIDA_CONFIG['FORM_UUID'],
                    search_condition=json.dumps(search_condition) if search_condition else None,
                    system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                    page_size=page_size,
                    user_id=YIDA_CONFIG['USER_ID'],
                    app_type=YIDA_CONFIG['APP_TYPE']
                )
                
                # 记录请求参数
                logging.info(f"Request Parameters - Page {current_page}:")
                logging.info(f"Headers: {headers}")
                logging.info(f"Request: {request}")
                
                result = self.client.search_form_data_second_generation_no_table_field_with_options(
                    request, 
                    headers, 
                    util_models.RuntimeOptions()
                )
                
                # 记录响应结果
                logging.info(f"Response - Page {current_page}:")
                # logging.info(f"Result: {result.body}")
                
                if not result or not result.body or not result.body.data:
                    break
                
                # 提取指定字段
                for item in result.body.data:
                    filtered_data = {
                        'formInstanceId': item.form_instance_id,
                        'formData': item.form_data
                    }
                    all_data.append(filtered_data)
                
                logging.info(f"第 {current_page} 页获取到 {len(result.body.data)} 条记录")
                sleep(0.5)
                # 如果获取的数据少于页大小，说明已经是最后一页
                if len(result.body.data) < page_size:
                    break
                    
                current_page += 1
            
            logging.info(f"查询完成，共获取到 {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            error_msg = f"获取表单数据失败: {str(e)}"
            if hasattr(e, 'code') and hasattr(e, 'message'):
                error_msg += f" (错误码: {e.code}, 错误信息: {e.message})"
            raise Exception(error_msg)

    def update_form_data(self, form_instance_id: str, new_data: Dict):
        """更新宜搭表单数据"""
        try:
            client = DingTalkYidaClient(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = yida_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 处理日期字段
            processed_data = new_data.copy()
            if FIELD_MAPPING['sale_date'] in processed_data:
                try:
                    dt = datetime.strptime(str(processed_data[FIELD_MAPPING['sale_date']]), '%Y-%m-%d')
                    processed_data[FIELD_MAPPING['sale_date']] = int(dt.timestamp() * 1000)  # 秒转毫秒
                    logging.debug(f"更新数据日期字段转换: {new_data[FIELD_MAPPING['sale_date']]} -> {processed_data[FIELD_MAPPING['sale_date']]}")
                except ValueError as e:
                    logging.error(f"更新数据日期格式转换错误: {processed_data[FIELD_MAPPING['sale_date']]}, {str(e)}")
                    raise
            
            request = yida_models.UpdateFormDataRequest()
            request.app_type = YIDA_CONFIG['APP_TYPE']
            request.system_token = YIDA_CONFIG['SYSTEM_TOKEN']
            request.user_id = YIDA_CONFIG['USER_ID']
            request.language = YIDA_CONFIG['LANGUAGE']
            request.form_instance_id = form_instance_id
            request.form_uuid = YIDA_CONFIG['FORM_UUID']
            request.update_form_data_json = json.dumps(processed_data, ensure_ascii=False)
            request.use_alias = False
            request.use_latest_version = False
            
            response = client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())
            logging.info(f"更新表单数据成功: {form_instance_id}")
            return response
            
        except Exception as e:
            logging.error(f"更新表单数据失败: {str(e)}")
            raise

    def batch_create_form_data(self, data_list: List[Dict], batch_size: int = 50):
        """批量插入宜搭表单数据"""
        try:
            client = dingtalkyida_1_0Client(open_api_models.Config(
                protocol='https',
                region_id='central'
            ))
            
            headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token
            
            # 将数据列表分成多个批次
            for i in range(0, len(data_list), batch_size):
                batch_data = data_list[i:i + batch_size]
                processed_data_list = []
                
                for item in batch_data:
                    processed_item = {}
                    for key, value in item.items():
                        # 处理日期字段
                        if key == FIELD_MAPPING['sale_date']:
                            try:
                                dt = datetime.strptime(str(value), '%Y-%m-%d')
                                processed_item[key] = int(dt.timestamp() * 1000)  # 秒转毫秒
                                logging.debug(f"批量插入日期字段转换: {value} -> {processed_item[key]}")
                            except ValueError as e:
                                logging.error(f"批量插入日期格式转换错误: {value}, {str(e)}")
                                continue
                        # 处理数值字段
                        elif key.startswith('numberField_'):
                            try:
                                if value is None or value == '':
                                    value = 0
                                # 对于笔数字段，确保是整数
                                if key == FIELD_MAPPING['order_count']:
                                    value = int(value) if value else 0
                                # 对于金额字段，保持原始类型
                                elif key in [FIELD_MAPPING['online_amount'], FIELD_MAPPING['offline_amount'], FIELD_MAPPING['total_amount']]:
                                    if value == 0:
                                        value = 0  # 保持为整数0
                                    elif isinstance(value, (int, float)):
                                        value = value  # 保持原始数值类型
                                processed_item[f"{key}_value"] = str(value)  # 添加字符串版本
                                processed_item[key] = value  # 保持原始值
                            except ValueError as e:
                                logging.error(f"数值转换错误: {value}, {str(e)}")
                                continue
                        else:
                            processed_item[key] = value
                    processed_data_list.append(processed_item)
                
                form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in processed_data_list]
                # logging.info(f"请求数据: {form_data_json_list}")
                
                request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                    no_execute_expression=True,
                    form_uuid=YIDA_CONFIG['FORM_UUID'],
                    app_type=YIDA_CONFIG['APP_TYPE'],
                    asynchronous_execution=True,
                    system_token=YIDA_CONFIG['SYSTEM_TOKEN'],
                    keep_running_after_exception=True,
                    user_id=YIDA_CONFIG['USER_ID'],
                    form_data_json_list=form_data_json_list
                )
                
                try:
                    response = client.batch_save_form_data_with_options(request, headers, util_models.RuntimeOptions())
                    
                    # 记录详细的响应信息
                    logging.info(f"批量插入响应状态码: {response.status_code}")
                    logging.info(f"批量插入响应头: {response.headers}")
                    logging.info(f"批量插入响应体: {response.body}")
                    
                    # 检查响应是否成功
                    if response.status_code == 200:
                        if hasattr(response.body, 'result') and response.body.result:
                            logging.info(f"批量插入表单数据成功，批次 {i//batch_size + 1}，共 {len(batch_data)} 条记录")
                            logging.info(f"成功插入的数据ID: {response.body.result}")
                        else:
                            logging.warning(f"批量插入响应成功但未返回结果，批次 {i//batch_size + 1}")
                            logging.warning(f"请求数据: {form_data_json_list}")
                    else:
                        logging.error(f"批量插入表单数据失败，批次 {i//batch_size + 1}: {response.status_code}")
                        logging.error(f"错误响应: {response.body}")
                        logging.error(f"失败数据: {form_data_json_list}")
                        raise Exception(f"批量插入失败，状态码: {response.status_code}")
                        
                except Exception as e:
                    logging.error(f"批量插入表单数据失败，批次 {i//batch_size + 1}: {str(e)}")
                    logging.error(f"错误数据: {form_data_json_list}")
                    raise
                
                # 添加延时避免请求过于频繁
                time.sleep(5)
            
        except Exception as e:
            logging.error(f"批量插入表单数据失败: {str(e)}")
            raise

class DataSyncClient:
    def __init__(self):
        # 配置日志
        current_date = datetime.now().strftime('%Y%m%d')
        logging.basicConfig(
            filename=f'logs/sync_data_mysql2yida_log_{current_date}.txt',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            encoding='utf-8'
        )
        
        # 初始化MySQL连接
        self.mysql_conn = pymysql.connect(**DB_CONFIG)
        
        # 初始化宜搭客户端
        self.yida_client = YidaFormDataClient()
        
    def get_mysql_data(self, start_date=None, end_date=None) -> List[Dict]:
        """
        从MySQL获取销售数据
        :param start_date: 开始日期，格式：YYYY-MM-DD，默认为当天
        :param end_date: 结束日期，格式：YYYY-MM-DD，默认为当天
        :return: 销售数据列表
        """
        try:
            cursor = self.mysql_conn.cursor()
            
            # 构建SQL查询
            sql = """
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
            """
            
            # 根据是否提供时间段决定查询条件
            if start_date is not None or end_date is not None:
                # 设置默认日期
                if end_date is None:
                    end_date = datetime.now().strftime('%Y-%m-%d')
                if start_date is None:
                    start_date = end_date
                
                sql += " AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s"
                params = (start_date, end_date)
            else:
                # 默认查询当天的增量数据
                current_date = datetime.now().strftime('%Y-%m-%d')
                sql += " AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s"
                params = (current_date,)            
            logging.info(f"MySQL查询SQL: {sql}")
            if params:
                logging.info(f"查询参数: {params}")
            
            # 执行查询
            if params:
                cursor.execute(sql, params)
            else:
                cursor.execute(sql)
                
            result = cursor.fetchall()
            
            # 记录查询信息
            if start_date is not None or end_date is not None:
                logging.info(f"MySQL查询成功，时间段: {start_date} 至 {end_date}，共获取 {len(result)} 条记录")
            else:
                logging.info(f"MySQL查询成功，增量数据（日期: {current_date}），共获取 {len(result)} 条记录")
            
            return result
        except Exception as e:
            logging.error(f"获取MySQL数据失败: {str(e)}")
            return []

    def sync_data(self, start_date=None, end_date=None):
        """同步数据主流程"""
        try:
            # 获取MySQL数据
            mysql_data = self.get_mysql_data(start_date, end_date)
            if not mysql_data:
                logging.error("未获取到MySQL数据")
                return
                
            # 获取日期去重清单
            date_list = sorted(set(item['sales_date'] for item in mysql_data))
            logging.info(f"获取到 {len(date_list)} 个日期需要处理: {date_list}")
            
            # 按项目和日期分组MySQL数据
            mysql_data_dict = {}
            for item in mysql_data:
                # 转换数值类型为float
                item['online_amount'] = float(item['online_amount']) if item['online_amount'] is not None else 0.0
                item['offline_amount'] = float(item['offline_amount']) if item['offline_amount'] is not None else 0.0
                item['total_amount'] = float(item['total_amount']) if item['total_amount'] is not None else 0.0
                item['order_count'] = int(item['order_count']) if item['order_count'] is not None else 0
                
                key = f"{item['project_code']}_{item['store_code']}_{item['sales_date']}"
                mysql_data_dict[key] = item
                logging.debug(f"MySQL数据分组 - 键: {key}, 值: {item}")
            
            # 按日期循环处理
            total_update_count = 0
            total_insert_count = 0
            total_error_count = 0
            
            for date in date_list:
                try:
                    logging.info(f"开始处理日期: {date}")
                    
                    # 将日期转换为时间戳（毫秒）
                    try:
                        date_obj = datetime.strptime(date, '%Y-%m-%d')
                        start_timestamp = int(date_obj.timestamp() * 1000)
                        end_timestamp = start_timestamp + 86399000  # 加上23:59:59的毫秒数
                    except ValueError as e:
                        logging.error(f"日期格式转换错误: {date}, {str(e)}")
                        continue
                    
                    # 构建日期筛选条件
                    search_condition = [{
                        "key": FIELD_MAPPING['sale_date'],
                        "value": [start_timestamp, end_timestamp],
                        "type": "DOUBLE",
                        "operator": "between",
                        "componentName": "DateField"
                    }]
                    logging.debug(f"查询条件: {search_condition}")
                    
                    # 获取指定日期的表单数据
                    yida_data = self.yida_client.get_form_data(search_condition=search_condition)
                    logging.info(f"获取到 {len(yida_data)} 条表单数据")
                    
                    # 创建宜搭数据索引
                    yida_data_dict = {}
                    for item in yida_data:
                        form_data = item['formData']
                        # 将日期字段转换为YYYY-MM-DD格式
                        sale_date = form_data[FIELD_MAPPING['sale_date']]
                        if isinstance(sale_date, (int, float)):
                            # 如果是时间戳，转换为日期字符串
                            sale_date = datetime.fromtimestamp(sale_date/1000).strftime('%Y-%m-%d')
                        key = f"{form_data[FIELD_MAPPING['project_code']]}_{form_data[FIELD_MAPPING['store_code']]}_{sale_date}"
                        yida_data_dict[key] = {
                            'form_instance_id': item['formInstanceId'],
                            'form_data': form_data
                        }
                        logging.debug(f"表单数据分组 - 键: {key}, 值: {form_data}")
                    
                    # 处理该日期的数据
                    current_date_mysql_items = [k for k in mysql_data_dict.keys() if k.split('_')[-1] == date]
                    logging.info(f"当前日期 {date} 有 {len(current_date_mysql_items)} 条MySQL数据需要处理")
                    
                    # 当前日期的计数器
                    date_update_count = 0
                    date_insert_count = 0
                    date_error_count = 0
                    insert_data_list = []
                    
                    for key in current_date_mysql_items:
                        mysql_item = mysql_data_dict[key]
                        try:
                            if key in yida_data_dict:
                                yida_item = yida_data_dict[key]['form_data']
                                logging.debug(f"开始对比数据 - 键: {key}")
                                logging.debug(f"MySQL数据: {mysql_item}")
                                logging.debug(f"表单数据: {yida_item}")
                                
                                # 逐个字段比较
                                need_update = False
                                changed_fields = []
                                
                                for field in COMPARE_FIELDS:
                                    mysql_value = mysql_item.get(field, 0)
                                    yida_field_name = FIELD_MAPPING[field]
                                    yida_field_value = yida_item.get(yida_field_name, '')
                                    
                                    # 根据字段名前缀判断字段类型
                                    if yida_field_name.startswith('numberField_'):
                                        # 数值类型字段，需要转换为数值进行比较
                                        try:
                                            yida_value = float(yida_field_value) if yida_field_value != '' else 0.0
                                            mysql_value = float(mysql_value) if mysql_value != '' else 0.0
                                        except (ValueError, TypeError):
                                            logging.warning(f"数值字段 {field} 的值 '{yida_field_value}' 无法转换为数值，使用默认值 0")
                                            yida_value = 0.0
                                            mysql_value = float(mysql_value) if mysql_value != '' else 0.0
                                        
                                        # 对于订单数量，确保比较整数
                                        if field == 'order_count':
                                            mysql_value = int(mysql_value) if mysql_value else 0
                                            yida_value = int(yida_value) if yida_value else 0
                                    else:
                                        # 文本类型字段，直接进行字符串比较
                                        yida_value = str(yida_field_value) if yida_field_value is not None else ''
                                        mysql_value = str(mysql_value) if mysql_value is not None else ''
                                    
                                    if mysql_value != yida_value:
                                        need_update = True
                                        changed_fields.append({
                                            'field': field,
                                            'old_value': yida_value,
                                            'new_value': mysql_value
                                        })
                                        logging.debug(f"字段 {field} 需要更新 - 旧值: {yida_value}, 新值: {mysql_value}")
                                
                                if need_update:
                                    try:
                                        form_data = {
                                            FIELD_MAPPING['project_code']: mysql_item['project_code'],
                                            FIELD_MAPPING['project_name']: mysql_item['project_name'],
                                            FIELD_MAPPING['store_code']: mysql_item['store_code'],
                                            FIELD_MAPPING['store_name']: mysql_item['store_name'],
                                            FIELD_MAPPING['sale_date']: mysql_item['sales_date'],
                                            FIELD_MAPPING['online_amount']: float(mysql_item['online_amount']),
                                            FIELD_MAPPING['offline_amount']: float(mysql_item['offline_amount']),
                                            FIELD_MAPPING['total_amount']: float(mysql_item['total_amount']),
                                            FIELD_MAPPING['order_count']: int(mysql_item['order_count']),
                                            FIELD_MAPPING['report_source']: mysql_item['report_source'],
                                            FIELD_MAPPING['url']: mysql_item['url']
                                        }
                                        
                                        form_instance_id = yida_data_dict[key]['form_instance_id']
                                        logging.info(f"开始更新记录 - 表单实例ID: {form_instance_id}")
                                        self.yida_client.update_form_data(form_instance_id, form_data)
                                        date_update_count += 1
                                        logging.info(f"更新记录成功，变更字段: {changed_fields}")
                                    except Exception as e:
                                        logging.error(f"更新记录时发生错误: {str(e)}")
                                        logging.error(f"错误数据 - 键: {key}, 表单实例ID: {form_instance_id}")
                                        date_error_count += 1
                            else:
                                try:
                                    form_data = {
                                        FIELD_MAPPING['project_code']: mysql_item['project_code'],
                                        FIELD_MAPPING['project_name']: mysql_item['project_name'],
                                        FIELD_MAPPING['store_code']: mysql_item['store_code'],
                                        FIELD_MAPPING['store_name']: mysql_item['store_name'],
                                        FIELD_MAPPING['sale_date']: mysql_item['sales_date'],
                                        FIELD_MAPPING['online_amount']: float(mysql_item['online_amount']),
                                        FIELD_MAPPING['offline_amount']: float(mysql_item['offline_amount']),
                                        FIELD_MAPPING['total_amount']: float(mysql_item['total_amount']),
                                        FIELD_MAPPING['order_count']: int(mysql_item['order_count']),
                                        FIELD_MAPPING['report_source']: mysql_item['report_source'],
                                        FIELD_MAPPING['url']: mysql_item['url']
                                    }
                                    logging.debug(f"准备插入新记录 - 数据: {form_data}")
                                    insert_data_list.append(form_data)
                                    date_insert_count += 1
                                except Exception as e:
                                    logging.error(f"转换数据格式失败: {str(e)}")
                                    logging.error(f"错误数据 - 键: {key}, MySQL数据: {mysql_item}")
                                    date_error_count += 1
                        except Exception as e:
                            logging.error(f"处理数据项失败: {str(e)}")
                            logging.error(f"错误数据 - 键: {key}, MySQL数据: {mysql_item}")
                            date_error_count += 1
                            continue
                    
                    # 批量插入新数据
                    if insert_data_list:
                        try:
                            logging.info(f"开始批量插入 {len(insert_data_list)} 条新记录")
                            self.yida_client.batch_create_form_data(insert_data_list)
                            logging.info(f"批量插入完成，共 {len(insert_data_list)} 条记录")
                        except Exception as e:
                            logging.error(f"批量插入数据失败: {str(e)}")
                            logging.error(f"错误数据列表: {insert_data_list}")
                            date_error_count += len(insert_data_list)
                    
                    # 更新总计数器
                    total_update_count += date_update_count
                    total_insert_count += date_insert_count
                    total_error_count += date_error_count
                    
                    logging.info(f"日期 {date} 处理完成 - 更新: {date_update_count} 条，插入: {date_insert_count} 条，错误: {date_error_count} 条")
                    
                except Exception as e:
                    logging.error(f"处理日期 {date} 时发生错误: {str(e)}")
                    total_error_count += 1
                    continue
            
            logging.info(f"数据同步完成！更新: {total_update_count} 条，插入: {total_insert_count} 条，错误: {total_error_count} 条")
            
        except Exception as e:
            logging.error(f"数据同步失败: {str(e)}")
            raise

def main():
    try:
        # 解析命令行参数
        parser = argparse.ArgumentParser(description='同步销售数据到宜搭表单')
        parser.add_argument('start_date', nargs='?', help='开始日期，格式：YYYYMMDD')
        parser.add_argument('end_date', nargs='?', help='结束日期，格式：YYYYMMDD')
        args = parser.parse_args()
        
        # 初始化客户端
        client = DataSyncClient()
        
        # 转换日期格式
        if args.start_date:
            start_date = f"{args.start_date[:4]}-{args.start_date[4:6]}-{args.start_date[6:]}"
        else:
            start_date = None
            
        if args.end_date:
            end_date = f"{args.end_date[:4]}-{args.end_date[4:6]}-{args.end_date[6:]}"
        else:
            end_date = None
        
        # 根据参数决定是否使用时间段
        if start_date or end_date:
            logging.info(f"使用时间段同步数据: {start_date} 至 {end_date}")
            client.sync_data(start_date, end_date)
        else:
            logging.info("使用默认增量同步（当天更新数据）")
            client.sync_data()
        #延迟1分钟
        sleep(60)
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
        current_date = datetime.now().strftime('%Y-%m-%d')
        logging.info(f"开始同步昨天与今天的销售数据: {yesterday} 至 {current_date}")
        client.sync_data(yesterday, current_date)
        logging.info(f"同步完成")
    except Exception as e:
        logging.error(f"程序执行出错: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()