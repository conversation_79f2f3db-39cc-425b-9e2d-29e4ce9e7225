2025-08-20 01:30:33,739 - INFO - 使用默认增量同步（当天更新数据）
2025-08-20 01:30:33,739 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-20 01:30:33,739 - INFO - 查询参数: ('2025-08-20',)
2025-08-20 01:30:33,832 - INFO - MySQL查询成功，增量数据（日期: 2025-08-20），共获取 0 条记录
2025-08-20 01:30:33,832 - ERROR - 未获取到MySQL数据
2025-08-20 01:31:33,846 - INFO - 开始同步昨天与今天的销售数据: 2025-08-19 至 2025-08-20
2025-08-20 01:31:33,846 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-20 01:31:33,846 - INFO - 查询参数: ('2025-08-19', '2025-08-20')
2025-08-20 01:31:34,018 - INFO - MySQL查询成功，时间段: 2025-08-19 至 2025-08-20，共获取 84 条记录
2025-08-20 01:31:34,018 - INFO - 获取到 1 个日期需要处理: ['2025-08-19']
2025-08-20 01:31:34,018 - INFO - 开始处理日期: 2025-08-19
2025-08-20 01:31:34,018 - INFO - Request Parameters - Page 1:
2025-08-20 01:31:34,018 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 01:31:34,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 01:31:42,128 - ERROR - 处理日期 2025-08-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 90217B3F-524B-724C-B8F1-79FBFF3FCD5D Response: {'code': 'ServiceUnavailable', 'requestid': '90217B3F-524B-724C-B8F1-79FBFF3FCD5D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 90217B3F-524B-724C-B8F1-79FBFF3FCD5D)
2025-08-20 01:31:42,128 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-20 01:31:42,128 - INFO - 同步完成
2025-08-20 04:30:33,848 - INFO - 使用默认增量同步（当天更新数据）
2025-08-20 04:30:33,848 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-20 04:30:33,848 - INFO - 查询参数: ('2025-08-20',)
2025-08-20 04:30:33,957 - INFO - MySQL查询成功，增量数据（日期: 2025-08-20），共获取 0 条记录
2025-08-20 04:30:33,957 - ERROR - 未获取到MySQL数据
2025-08-20 04:31:33,972 - INFO - 开始同步昨天与今天的销售数据: 2025-08-19 至 2025-08-20
2025-08-20 04:31:33,972 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-20 04:31:33,972 - INFO - 查询参数: ('2025-08-19', '2025-08-20')
2025-08-20 04:31:34,128 - INFO - MySQL查询成功，时间段: 2025-08-19 至 2025-08-20，共获取 84 条记录
2025-08-20 04:31:34,128 - INFO - 获取到 1 个日期需要处理: ['2025-08-19']
2025-08-20 04:31:34,128 - INFO - 开始处理日期: 2025-08-19
2025-08-20 04:31:34,143 - INFO - Request Parameters - Page 1:
2025-08-20 04:31:34,143 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 04:31:34,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 04:31:42,269 - ERROR - 处理日期 2025-08-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9A46F341-3E7B-73B5-AF09-00C3A9B259A0 Response: {'code': 'ServiceUnavailable', 'requestid': '9A46F341-3E7B-73B5-AF09-00C3A9B259A0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9A46F341-3E7B-73B5-AF09-00C3A9B259A0)
2025-08-20 04:31:42,269 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-20 04:31:42,269 - INFO - 同步完成
2025-08-20 07:30:33,571 - INFO - 使用默认增量同步（当天更新数据）
2025-08-20 07:30:33,571 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-20 07:30:33,571 - INFO - 查询参数: ('2025-08-20',)
2025-08-20 07:30:33,680 - INFO - MySQL查询成功，增量数据（日期: 2025-08-20），共获取 0 条记录
2025-08-20 07:30:33,680 - ERROR - 未获取到MySQL数据
2025-08-20 07:31:33,691 - INFO - 开始同步昨天与今天的销售数据: 2025-08-19 至 2025-08-20
2025-08-20 07:31:33,691 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-20 07:31:33,691 - INFO - 查询参数: ('2025-08-19', '2025-08-20')
2025-08-20 07:31:33,847 - INFO - MySQL查询成功，时间段: 2025-08-19 至 2025-08-20，共获取 84 条记录
2025-08-20 07:31:33,847 - INFO - 获取到 1 个日期需要处理: ['2025-08-19']
2025-08-20 07:31:33,847 - INFO - 开始处理日期: 2025-08-19
2025-08-20 07:31:33,863 - INFO - Request Parameters - Page 1:
2025-08-20 07:31:33,863 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 07:31:33,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 07:31:41,987 - ERROR - 处理日期 2025-08-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 6216B30C-EBB5-7CC1-955A-15FD0C87764A Response: {'code': 'ServiceUnavailable', 'requestid': '6216B30C-EBB5-7CC1-955A-15FD0C87764A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 6216B30C-EBB5-7CC1-955A-15FD0C87764A)
2025-08-20 07:31:41,987 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-20 07:31:41,987 - INFO - 同步完成
2025-08-20 10:30:34,074 - INFO - 使用默认增量同步（当天更新数据）
2025-08-20 10:30:34,074 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-20 10:30:34,074 - INFO - 查询参数: ('2025-08-20',)
2025-08-20 10:30:34,261 - INFO - MySQL查询成功，增量数据（日期: 2025-08-20），共获取 139 条记录
2025-08-20 10:30:34,261 - INFO - 获取到 3 个日期需要处理: ['2025-08-18', '2025-08-19', '2025-08-20']
2025-08-20 10:30:34,261 - INFO - 开始处理日期: 2025-08-18
2025-08-20 10:30:34,261 - INFO - Request Parameters - Page 1:
2025-08-20 10:30:34,261 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 10:30:34,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 10:30:42,381 - ERROR - 处理日期 2025-08-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 812288CB-D53D-7383-B96B-7AA43A0A2B05 Response: {'code': 'ServiceUnavailable', 'requestid': '812288CB-D53D-7383-B96B-7AA43A0A2B05', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 812288CB-D53D-7383-B96B-7AA43A0A2B05)
2025-08-20 10:30:42,381 - INFO - 开始处理日期: 2025-08-19
2025-08-20 10:30:42,381 - INFO - Request Parameters - Page 1:
2025-08-20 10:30:42,381 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 10:30:42,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 10:30:44,678 - INFO - Response - Page 1:
2025-08-20 10:30:44,678 - INFO - 第 1 页获取到 16 条记录
2025-08-20 10:30:45,194 - INFO - 查询完成，共获取到 16 条记录
2025-08-20 10:30:45,194 - INFO - 获取到 16 条表单数据
2025-08-20 10:30:45,194 - INFO - 当前日期 2025-08-19 有 131 条MySQL数据需要处理
2025-08-20 10:30:45,194 - INFO - 开始更新记录 - 表单实例ID: FINST-74766M71JJ4Y5R4MBBTTN4LF7RUQ3XLQFXHEMR4
2025-08-20 10:30:45,741 - INFO - 更新表单数据成功: FINST-74766M71JJ4Y5R4MBBTTN4LF7RUQ3XLQFXHEMR4
2025-08-20 10:30:45,741 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 911.0, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 14677.0, 'new_value': 8675.0}, {'field': 'total_amount', 'old_value': 15588.0, 'new_value': 8675.0}, {'field': 'order_count', 'old_value': 44, 'new_value': 58}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/48ea86e288414117a73a2a4617e1ec3f.jpg?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=%2B%2BjcC%2B3RO%2BGCcL2a%2Fc%2FFbpGCeU0%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/f656cbc09cad4cd2b1191d467baf397e.jpg?Expires=2070523207&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=UyQFHxawuzBEQiTlcWyMvIS58bw%3D'}]
2025-08-20 10:30:45,741 - INFO - 开始批量插入 130 条新记录
2025-08-20 10:30:46,006 - INFO - 批量插入响应状态码: 200
2025-08-20 10:30:46,006 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 02:30:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5FCCADC0-5795-7F21-96C5-D38C3E7C5F7A', 'x-acs-trace-id': '867ddc68d26e757187da44ec4f70933d', 'etag': '2AfZV+hnXP4U22BdEkyK27Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 10:30:46,006 - INFO - 批量插入响应体: {'result': ['FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMS3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMT3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMU3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMV3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMW3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMX3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMY3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMZ3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM04', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM14', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM24', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM34', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM44', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM54', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM64', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM74', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM84', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM94', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMA4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMB4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMC4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMD4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEME4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMF4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMG4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMH4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMI4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMJ4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMK4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEML4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMM4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMN4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMO4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMP4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMQ4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMR4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMS4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMT4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMU4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMV4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMW4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMX4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMY4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMZ4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM05', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM15', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM25', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM35', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM45', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM55']}
2025-08-20 10:30:46,006 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-20 10:30:46,006 - INFO - 成功插入的数据ID: ['FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMS3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMT3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMU3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMV3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMW3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMX3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMY3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMZ3', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM04', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM14', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM24', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM34', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM44', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM54', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM64', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM74', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM84', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM94', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMA4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMB4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMC4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMD4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEME4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMF4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMG4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMH4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMI4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMJ4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMK4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEML4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMM4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMN4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMO4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMP4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMQ4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMR4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMS4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMT4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMU4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMV4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMW4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMX4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMY4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEMZ4', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM05', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM15', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM25', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM35', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM45', 'FINST-G5E664710N5YOW1PF4I63DJC32PD2L1KVCJEM55']
2025-08-20 10:30:51,241 - INFO - 批量插入响应状态码: 200
2025-08-20 10:30:51,241 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 02:30:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2394', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BC1371E8-78C1-7EBE-8DED-6BE532BC7D96', 'x-acs-trace-id': '380bc55441d6a941a924b4efc230b432', 'etag': '2XVQLR/rf01x+S7nvBvAdJA4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 10:30:51,241 - INFO - 批量插入响应体: {'result': ['FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMI', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMJ', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMK', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEML', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMM', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMN', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMO', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMP', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMQ', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMR', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMS', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMT', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMU', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMV', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMW', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMX', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMY', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMZ', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM01', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM11', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM21', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM31', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM41', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM51', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM61', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM71', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM81', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM91', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMA1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMB1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMC1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMD1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEME1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMF1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMG1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMH1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMI1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMJ1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMK1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEML1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMM1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMN1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMO1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMP1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMQ1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMR1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMS1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMT1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMU1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMV1']}
2025-08-20 10:30:51,241 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-20 10:30:51,241 - INFO - 成功插入的数据ID: ['FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMI', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMJ', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMK', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEML', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMM', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMN', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMO', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMP', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMQ', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMR', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMS', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMT', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMU', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMV', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMW', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMX', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMY', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMZ', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM01', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM11', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM21', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM31', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM41', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM51', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM61', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM71', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM81', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEM91', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMA1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMB1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMC1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMD1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEME1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMF1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMG1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMH1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMI1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMJ1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMK1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEML1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMM1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMN1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMO1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMP1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMQ1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMR1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMS1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMT1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMU1', 'FINST-AVF66WB1RX5YGJMH6838C44CUH10233OVCJEMV1']
2025-08-20 10:30:56,444 - INFO - 批量插入响应状态码: 200
2025-08-20 10:30:56,444 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 02:30:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1452', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '90056FF6-9FB9-7F0B-80C2-224A6AA681F6', 'x-acs-trace-id': '894a927d79af6b59e92d673e04b7c9a9', 'etag': '1fJX4oRzlp1m6gySjnYb0yg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 10:30:56,444 - INFO - 批量插入响应体: {'result': ['FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM41', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM51', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM61', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM71', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM81', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM91', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMA1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMB1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMC1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMD1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEME1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMF1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMG1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMH1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMI1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMJ1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMK1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEML1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMM1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMN1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMO1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMP1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMQ1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMR1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMS1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMT1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMU1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMV1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMW1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMX1']}
2025-08-20 10:30:56,444 - INFO - 批量插入表单数据成功，批次 3，共 30 条记录
2025-08-20 10:30:56,444 - INFO - 成功插入的数据ID: ['FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM41', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM51', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM61', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM71', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM81', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEM91', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMA1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMB1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMC1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMD1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEME1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMF1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMG1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMH1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMI1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMJ1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMK1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEML1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMM1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMN1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMO1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMP1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMQ1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMR1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMS1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMT1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMU1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMV1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMW1', 'FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMX1']
2025-08-20 10:31:01,459 - INFO - 批量插入完成，共 130 条记录
2025-08-20 10:31:01,459 - INFO - 日期 2025-08-19 处理完成 - 更新: 1 条，插入: 130 条，错误: 0 条
2025-08-20 10:31:01,459 - INFO - 开始处理日期: 2025-08-20
2025-08-20 10:31:01,459 - INFO - Request Parameters - Page 1:
2025-08-20 10:31:01,459 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 10:31:01,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 10:31:02,022 - INFO - Response - Page 1:
2025-08-20 10:31:02,022 - INFO - 查询完成，共获取到 0 条记录
2025-08-20 10:31:02,022 - INFO - 获取到 0 条表单数据
2025-08-20 10:31:02,022 - INFO - 当前日期 2025-08-20 有 3 条MySQL数据需要处理
2025-08-20 10:31:02,022 - INFO - 开始批量插入 3 条新记录
2025-08-20 10:31:02,194 - INFO - 批量插入响应状态码: 200
2025-08-20 10:31:02,194 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 02:31:04 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '159', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A5604CFC-6B19-749B-ADF5-0AD700CEA225', 'x-acs-trace-id': 'e5ee27dbfb548d028fb0319e01ed419e', 'etag': '1rRjwuILbuI2gjvFsGtulgA9', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 10:31:02,194 - INFO - 批量插入响应体: {'result': ['FINST-S0E660A18N4YU0JP886LR4GRRO1R3EJWVCJEMU41', 'FINST-S0E660A18N4YU0JP886LR4GRRO1R3EJWVCJEMV41', 'FINST-S0E660A18N4YU0JP886LR4GRRO1R3EJWVCJEMW41']}
2025-08-20 10:31:02,194 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-20 10:31:02,194 - INFO - 成功插入的数据ID: ['FINST-S0E660A18N4YU0JP886LR4GRRO1R3EJWVCJEMU41', 'FINST-S0E660A18N4YU0JP886LR4GRRO1R3EJWVCJEMV41', 'FINST-S0E660A18N4YU0JP886LR4GRRO1R3EJWVCJEMW41']
2025-08-20 10:31:07,209 - INFO - 批量插入完成，共 3 条记录
2025-08-20 10:31:07,209 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-08-20 10:31:07,209 - INFO - 数据同步完成！更新: 1 条，插入: 133 条，错误: 1 条
2025-08-20 10:32:07,220 - INFO - 开始同步昨天与今天的销售数据: 2025-08-19 至 2025-08-20
2025-08-20 10:32:07,220 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-20 10:32:07,220 - INFO - 查询参数: ('2025-08-19', '2025-08-20')
2025-08-20 10:32:07,407 - INFO - MySQL查询成功，时间段: 2025-08-19 至 2025-08-20，共获取 528 条记录
2025-08-20 10:32:07,407 - INFO - 获取到 2 个日期需要处理: ['2025-08-19', '2025-08-20']
2025-08-20 10:32:07,407 - INFO - 开始处理日期: 2025-08-19
2025-08-20 10:32:07,407 - INFO - Request Parameters - Page 1:
2025-08-20 10:32:07,407 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 10:32:07,407 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 10:32:15,532 - ERROR - 处理日期 2025-08-19 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-2591-7190-96CE-************ Response: {'code': 'ServiceUnavailable', 'requestid': '********-2591-7190-96CE-************', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-2591-7190-96CE-************)
2025-08-20 10:32:15,532 - INFO - 开始处理日期: 2025-08-20
2025-08-20 10:32:15,532 - INFO - Request Parameters - Page 1:
2025-08-20 10:32:15,532 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 10:32:15,532 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 10:32:16,063 - INFO - Response - Page 1:
2025-08-20 10:32:16,063 - INFO - 第 1 页获取到 3 条记录
2025-08-20 10:32:16,563 - INFO - 查询完成，共获取到 3 条记录
2025-08-20 10:32:16,563 - INFO - 获取到 3 条表单数据
2025-08-20 10:32:16,563 - INFO - 当前日期 2025-08-20 有 3 条MySQL数据需要处理
2025-08-20 10:32:16,563 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 10:32:16,563 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-20 10:32:16,563 - INFO - 同步完成
2025-08-20 13:30:33,541 - INFO - 使用默认增量同步（当天更新数据）
2025-08-20 13:30:33,541 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-20 13:30:33,541 - INFO - 查询参数: ('2025-08-20',)
2025-08-20 13:30:33,729 - INFO - MySQL查询成功，增量数据（日期: 2025-08-20），共获取 156 条记录
2025-08-20 13:30:33,729 - INFO - 获取到 5 个日期需要处理: ['2025-08-09', '2025-08-13', '2025-08-18', '2025-08-19', '2025-08-20']
2025-08-20 13:30:33,729 - INFO - 开始处理日期: 2025-08-09
2025-08-20 13:30:33,744 - INFO - Request Parameters - Page 1:
2025-08-20 13:30:33,744 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:30:33,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:30:41,864 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 84FF1E4E-76D1-7CFB-BC55-55C3974298A3 Response: {'code': 'ServiceUnavailable', 'requestid': '84FF1E4E-76D1-7CFB-BC55-55C3974298A3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 84FF1E4E-76D1-7CFB-BC55-55C3974298A3)
2025-08-20 13:30:41,864 - INFO - 开始处理日期: 2025-08-13
2025-08-20 13:30:41,864 - INFO - Request Parameters - Page 1:
2025-08-20 13:30:41,864 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:30:41,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:30:49,677 - INFO - Response - Page 1:
2025-08-20 13:30:49,677 - INFO - 第 1 页获取到 50 条记录
2025-08-20 13:30:50,177 - INFO - Request Parameters - Page 2:
2025-08-20 13:30:50,177 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:30:50,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:30:58,302 - ERROR - 处理日期 2025-08-13 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5770FE97-B488-713D-AFD9-C518CE223915 Response: {'code': 'ServiceUnavailable', 'requestid': '5770FE97-B488-713D-AFD9-C518CE223915', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5770FE97-B488-713D-AFD9-C518CE223915)
2025-08-20 13:30:58,302 - INFO - 开始处理日期: 2025-08-18
2025-08-20 13:30:58,302 - INFO - Request Parameters - Page 1:
2025-08-20 13:30:58,302 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:30:58,302 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:30:59,083 - INFO - Response - Page 1:
2025-08-20 13:30:59,083 - INFO - 第 1 页获取到 50 条记录
2025-08-20 13:30:59,599 - INFO - Request Parameters - Page 2:
2025-08-20 13:30:59,599 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:30:59,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:00,333 - INFO - Response - Page 2:
2025-08-20 13:31:00,333 - INFO - 第 2 页获取到 50 条记录
2025-08-20 13:31:00,833 - INFO - Request Parameters - Page 3:
2025-08-20 13:31:00,833 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:00,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:01,661 - INFO - Response - Page 3:
2025-08-20 13:31:01,661 - INFO - 第 3 页获取到 50 条记录
2025-08-20 13:31:02,161 - INFO - Request Parameters - Page 4:
2025-08-20 13:31:02,161 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:02,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:02,880 - INFO - Response - Page 4:
2025-08-20 13:31:02,880 - INFO - 第 4 页获取到 50 条记录
2025-08-20 13:31:03,396 - INFO - Request Parameters - Page 5:
2025-08-20 13:31:03,396 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:03,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:04,224 - INFO - Response - Page 5:
2025-08-20 13:31:04,224 - INFO - 第 5 页获取到 50 条记录
2025-08-20 13:31:04,739 - INFO - Request Parameters - Page 6:
2025-08-20 13:31:04,739 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:04,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:05,474 - INFO - Response - Page 6:
2025-08-20 13:31:05,474 - INFO - 第 6 页获取到 50 条记录
2025-08-20 13:31:05,989 - INFO - Request Parameters - Page 7:
2025-08-20 13:31:05,989 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:05,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:06,771 - INFO - Response - Page 7:
2025-08-20 13:31:06,771 - INFO - 第 7 页获取到 50 条记录
2025-08-20 13:31:07,286 - INFO - Request Parameters - Page 8:
2025-08-20 13:31:07,286 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:07,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:08,036 - INFO - Response - Page 8:
2025-08-20 13:31:08,036 - INFO - 第 8 页获取到 50 条记录
2025-08-20 13:31:08,552 - INFO - Request Parameters - Page 9:
2025-08-20 13:31:08,552 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:08,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:09,333 - INFO - Response - Page 9:
2025-08-20 13:31:09,333 - INFO - 第 9 页获取到 50 条记录
2025-08-20 13:31:09,849 - INFO - Request Parameters - Page 10:
2025-08-20 13:31:09,849 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:09,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:10,708 - INFO - Response - Page 10:
2025-08-20 13:31:10,708 - INFO - 第 10 页获取到 50 条记录
2025-08-20 13:31:11,208 - INFO - Request Parameters - Page 11:
2025-08-20 13:31:11,208 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:11,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:11,911 - INFO - Response - Page 11:
2025-08-20 13:31:11,911 - INFO - 第 11 页获取到 26 条记录
2025-08-20 13:31:12,411 - INFO - 查询完成，共获取到 526 条记录
2025-08-20 13:31:12,411 - INFO - 获取到 526 条表单数据
2025-08-20 13:31:12,411 - INFO - 当前日期 2025-08-18 有 1 条MySQL数据需要处理
2025-08-20 13:31:12,411 - INFO - 开始更新记录 - 表单实例ID: FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMO3
2025-08-20 13:31:12,989 - INFO - 更新表单数据成功: FINST-AI866781ML4Y0TPSBOEJ1BIWKX5F33XBHXHEMO3
2025-08-20 13:31:12,989 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 7124.0, 'new_value': 7125.0}, {'field': 'total_amount', 'old_value': 7124.0, 'new_value': 7125.0}]
2025-08-20 13:31:12,989 - INFO - 日期 2025-08-18 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 13:31:12,989 - INFO - 开始处理日期: 2025-08-19
2025-08-20 13:31:12,989 - INFO - Request Parameters - Page 1:
2025-08-20 13:31:12,989 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:12,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:13,786 - INFO - Response - Page 1:
2025-08-20 13:31:13,786 - INFO - 第 1 页获取到 50 条记录
2025-08-20 13:31:14,286 - INFO - Request Parameters - Page 2:
2025-08-20 13:31:14,286 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:14,286 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:15,020 - INFO - Response - Page 2:
2025-08-20 13:31:15,020 - INFO - 第 2 页获取到 50 条记录
2025-08-20 13:31:15,536 - INFO - Request Parameters - Page 3:
2025-08-20 13:31:15,536 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:15,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:16,255 - INFO - Response - Page 3:
2025-08-20 13:31:16,255 - INFO - 第 3 页获取到 46 条记录
2025-08-20 13:31:16,755 - INFO - 查询完成，共获取到 146 条记录
2025-08-20 13:31:16,755 - INFO - 获取到 146 条表单数据
2025-08-20 13:31:16,755 - INFO - 当前日期 2025-08-19 有 144 条MySQL数据需要处理
2025-08-20 13:31:16,755 - INFO - 开始更新记录 - 表单实例ID: FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMP1
2025-08-20 13:31:17,286 - INFO - 更新表单数据成功: FINST-RE766GC1DW5Y8BXSA9M4DCD6C1DV3R3SVCJEMP1
2025-08-20 13:31:17,286 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6580.0, 'new_value': 11898.29}, {'field': 'total_amount', 'old_value': 6580.0, 'new_value': 11898.29}, {'field': 'order_count', 'old_value': 126, 'new_value': 328}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/54b5a66eeef24fe7ab45c820e3534087.jpg?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=yGO3tVFY2uaFBYILxVL%2FzzFiH8I%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/751f6afcc0704dea906f7d14f9d0e5bd.jpg?Expires=2070523207&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=kuGf0DsAdKPrrqBNqN43EvbT81o%3D'}]
2025-08-20 13:31:17,286 - INFO - 开始批量插入 13 条新记录
2025-08-20 13:31:17,458 - INFO - 批量插入响应状态码: 200
2025-08-20 13:31:17,458 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:31:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A4FD8B78-BFEC-7AFF-8EE6-B368A6D76251', 'x-acs-trace-id': '7028e30e065b93aa0df78f59f9131ae1', 'etag': '6XN35DpM9BIgnPU8aJlgl4A6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:31:17,458 - INFO - 批量插入响应体: {'result': ['FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMFJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMGJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMHJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMIJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMJJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMKJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMLJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMMJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMNJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMOJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMPJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMQJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMRJ']}
2025-08-20 13:31:17,474 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-08-20 13:31:17,474 - INFO - 成功插入的数据ID: ['FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMFJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMGJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMHJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMIJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMJJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMKJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMLJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53KPPBJJEMMJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMNJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMOJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMPJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMQJ', 'FINST-DIC66I91FX5YT1VG6TXRPCTE1VX53LPPBJJEMRJ']
2025-08-20 13:31:22,489 - INFO - 批量插入完成，共 13 条记录
2025-08-20 13:31:22,489 - INFO - 日期 2025-08-19 处理完成 - 更新: 1 条，插入: 13 条，错误: 0 条
2025-08-20 13:31:22,489 - INFO - 开始处理日期: 2025-08-20
2025-08-20 13:31:22,489 - INFO - Request Parameters - Page 1:
2025-08-20 13:31:22,489 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:31:22,489 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:31:23,005 - INFO - Response - Page 1:
2025-08-20 13:31:23,005 - INFO - 第 1 页获取到 3 条记录
2025-08-20 13:31:23,520 - INFO - 查询完成，共获取到 3 条记录
2025-08-20 13:31:23,520 - INFO - 获取到 3 条表单数据
2025-08-20 13:31:23,520 - INFO - 当前日期 2025-08-20 有 3 条MySQL数据需要处理
2025-08-20 13:31:23,520 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 13:31:23,520 - INFO - 数据同步完成！更新: 2 条，插入: 13 条，错误: 2 条
2025-08-20 13:32:23,531 - INFO - 开始同步昨天与今天的销售数据: 2025-08-19 至 2025-08-20
2025-08-20 13:32:23,531 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-20 13:32:23,531 - INFO - 查询参数: ('2025-08-19', '2025-08-20')
2025-08-20 13:32:23,718 - INFO - MySQL查询成功，时间段: 2025-08-19 至 2025-08-20，共获取 554 条记录
2025-08-20 13:32:23,718 - INFO - 获取到 2 个日期需要处理: ['2025-08-19', '2025-08-20']
2025-08-20 13:32:23,718 - INFO - 开始处理日期: 2025-08-19
2025-08-20 13:32:23,718 - INFO - Request Parameters - Page 1:
2025-08-20 13:32:23,718 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:32:23,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:32:24,515 - INFO - Response - Page 1:
2025-08-20 13:32:24,515 - INFO - 第 1 页获取到 50 条记录
2025-08-20 13:32:25,031 - INFO - Request Parameters - Page 2:
2025-08-20 13:32:25,031 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:32:25,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:32:25,890 - INFO - Response - Page 2:
2025-08-20 13:32:25,890 - INFO - 第 2 页获取到 50 条记录
2025-08-20 13:32:26,406 - INFO - Request Parameters - Page 3:
2025-08-20 13:32:26,406 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:32:26,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:32:27,140 - INFO - Response - Page 3:
2025-08-20 13:32:27,140 - INFO - 第 3 页获取到 50 条记录
2025-08-20 13:32:27,656 - INFO - Request Parameters - Page 4:
2025-08-20 13:32:27,656 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:32:27,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:32:28,249 - INFO - Response - Page 4:
2025-08-20 13:32:28,249 - INFO - 第 4 页获取到 9 条记录
2025-08-20 13:32:28,765 - INFO - 查询完成，共获取到 159 条记录
2025-08-20 13:32:28,765 - INFO - 获取到 159 条表单数据
2025-08-20 13:32:28,765 - INFO - 当前日期 2025-08-19 有 530 条MySQL数据需要处理
2025-08-20 13:32:28,765 - INFO - 开始更新记录 - 表单实例ID: FINST-AAG66KB1DI2YPNWIFUWSNCRMV7I32R0RKKHEM481
2025-08-20 13:32:29,312 - INFO - 更新表单数据成功: FINST-AAG66KB1DI2YPNWIFUWSNCRMV7I32R0RKKHEM481
2025-08-20 13:32:29,312 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 9664.01, 'new_value': 8413.12}, {'field': 'total_amount', 'old_value': 9664.01, 'new_value': 8413.12}, {'field': 'order_count', 'old_value': 70, 'new_value': 72}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/cd0c196e04534415a2fba8c215dec0e2.jpg?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=6CJdqZ47zJd070oLIPS64htlUoE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/f7721506a1704275ac3163bdc5276a65.jpg?Expires=2070523207&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=MY9fjDFriTt2Cv%2F67R5DWxzsa5Y%3D'}]
2025-08-20 13:32:29,312 - INFO - 开始批量插入 371 条新记录
2025-08-20 13:32:29,593 - INFO - 批量插入响应状态码: 200
2025-08-20 13:32:29,593 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:32:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2392', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F47EABC4-4620-73BD-A4A8-547DE3BA6309', 'x-acs-trace-id': '70842a1a4deb03a86895005a3db659dd', 'etag': '2QruhSt9nUZ6zQ9puxIVY2g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:32:29,593 - INFO - 批量插入响应体: {'result': ['FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMG', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMH', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMI', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMJ', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMK', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEML', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMM', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMN', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMO', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMP', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMQ', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMR', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMS', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMT', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMU', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMV', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMW', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMX', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMY', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMZ', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM01', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM11', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM21', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM31', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM41', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM51', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM61', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM71', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM81', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM91', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMA1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMB1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMC1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMD1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEME1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMF1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMG1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMH1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMI1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMJ1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMK1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEML1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMM1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMN1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMO1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMP1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMQ1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMR1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMS1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMT1']}
2025-08-20 13:32:29,593 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-20 13:32:29,593 - INFO - 成功插入的数据ID: ['FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMG', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMH', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMI', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMJ', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMK', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEML', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMM', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMN', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMO', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE26D9DJJEMP', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMQ', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMR', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMS', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMT', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMU', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMV', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMW', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMX', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMY', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMZ', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM01', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM11', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM21', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM31', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM41', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM51', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM61', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM71', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM81', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEM91', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMA1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMB1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMC1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMD1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEME1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMF1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMG1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMH1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMI1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMJ1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMK1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEML1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMM1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMN1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMO1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMP1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMQ1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMR1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMS1', 'FINST-CL966QB1GP6Y4JVBA9MEY75CQTAE27D9DJJEMT1']
2025-08-20 13:32:34,859 - INFO - 批量插入响应状态码: 200
2025-08-20 13:32:34,859 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:32:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3EA490D1-102F-7112-B339-7521DBAC6442', 'x-acs-trace-id': '5ca7c86c3817c806c8427a3b02dd81f2', 'etag': '26zxSC7J93+MsaejC8JlWlA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:32:34,859 - INFO - 批量插入响应体: {'result': ['FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMQ3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMR3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMS3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMT3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMU3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMV3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMW3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMX3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMY3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMZ3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM04', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM14', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM24', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM34', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM44', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM54', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM64', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM74', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM84', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM94', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMA4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMB4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMC4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMD4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEME4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMF4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMG4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMH4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMI4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMJ4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMK4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEML4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMM4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMN4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMO4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMP4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMQ4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMR4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMS4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMT4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMU4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMV4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMW4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMX4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMY4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMZ4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM05', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM15', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM25', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM35']}
2025-08-20 13:32:34,859 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-20 13:32:34,859 - INFO - 成功插入的数据ID: ['FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMQ3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMR3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMS3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMT3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMU3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMV3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMW3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMX3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMY3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMZ3', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM04', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM14', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM24', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM34', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM44', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM54', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM64', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM74', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM84', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM94', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMA4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMB4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMC4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMD4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEME4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMF4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMG4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMH4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMI4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMJ4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMK4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEML4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMM4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMN4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMO4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMP4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMQ4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMR4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMS4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMT4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMU4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMV4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMW4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMX4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMY4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEMZ4', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM05', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM15', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM25', 'FINST-KLF66HD1LY5YO88B8H2QWA6R8AVI39FDDJJEM35']
2025-08-20 13:32:40,135 - INFO - 批量插入响应状态码: 200
2025-08-20 13:32:40,135 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:32:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '12838570-DD68-7AF2-8DD2-75943B552473', 'x-acs-trace-id': 'f3e19138522c17c3cbff061c541236dd', 'etag': '2cLO2fZzM4SG4wYYcCBALgg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:32:40,135 - INFO - 批量插入响应体: {'result': ['FINST-8LG66D71DR1YEBKRDYNC99BUGPXI23IHDJJEM781', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM881', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM981', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMA81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMB81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMC81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMD81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEME81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMF81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMG81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMH81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMI81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMJ81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMK81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEML81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMM81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMN81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMO81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMP81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMQ81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMR81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMS81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMT81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMU81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMV81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMW81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMX81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMY81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMZ81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM091', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM191', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM291', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM391', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM491', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM591', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM691', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM791', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM891', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM991', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMA91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMB91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMC91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMD91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEME91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMF91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMG91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMH91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMI91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMJ91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMK91']}
2025-08-20 13:32:40,135 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-20 13:32:40,135 - INFO - 成功插入的数据ID: ['FINST-8LG66D71DR1YEBKRDYNC99BUGPXI23IHDJJEM781', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM881', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM981', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMA81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMB81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMC81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMD81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEME81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMF81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMG81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMH81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMI81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMJ81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMK81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEML81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMM81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMN81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMO81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMP81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMQ81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMR81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMS81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMT81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMU81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMV81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMW81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMX81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMY81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMZ81', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM091', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM191', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM291', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM391', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM491', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM591', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM691', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM791', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM891', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEM991', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMA91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMB91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMC91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMD91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEME91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMF91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMG91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMH91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMI91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMJ91', 'FINST-8LG66D71DR1YEBKRDYNC99BUGPXI24IHDJJEMK91']
2025-08-20 13:32:45,401 - INFO - 批量插入响应状态码: 200
2025-08-20 13:32:45,401 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:32:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8FAFA7CC-264D-7D9A-927F-CCE57921FB72', 'x-acs-trace-id': '1c0d21af40d8489aed02f7a54248ae0e', 'etag': '2MsMrTHW8O0uYEiJ+9Z5/gg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:32:45,401 - INFO - 批量插入响应体: {'result': ['FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMQH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMRH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMSH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMTH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMUH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMVH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMWH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMXH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMYH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMZH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM0I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM1I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM2I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM3I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM4I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM5I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM6I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM7I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM8I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM9I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMAI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMBI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMCI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMDI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMEI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMFI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMGI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMHI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMII', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMJI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMKI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMLI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMMI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMNI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMOI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMPI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMQI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMRI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMSI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMTI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMUI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMVI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMWI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEMXI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEMYI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEMZI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEM0J', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEM1J', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEM2J', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEM3J']}
2025-08-20 13:32:45,401 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-08-20 13:32:45,401 - INFO - 成功插入的数据ID: ['FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMQH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMRH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMSH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMTH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMUH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMVH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMWH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMXH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMYH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMZH', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM0I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM1I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM2I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM3I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM4I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM5I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM6I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM7I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM8I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEM9I', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMAI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMBI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMCI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMDI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMEI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMFI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMGI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMHI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMII', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMJI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMKI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMLI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMMI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMNI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMOI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMPI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMQI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMRI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMSI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMTI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMUI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMVI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS37KLDJJEMWI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEMXI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEMYI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEMZI', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEM0J', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEM1J', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEM2J', 'FINST-TQB66671X15YOA1F7SW3CDHVWCHS38KLDJJEM3J']
2025-08-20 13:32:50,651 - INFO - 批量插入响应状态码: 200
2025-08-20 13:32:50,651 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:32:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6B7252A1-4D81-7E5D-9E95-0ACBEC64BA40', 'x-acs-trace-id': 'a8918b232e8cc1e4a5f697e136fdccb9', 'etag': '2xLEM3owP+KznZV+5xJXO5Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:32:50,651 - INFO - 批量插入响应体: {'result': ['FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMUJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMVJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMWJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMXJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMYJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMZJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM0K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM1K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM2K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM3K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM4K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM5K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM6K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM7K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM8K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM9K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMAK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMBK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMCK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMDK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMEK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMFK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMGK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMHK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMIK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMJK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMKK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMLK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMMK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMNK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMOK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMPK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMQK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMRK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMSK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMTK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMUK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMVK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMWK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMXK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMYK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMZK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM0L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM1L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM2L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM3L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM4L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK35MPDJJEM5L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK35MPDJJEM6L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK35MPDJJEM7L1']}
2025-08-20 13:32:50,651 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-08-20 13:32:50,651 - INFO - 成功插入的数据ID: ['FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMUJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMVJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMWJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMXJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMYJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMZJ1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM0K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM1K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM2K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM3K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM4K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM5K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM6K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM7K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM8K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM9K1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMAK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMBK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMCK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMDK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMEK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMFK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMGK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMHK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMIK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMJK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMKK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMLK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMMK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMNK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMOK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMPK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMQK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMRK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMSK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMTK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMUK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMVK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMWK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMXK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMYK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEMZK1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM0L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM1L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM2L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM3L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK34MPDJJEM4L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK35MPDJJEM5L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK35MPDJJEM6L1', 'FINST-0Q8661D1HP1YALFOD008J6Z1B7CK35MPDJJEM7L1']
2025-08-20 13:32:55,901 - INFO - 批量插入响应状态码: 200
2025-08-20 13:32:55,901 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:32:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3B7A8C7F-5BC8-7458-A41C-309DBB69EE92', 'x-acs-trace-id': '2106e1653179f6a4f86703e89b84c9ca', 'etag': '29nZ++I4i8Wx+Gl+MtKQgHA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:32:55,901 - INFO - 批量插入响应体: {'result': ['FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM52', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM62', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM72', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM82', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM92', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMA2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMB2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMC2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMD2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEME2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMF2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMG2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMH2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMI2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMJ2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMK2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEML2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMM2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMN2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMO2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMP2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMQ2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMR2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMS2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMT2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMU2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMV2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMW2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMX2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMY2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMZ2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM03', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM13', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM23', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM33', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM43', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM53', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM63', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM73', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM83', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM93', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMA3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMB3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMC3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMD3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEME3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMF3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMG3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMH3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMI3']}
2025-08-20 13:32:55,901 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-08-20 13:32:55,901 - INFO - 成功插入的数据ID: ['FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM52', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM62', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM72', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM82', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM92', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMA2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMB2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMC2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMD2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEME2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMF2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMG2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMH2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMI2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMJ2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMK2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEML2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMM2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMN2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMO2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMP2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMQ2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMR2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMS2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMT2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMU2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMV2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMW2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMX2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMY2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMZ2', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM03', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM13', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM23', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM33', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM43', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM53', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM63', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM73', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM83', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEM93', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMA3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMB3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMC3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMD3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEME3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMF3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMG3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMH3', 'FINST-RNA66D71WW5YT4UZB30I877XLQAK30OTDJJEMI3']
2025-08-20 13:33:01,151 - INFO - 批量插入响应状态码: 200
2025-08-20 13:33:01,151 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:33:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '15E29FC4-F668-71AE-9CEC-0F3DE2C60932', 'x-acs-trace-id': 'b34536884fc1120ecbc63b710f332078', 'etag': '2ri5G7kDmdt6Oo7UKfI/sCg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:33:01,151 - INFO - 批量插入响应体: {'result': ['FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMQ2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMR2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMS2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMT2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMU2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMV2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMW2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMX2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMY2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMZ2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM03', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM13', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM23', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM33', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM43', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM53', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM63', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM73', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM83', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM93', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMA3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMB3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMC3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMD3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEME3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMF3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMG3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMH3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMI3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMJ3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMK3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEML3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMM3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMN3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMO3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMP3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMQ3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMR3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMS3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMT3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMU3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMV3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMW3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMX3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMY3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMZ3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM04', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM14', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM24', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM34']}
2025-08-20 13:33:01,151 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-08-20 13:33:01,151 - INFO - 成功插入的数据ID: ['FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMQ2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMR2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMS2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMT2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMU2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMV2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMW2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMX2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMY2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMZ2', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM03', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM13', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM23', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM33', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM43', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM53', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM63', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM73', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM83', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM93', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMA3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMB3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMC3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMD3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEME3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMF3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMG3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMH3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMI3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMJ3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMK3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEML3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMM3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMN3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMO3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMP3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMQ3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMR3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMS3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMT3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMU3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMV3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMW3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMX3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMY3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEMZ3', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM04', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM14', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM24', 'FINST-6I766IB1HL5Y08AF6NKVO4A1GNFQ3SPXDJJEM34']
2025-08-20 13:33:06,369 - INFO - 批量插入响应状态码: 200
2025-08-20 13:33:06,369 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 05:33:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3DFDE2D4-1D00-7D40-904A-5F7E12B41438', 'x-acs-trace-id': '302dd1c5e25bb817d370d8deb462ae93', 'etag': '17xir9WZZCyJ1gKuP7FDTNQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 13:33:06,369 - INFO - 批量插入响应体: {'result': ['FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMQL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMRL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMSL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMTL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMUL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMVL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMWL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMXL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMYL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMZL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM0M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM1M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM2M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM3M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM4M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM5M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM6M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM7M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM8M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM9M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMAM']}
2025-08-20 13:33:06,369 - INFO - 批量插入表单数据成功，批次 8，共 21 条记录
2025-08-20 13:33:06,369 - INFO - 成功插入的数据ID: ['FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMQL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMRL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMSL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMTL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMUL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMVL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMWL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMXL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMYL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMZL', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM0M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM1M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM2M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM3M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM4M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM5M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM6M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM7M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM8M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEM9M', 'FINST-8LG66D71B21Y7FKKEENE344USBJQ2OQ1EJJEMAM']
2025-08-20 13:33:11,385 - INFO - 批量插入完成，共 371 条记录
2025-08-20 13:33:11,385 - INFO - 日期 2025-08-19 处理完成 - 更新: 1 条，插入: 371 条，错误: 0 条
2025-08-20 13:33:11,385 - INFO - 开始处理日期: 2025-08-20
2025-08-20 13:33:11,385 - INFO - Request Parameters - Page 1:
2025-08-20 13:33:11,385 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 13:33:11,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 13:33:11,901 - INFO - Response - Page 1:
2025-08-20 13:33:11,901 - INFO - 第 1 页获取到 3 条记录
2025-08-20 13:33:12,401 - INFO - 查询完成，共获取到 3 条记录
2025-08-20 13:33:12,401 - INFO - 获取到 3 条表单数据
2025-08-20 13:33:12,401 - INFO - 当前日期 2025-08-20 有 3 条MySQL数据需要处理
2025-08-20 13:33:12,401 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 13:33:12,401 - INFO - 数据同步完成！更新: 1 条，插入: 371 条，错误: 0 条
2025-08-20 13:33:12,401 - INFO - 同步完成
2025-08-20 16:30:33,548 - INFO - 使用默认增量同步（当天更新数据）
2025-08-20 16:30:33,548 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-20 16:30:33,548 - INFO - 查询参数: ('2025-08-20',)
2025-08-20 16:30:33,735 - INFO - MySQL查询成功，增量数据（日期: 2025-08-20），共获取 158 条记录
2025-08-20 16:30:33,735 - INFO - 获取到 7 个日期需要处理: ['2025-08-01', '2025-08-02', '2025-08-09', '2025-08-13', '2025-08-18', '2025-08-19', '2025-08-20']
2025-08-20 16:30:33,735 - INFO - 开始处理日期: 2025-08-01
2025-08-20 16:30:33,735 - INFO - Request Parameters - Page 1:
2025-08-20 16:30:33,735 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:30:33,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:30:41,860 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0C9E87D4-0540-7317-8EE4-6FFEE18EC292 Response: {'code': 'ServiceUnavailable', 'requestid': '0C9E87D4-0540-7317-8EE4-6FFEE18EC292', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0C9E87D4-0540-7317-8EE4-6FFEE18EC292)
2025-08-20 16:30:41,860 - INFO - 开始处理日期: 2025-08-02
2025-08-20 16:30:41,860 - INFO - Request Parameters - Page 1:
2025-08-20 16:30:41,860 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:30:41,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:30:43,517 - INFO - Response - Page 1:
2025-08-20 16:30:43,517 - INFO - 第 1 页获取到 50 条记录
2025-08-20 16:30:44,032 - INFO - Request Parameters - Page 2:
2025-08-20 16:30:44,032 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:30:44,032 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:30:52,137 - ERROR - 处理日期 2025-08-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 13D02C82-8D18-736B-923E-2A6A3797B118 Response: {'code': 'ServiceUnavailable', 'requestid': '13D02C82-8D18-736B-923E-2A6A3797B118', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 13D02C82-8D18-736B-923E-2A6A3797B118)
2025-08-20 16:30:52,137 - INFO - 开始处理日期: 2025-08-09
2025-08-20 16:30:52,137 - INFO - Request Parameters - Page 1:
2025-08-20 16:30:52,137 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:30:52,137 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:00,199 - INFO - Response - Page 1:
2025-08-20 16:31:00,199 - INFO - 第 1 页获取到 50 条记录
2025-08-20 16:31:00,699 - INFO - Request Parameters - Page 2:
2025-08-20 16:31:00,699 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:00,699 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:01,527 - INFO - Response - Page 2:
2025-08-20 16:31:01,527 - INFO - 第 2 页获取到 50 条记录
2025-08-20 16:31:02,043 - INFO - Request Parameters - Page 3:
2025-08-20 16:31:02,043 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:02,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:02,824 - INFO - Response - Page 3:
2025-08-20 16:31:02,824 - INFO - 第 3 页获取到 50 条记录
2025-08-20 16:31:03,340 - INFO - Request Parameters - Page 4:
2025-08-20 16:31:03,340 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:03,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:04,090 - INFO - Response - Page 4:
2025-08-20 16:31:04,090 - INFO - 第 4 页获取到 50 条记录
2025-08-20 16:31:04,605 - INFO - Request Parameters - Page 5:
2025-08-20 16:31:04,605 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:04,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:05,355 - INFO - Response - Page 5:
2025-08-20 16:31:05,355 - INFO - 第 5 页获取到 50 条记录
2025-08-20 16:31:05,871 - INFO - Request Parameters - Page 6:
2025-08-20 16:31:05,871 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:05,871 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:06,699 - INFO - Response - Page 6:
2025-08-20 16:31:06,699 - INFO - 第 6 页获取到 50 条记录
2025-08-20 16:31:07,215 - INFO - Request Parameters - Page 7:
2025-08-20 16:31:07,215 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:07,215 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:08,090 - INFO - Response - Page 7:
2025-08-20 16:31:08,090 - INFO - 第 7 页获取到 50 条记录
2025-08-20 16:31:08,590 - INFO - Request Parameters - Page 8:
2025-08-20 16:31:08,590 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:08,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:09,418 - INFO - Response - Page 8:
2025-08-20 16:31:09,418 - INFO - 第 8 页获取到 50 条记录
2025-08-20 16:31:09,918 - INFO - Request Parameters - Page 9:
2025-08-20 16:31:09,918 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:09,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:10,730 - INFO - Response - Page 9:
2025-08-20 16:31:10,730 - INFO - 第 9 页获取到 47 条记录
2025-08-20 16:31:11,246 - INFO - 查询完成，共获取到 447 条记录
2025-08-20 16:31:11,246 - INFO - 获取到 447 条表单数据
2025-08-20 16:31:11,246 - INFO - 当前日期 2025-08-09 有 1 条MySQL数据需要处理
2025-08-20 16:31:11,246 - INFO - 开始更新记录 - 表单实例ID: FINST-74766M71MWUXVT2VD80ZC7HICN292CAN8S5EMDM
2025-08-20 16:31:11,902 - INFO - 更新表单数据成功: FINST-74766M71MWUXVT2VD80ZC7HICN292CAN8S5EMDM
2025-08-20 16:31:11,902 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 4570.2, 'new_value': 2581.37}, {'field': 'total_amount', 'old_value': 4570.2, 'new_value': 2581.37}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 16:31:11,902 - INFO - 日期 2025-08-09 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 16:31:11,902 - INFO - 开始处理日期: 2025-08-13
2025-08-20 16:31:11,902 - INFO - Request Parameters - Page 1:
2025-08-20 16:31:11,902 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:11,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:12,777 - INFO - Response - Page 1:
2025-08-20 16:31:12,777 - INFO - 第 1 页获取到 50 条记录
2025-08-20 16:31:13,293 - INFO - Request Parameters - Page 2:
2025-08-20 16:31:13,293 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:13,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:14,136 - INFO - Response - Page 2:
2025-08-20 16:31:14,136 - INFO - 第 2 页获取到 50 条记录
2025-08-20 16:31:14,652 - INFO - Request Parameters - Page 3:
2025-08-20 16:31:14,652 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:14,652 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:15,465 - INFO - Response - Page 3:
2025-08-20 16:31:15,465 - INFO - 第 3 页获取到 50 条记录
2025-08-20 16:31:15,980 - INFO - Request Parameters - Page 4:
2025-08-20 16:31:15,980 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:15,980 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:16,824 - INFO - Response - Page 4:
2025-08-20 16:31:16,824 - INFO - 第 4 页获取到 50 条记录
2025-08-20 16:31:17,324 - INFO - Request Parameters - Page 5:
2025-08-20 16:31:17,324 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:17,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:18,121 - INFO - Response - Page 5:
2025-08-20 16:31:18,121 - INFO - 第 5 页获取到 50 条记录
2025-08-20 16:31:18,621 - INFO - Request Parameters - Page 6:
2025-08-20 16:31:18,621 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:18,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:19,293 - INFO - Response - Page 6:
2025-08-20 16:31:19,293 - INFO - 第 6 页获取到 50 条记录
2025-08-20 16:31:19,793 - INFO - Request Parameters - Page 7:
2025-08-20 16:31:19,793 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:19,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:20,527 - INFO - Response - Page 7:
2025-08-20 16:31:20,527 - INFO - 第 7 页获取到 50 条记录
2025-08-20 16:31:21,043 - INFO - Request Parameters - Page 8:
2025-08-20 16:31:21,043 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:21,043 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:21,808 - INFO - Response - Page 8:
2025-08-20 16:31:21,808 - INFO - 第 8 页获取到 50 条记录
2025-08-20 16:31:22,324 - INFO - Request Parameters - Page 9:
2025-08-20 16:31:22,324 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:22,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:23,043 - INFO - Response - Page 9:
2025-08-20 16:31:23,043 - INFO - 第 9 页获取到 50 条记录
2025-08-20 16:31:23,558 - INFO - Request Parameters - Page 10:
2025-08-20 16:31:23,558 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:23,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:24,277 - INFO - Response - Page 10:
2025-08-20 16:31:24,277 - INFO - 第 10 页获取到 50 条记录
2025-08-20 16:31:24,793 - INFO - Request Parameters - Page 11:
2025-08-20 16:31:24,793 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:24,793 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:25,636 - INFO - Response - Page 11:
2025-08-20 16:31:25,636 - INFO - 第 11 页获取到 48 条记录
2025-08-20 16:31:26,152 - INFO - 查询完成，共获取到 548 条记录
2025-08-20 16:31:26,152 - INFO - 获取到 548 条表单数据
2025-08-20 16:31:26,152 - INFO - 当前日期 2025-08-13 有 1 条MySQL数据需要处理
2025-08-20 16:31:26,152 - INFO - 开始更新记录 - 表单实例ID: FINST-49866E71KDXXG4S89KPDO5T0DEEM2WRZHSAEMU2
2025-08-20 16:31:26,746 - INFO - 更新表单数据成功: FINST-49866E71KDXXG4S89KPDO5T0DEEM2WRZHSAEMU2
2025-08-20 16:31:26,746 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2100.0, 'new_value': 4694.94}, {'field': 'total_amount', 'old_value': 2100.0, 'new_value': 4694.94}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/9177cb3feed247da98007b09203484b9.png?Expires=2069918739&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=he6HlXN2lBbxUpqCkA4Oi8PSON4%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/c9fa8fee82254c60a00f4fffec1cf372.png?Expires=2070523207&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=jGG9Fda7ex4oxOwE2An3OAUoH7Y%3D'}]
2025-08-20 16:31:26,746 - INFO - 日期 2025-08-13 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 16:31:26,746 - INFO - 开始处理日期: 2025-08-18
2025-08-20 16:31:26,746 - INFO - Request Parameters - Page 1:
2025-08-20 16:31:26,746 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:26,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:27,558 - INFO - Response - Page 1:
2025-08-20 16:31:27,558 - INFO - 第 1 页获取到 50 条记录
2025-08-20 16:31:28,058 - INFO - Request Parameters - Page 2:
2025-08-20 16:31:28,058 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:28,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:28,808 - INFO - Response - Page 2:
2025-08-20 16:31:28,824 - INFO - 第 2 页获取到 50 条记录
2025-08-20 16:31:29,324 - INFO - Request Parameters - Page 3:
2025-08-20 16:31:29,324 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:29,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:30,027 - INFO - Response - Page 3:
2025-08-20 16:31:30,027 - INFO - 第 3 页获取到 50 条记录
2025-08-20 16:31:30,543 - INFO - Request Parameters - Page 4:
2025-08-20 16:31:30,543 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:30,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:31,261 - INFO - Response - Page 4:
2025-08-20 16:31:31,261 - INFO - 第 4 页获取到 50 条记录
2025-08-20 16:31:31,761 - INFO - Request Parameters - Page 5:
2025-08-20 16:31:31,761 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:31,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:32,621 - INFO - Response - Page 5:
2025-08-20 16:31:32,621 - INFO - 第 5 页获取到 50 条记录
2025-08-20 16:31:33,136 - INFO - Request Parameters - Page 6:
2025-08-20 16:31:33,136 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:33,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:34,027 - INFO - Response - Page 6:
2025-08-20 16:31:34,027 - INFO - 第 6 页获取到 50 条记录
2025-08-20 16:31:34,543 - INFO - Request Parameters - Page 7:
2025-08-20 16:31:34,543 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:34,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:35,339 - INFO - Response - Page 7:
2025-08-20 16:31:35,339 - INFO - 第 7 页获取到 50 条记录
2025-08-20 16:31:35,855 - INFO - Request Parameters - Page 8:
2025-08-20 16:31:35,855 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:35,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:36,589 - INFO - Response - Page 8:
2025-08-20 16:31:36,589 - INFO - 第 8 页获取到 50 条记录
2025-08-20 16:31:37,089 - INFO - Request Parameters - Page 9:
2025-08-20 16:31:37,089 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:37,089 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:37,808 - INFO - Response - Page 9:
2025-08-20 16:31:37,808 - INFO - 第 9 页获取到 50 条记录
2025-08-20 16:31:38,324 - INFO - Request Parameters - Page 10:
2025-08-20 16:31:38,324 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:38,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:39,105 - INFO - Response - Page 10:
2025-08-20 16:31:39,105 - INFO - 第 10 页获取到 50 条记录
2025-08-20 16:31:39,621 - INFO - Request Parameters - Page 11:
2025-08-20 16:31:39,621 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:39,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:40,293 - INFO - Response - Page 11:
2025-08-20 16:31:40,293 - INFO - 第 11 页获取到 26 条记录
2025-08-20 16:31:40,808 - INFO - 查询完成，共获取到 526 条记录
2025-08-20 16:31:40,808 - INFO - 获取到 526 条表单数据
2025-08-20 16:31:40,808 - INFO - 当前日期 2025-08-18 有 1 条MySQL数据需要处理
2025-08-20 16:31:40,808 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 16:31:40,808 - INFO - 开始处理日期: 2025-08-19
2025-08-20 16:31:40,808 - INFO - Request Parameters - Page 1:
2025-08-20 16:31:40,808 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:40,808 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:41,652 - INFO - Response - Page 1:
2025-08-20 16:31:41,668 - INFO - 第 1 页获取到 50 条记录
2025-08-20 16:31:42,183 - INFO - Request Parameters - Page 2:
2025-08-20 16:31:42,183 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:42,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:43,043 - INFO - Response - Page 2:
2025-08-20 16:31:43,043 - INFO - 第 2 页获取到 50 条记录
2025-08-20 16:31:43,558 - INFO - Request Parameters - Page 3:
2025-08-20 16:31:43,558 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:43,558 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:44,381 - INFO - Response - Page 3:
2025-08-20 16:31:44,381 - INFO - 第 3 页获取到 50 条记录
2025-08-20 16:31:44,897 - INFO - Request Parameters - Page 4:
2025-08-20 16:31:44,897 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:44,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:45,709 - INFO - Response - Page 4:
2025-08-20 16:31:45,709 - INFO - 第 4 页获取到 50 条记录
2025-08-20 16:31:46,209 - INFO - Request Parameters - Page 5:
2025-08-20 16:31:46,209 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:46,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:46,959 - INFO - Response - Page 5:
2025-08-20 16:31:46,959 - INFO - 第 5 页获取到 50 条记录
2025-08-20 16:31:47,459 - INFO - Request Parameters - Page 6:
2025-08-20 16:31:47,459 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:47,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:48,210 - INFO - Response - Page 6:
2025-08-20 16:31:48,210 - INFO - 第 6 页获取到 50 条记录
2025-08-20 16:31:48,725 - INFO - Request Parameters - Page 7:
2025-08-20 16:31:48,725 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:48,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:49,506 - INFO - Response - Page 7:
2025-08-20 16:31:49,506 - INFO - 第 7 页获取到 50 条记录
2025-08-20 16:31:50,022 - INFO - Request Parameters - Page 8:
2025-08-20 16:31:50,022 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:50,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:50,897 - INFO - Response - Page 8:
2025-08-20 16:31:50,897 - INFO - 第 8 页获取到 50 条记录
2025-08-20 16:31:51,413 - INFO - Request Parameters - Page 9:
2025-08-20 16:31:51,413 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:51,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:52,163 - INFO - Response - Page 9:
2025-08-20 16:31:52,163 - INFO - 第 9 页获取到 50 条记录
2025-08-20 16:31:52,663 - INFO - Request Parameters - Page 10:
2025-08-20 16:31:52,663 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:52,663 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:53,413 - INFO - Response - Page 10:
2025-08-20 16:31:53,413 - INFO - 第 10 页获取到 50 条记录
2025-08-20 16:31:53,928 - INFO - Request Parameters - Page 11:
2025-08-20 16:31:53,928 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:53,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:54,584 - INFO - Response - Page 11:
2025-08-20 16:31:54,584 - INFO - 第 11 页获取到 30 条记录
2025-08-20 16:31:55,084 - INFO - 查询完成，共获取到 530 条记录
2025-08-20 16:31:55,084 - INFO - 获取到 530 条表单数据
2025-08-20 16:31:55,084 - INFO - 当前日期 2025-08-19 有 144 条MySQL数据需要处理
2025-08-20 16:31:55,084 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 16:31:55,084 - INFO - 开始处理日期: 2025-08-20
2025-08-20 16:31:55,084 - INFO - Request Parameters - Page 1:
2025-08-20 16:31:55,084 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:31:55,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:31:55,631 - INFO - Response - Page 1:
2025-08-20 16:31:55,631 - INFO - 第 1 页获取到 3 条记录
2025-08-20 16:31:56,147 - INFO - 查询完成，共获取到 3 条记录
2025-08-20 16:31:56,147 - INFO - 获取到 3 条表单数据
2025-08-20 16:31:56,147 - INFO - 当前日期 2025-08-20 有 3 条MySQL数据需要处理
2025-08-20 16:31:56,147 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 16:31:56,147 - INFO - 数据同步完成！更新: 2 条，插入: 0 条，错误: 2 条
2025-08-20 16:32:56,157 - INFO - 开始同步昨天与今天的销售数据: 2025-08-19 至 2025-08-20
2025-08-20 16:32:56,157 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-20 16:32:56,157 - INFO - 查询参数: ('2025-08-19', '2025-08-20')
2025-08-20 16:32:56,345 - INFO - MySQL查询成功，时间段: 2025-08-19 至 2025-08-20，共获取 554 条记录
2025-08-20 16:32:56,345 - INFO - 获取到 2 个日期需要处理: ['2025-08-19', '2025-08-20']
2025-08-20 16:32:56,345 - INFO - 开始处理日期: 2025-08-19
2025-08-20 16:32:56,345 - INFO - Request Parameters - Page 1:
2025-08-20 16:32:56,345 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:32:56,345 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:32:57,189 - INFO - Response - Page 1:
2025-08-20 16:32:57,189 - INFO - 第 1 页获取到 50 条记录
2025-08-20 16:32:57,704 - INFO - Request Parameters - Page 2:
2025-08-20 16:32:57,704 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:32:57,704 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:32:58,501 - INFO - Response - Page 2:
2025-08-20 16:32:58,501 - INFO - 第 2 页获取到 50 条记录
2025-08-20 16:32:59,017 - INFO - Request Parameters - Page 3:
2025-08-20 16:32:59,017 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:32:59,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:32:59,907 - INFO - Response - Page 3:
2025-08-20 16:32:59,907 - INFO - 第 3 页获取到 50 条记录
2025-08-20 16:33:00,423 - INFO - Request Parameters - Page 4:
2025-08-20 16:33:00,423 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:00,423 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:01,173 - INFO - Response - Page 4:
2025-08-20 16:33:01,173 - INFO - 第 4 页获取到 50 条记录
2025-08-20 16:33:01,673 - INFO - Request Parameters - Page 5:
2025-08-20 16:33:01,673 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:01,673 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:02,454 - INFO - Response - Page 5:
2025-08-20 16:33:02,454 - INFO - 第 5 页获取到 50 条记录
2025-08-20 16:33:02,970 - INFO - Request Parameters - Page 6:
2025-08-20 16:33:02,970 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:02,970 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:03,767 - INFO - Response - Page 6:
2025-08-20 16:33:03,767 - INFO - 第 6 页获取到 50 条记录
2025-08-20 16:33:04,267 - INFO - Request Parameters - Page 7:
2025-08-20 16:33:04,267 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:04,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:04,970 - INFO - Response - Page 7:
2025-08-20 16:33:04,970 - INFO - 第 7 页获取到 50 条记录
2025-08-20 16:33:05,485 - INFO - Request Parameters - Page 8:
2025-08-20 16:33:05,485 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:05,485 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:06,282 - INFO - Response - Page 8:
2025-08-20 16:33:06,282 - INFO - 第 8 页获取到 50 条记录
2025-08-20 16:33:06,798 - INFO - Request Parameters - Page 9:
2025-08-20 16:33:06,798 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:06,798 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:07,704 - INFO - Response - Page 9:
2025-08-20 16:33:07,704 - INFO - 第 9 页获取到 50 条记录
2025-08-20 16:33:08,220 - INFO - Request Parameters - Page 10:
2025-08-20 16:33:08,220 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:08,220 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:09,001 - INFO - Response - Page 10:
2025-08-20 16:33:09,001 - INFO - 第 10 页获取到 50 条记录
2025-08-20 16:33:09,501 - INFO - Request Parameters - Page 11:
2025-08-20 16:33:09,501 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:09,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:10,220 - INFO - Response - Page 11:
2025-08-20 16:33:10,220 - INFO - 第 11 页获取到 30 条记录
2025-08-20 16:33:10,720 - INFO - 查询完成，共获取到 530 条记录
2025-08-20 16:33:10,720 - INFO - 获取到 530 条表单数据
2025-08-20 16:33:10,720 - INFO - 当前日期 2025-08-19 有 530 条MySQL数据需要处理
2025-08-20 16:33:10,735 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 16:33:10,735 - INFO - 开始处理日期: 2025-08-20
2025-08-20 16:33:10,735 - INFO - Request Parameters - Page 1:
2025-08-20 16:33:10,735 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 16:33:10,735 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 16:33:11,267 - INFO - Response - Page 1:
2025-08-20 16:33:11,267 - INFO - 第 1 页获取到 3 条记录
2025-08-20 16:33:11,782 - INFO - 查询完成，共获取到 3 条记录
2025-08-20 16:33:11,782 - INFO - 获取到 3 条表单数据
2025-08-20 16:33:11,782 - INFO - 当前日期 2025-08-20 有 3 条MySQL数据需要处理
2025-08-20 16:33:11,782 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 16:33:11,782 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 16:33:11,782 - INFO - 同步完成
2025-08-20 19:30:33,530 - INFO - 使用默认增量同步（当天更新数据）
2025-08-20 19:30:33,530 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-20 19:30:33,530 - INFO - 查询参数: ('2025-08-20',)
2025-08-20 19:30:33,717 - INFO - MySQL查询成功，增量数据（日期: 2025-08-20），共获取 190 条记录
2025-08-20 19:30:33,717 - INFO - 获取到 14 个日期需要处理: ['2025-08-01', '2025-08-02', '2025-08-03', '2025-08-04', '2025-08-05', '2025-08-06', '2025-08-07', '2025-08-08', '2025-08-09', '2025-08-10', '2025-08-13', '2025-08-18', '2025-08-19', '2025-08-20']
2025-08-20 19:30:33,717 - INFO - 开始处理日期: 2025-08-01
2025-08-20 19:30:33,733 - INFO - Request Parameters - Page 1:
2025-08-20 19:30:33,733 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:33,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:41,842 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 352F8C96-97CF-7216-B270-1F894586F2A0 Response: {'code': 'ServiceUnavailable', 'requestid': '352F8C96-97CF-7216-B270-1F894586F2A0', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 352F8C96-97CF-7216-B270-1F894586F2A0)
2025-08-20 19:30:41,842 - INFO - 开始处理日期: 2025-08-02
2025-08-20 19:30:41,842 - INFO - Request Parameters - Page 1:
2025-08-20 19:30:41,842 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:41,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:49,951 - ERROR - 处理日期 2025-08-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-4C5D-7F90-8FDE-************ Response: {'code': 'ServiceUnavailable', 'requestid': '********-4C5D-7F90-8FDE-************', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-4C5D-7F90-8FDE-************)
2025-08-20 19:30:49,951 - INFO - 开始处理日期: 2025-08-03
2025-08-20 19:30:49,951 - INFO - Request Parameters - Page 1:
2025-08-20 19:30:49,951 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:49,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:50,779 - INFO - Response - Page 1:
2025-08-20 19:30:50,779 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:30:51,289 - INFO - Request Parameters - Page 2:
2025-08-20 19:30:51,289 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:51,289 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:52,086 - INFO - Response - Page 2:
2025-08-20 19:30:52,086 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:30:52,602 - INFO - Request Parameters - Page 3:
2025-08-20 19:30:52,602 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:52,602 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:53,399 - INFO - Response - Page 3:
2025-08-20 19:30:53,399 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:30:53,914 - INFO - Request Parameters - Page 4:
2025-08-20 19:30:53,914 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:53,914 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:54,711 - INFO - Response - Page 4:
2025-08-20 19:30:54,711 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:30:55,211 - INFO - Request Parameters - Page 5:
2025-08-20 19:30:55,211 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:55,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:56,024 - INFO - Response - Page 5:
2025-08-20 19:30:56,024 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:30:56,539 - INFO - Request Parameters - Page 6:
2025-08-20 19:30:56,539 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:56,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:57,367 - INFO - Response - Page 6:
2025-08-20 19:30:57,367 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:30:57,883 - INFO - Request Parameters - Page 7:
2025-08-20 19:30:57,883 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:57,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:58,664 - INFO - Response - Page 7:
2025-08-20 19:30:58,664 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:30:59,180 - INFO - Request Parameters - Page 8:
2025-08-20 19:30:59,180 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:30:59,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:30:59,945 - INFO - Response - Page 8:
2025-08-20 19:30:59,961 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:31:00,476 - INFO - Request Parameters - Page 9:
2025-08-20 19:31:00,476 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:00,476 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:01,211 - INFO - Response - Page 9:
2025-08-20 19:31:01,211 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:31:01,726 - INFO - Request Parameters - Page 10:
2025-08-20 19:31:01,726 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:01,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:02,539 - INFO - Response - Page 10:
2025-08-20 19:31:02,554 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:31:03,054 - INFO - Request Parameters - Page 11:
2025-08-20 19:31:03,054 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:03,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:03,664 - INFO - Response - Page 11:
2025-08-20 19:31:03,664 - INFO - 第 11 页获取到 10 条记录
2025-08-20 19:31:04,179 - INFO - 查询完成，共获取到 510 条记录
2025-08-20 19:31:04,179 - INFO - 获取到 510 条表单数据
2025-08-20 19:31:04,179 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-20 19:31:04,195 - INFO - 开始更新记录 - 表单实例ID: FINST-3PF66X61BMMXDVHCCJY85D7OEXKA2FONXHWDMVI
2025-08-20 19:31:04,820 - INFO - 更新表单数据成功: FINST-3PF66X61BMMXDVHCCJY85D7OEXKA2FONXHWDMVI
2025-08-20 19:31:04,820 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 16950.7}, {'field': 'total_amount', 'old_value': 16950.7, 'new_value': 33901.4}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 19:31:04,820 - INFO - 日期 2025-08-03 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 19:31:04,820 - INFO - 开始处理日期: 2025-08-04
2025-08-20 19:31:04,820 - INFO - Request Parameters - Page 1:
2025-08-20 19:31:04,820 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:04,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:05,570 - INFO - Response - Page 1:
2025-08-20 19:31:05,570 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:31:06,085 - INFO - Request Parameters - Page 2:
2025-08-20 19:31:06,085 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:06,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:06,820 - INFO - Response - Page 2:
2025-08-20 19:31:06,820 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:31:07,320 - INFO - Request Parameters - Page 3:
2025-08-20 19:31:07,320 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:07,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:08,210 - INFO - Response - Page 3:
2025-08-20 19:31:08,210 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:31:08,726 - INFO - Request Parameters - Page 4:
2025-08-20 19:31:08,726 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:08,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:09,476 - INFO - Response - Page 4:
2025-08-20 19:31:09,476 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:31:09,992 - INFO - Request Parameters - Page 5:
2025-08-20 19:31:09,992 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:09,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:10,741 - INFO - Response - Page 5:
2025-08-20 19:31:10,741 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:31:11,241 - INFO - Request Parameters - Page 6:
2025-08-20 19:31:11,241 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:11,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:12,007 - INFO - Response - Page 6:
2025-08-20 19:31:12,007 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:31:12,523 - INFO - Request Parameters - Page 7:
2025-08-20 19:31:12,523 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:12,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:13,257 - INFO - Response - Page 7:
2025-08-20 19:31:13,257 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:31:13,773 - INFO - Request Parameters - Page 8:
2025-08-20 19:31:13,773 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:13,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:14,569 - INFO - Response - Page 8:
2025-08-20 19:31:14,569 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:31:15,085 - INFO - Request Parameters - Page 9:
2025-08-20 19:31:15,085 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:15,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:15,866 - INFO - Response - Page 9:
2025-08-20 19:31:15,866 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:31:16,382 - INFO - Request Parameters - Page 10:
2025-08-20 19:31:16,382 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:16,382 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:17,132 - INFO - Response - Page 10:
2025-08-20 19:31:17,132 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:31:17,647 - INFO - Request Parameters - Page 11:
2025-08-20 19:31:17,647 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:17,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:18,350 - INFO - Response - Page 11:
2025-08-20 19:31:18,350 - INFO - 第 11 页获取到 21 条记录
2025-08-20 19:31:18,866 - INFO - 查询完成，共获取到 521 条记录
2025-08-20 19:31:18,866 - INFO - 获取到 521 条表单数据
2025-08-20 19:31:18,866 - INFO - 当前日期 2025-08-04 有 1 条MySQL数据需要处理
2025-08-20 19:31:18,866 - INFO - 开始更新记录 - 表单实例ID: FINST-IOC66G713DRX6EW4BVRR98ZZDP2035DRMXXDMI
2025-08-20 19:31:19,413 - INFO - 更新表单数据成功: FINST-IOC66G713DRX6EW4BVRR98ZZDP2035DRMXXDMI
2025-08-20 19:31:19,413 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 8555.2}, {'field': 'total_amount', 'old_value': 8555.2, 'new_value': 17110.4}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 19:31:19,413 - INFO - 日期 2025-08-04 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 19:31:19,413 - INFO - 开始处理日期: 2025-08-05
2025-08-20 19:31:19,413 - INFO - Request Parameters - Page 1:
2025-08-20 19:31:19,413 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:19,413 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:20,132 - INFO - Response - Page 1:
2025-08-20 19:31:20,132 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:31:20,647 - INFO - Request Parameters - Page 2:
2025-08-20 19:31:20,647 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:20,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:21,381 - INFO - Response - Page 2:
2025-08-20 19:31:21,381 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:31:21,897 - INFO - Request Parameters - Page 3:
2025-08-20 19:31:21,897 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:21,897 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:22,647 - INFO - Response - Page 3:
2025-08-20 19:31:22,647 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:31:23,163 - INFO - Request Parameters - Page 4:
2025-08-20 19:31:23,163 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:23,163 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:23,897 - INFO - Response - Page 4:
2025-08-20 19:31:23,897 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:31:24,397 - INFO - Request Parameters - Page 5:
2025-08-20 19:31:24,397 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:24,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:25,147 - INFO - Response - Page 5:
2025-08-20 19:31:25,147 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:31:25,662 - INFO - Request Parameters - Page 6:
2025-08-20 19:31:25,662 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:25,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:26,491 - INFO - Response - Page 6:
2025-08-20 19:31:26,491 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:31:27,006 - INFO - Request Parameters - Page 7:
2025-08-20 19:31:27,006 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:27,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:27,756 - INFO - Response - Page 7:
2025-08-20 19:31:27,756 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:31:28,272 - INFO - Request Parameters - Page 8:
2025-08-20 19:31:28,272 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:28,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:28,990 - INFO - Response - Page 8:
2025-08-20 19:31:28,990 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:31:29,506 - INFO - Request Parameters - Page 9:
2025-08-20 19:31:29,506 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:29,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:30,225 - INFO - Response - Page 9:
2025-08-20 19:31:30,225 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:31:30,740 - INFO - Request Parameters - Page 10:
2025-08-20 19:31:30,740 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:30,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:31,537 - INFO - Response - Page 10:
2025-08-20 19:31:31,537 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:31:32,037 - INFO - Request Parameters - Page 11:
2025-08-20 19:31:32,037 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:32,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:32,756 - INFO - Response - Page 11:
2025-08-20 19:31:32,756 - INFO - 第 11 页获取到 36 条记录
2025-08-20 19:31:33,256 - INFO - 查询完成，共获取到 536 条记录
2025-08-20 19:31:33,256 - INFO - 获取到 536 条表单数据
2025-08-20 19:31:33,256 - INFO - 当前日期 2025-08-05 有 1 条MySQL数据需要处理
2025-08-20 19:31:33,256 - INFO - 开始更新记录 - 表单实例ID: FINST-2LC66IA10GSXTUJKATEDG7MH2EKY2V372DZDMI
2025-08-20 19:31:33,834 - INFO - 更新表单数据成功: FINST-2LC66IA10GSXTUJKATEDG7MH2EKY2V372DZDMI
2025-08-20 19:31:33,834 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 10594.3}, {'field': 'total_amount', 'old_value': 10594.3, 'new_value': 21188.6}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 19:31:33,834 - INFO - 日期 2025-08-05 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 19:31:33,834 - INFO - 开始处理日期: 2025-08-06
2025-08-20 19:31:33,834 - INFO - Request Parameters - Page 1:
2025-08-20 19:31:33,834 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:33,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:34,678 - INFO - Response - Page 1:
2025-08-20 19:31:34,678 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:31:35,178 - INFO - Request Parameters - Page 2:
2025-08-20 19:31:35,178 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:35,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:35,974 - INFO - Response - Page 2:
2025-08-20 19:31:35,974 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:31:36,490 - INFO - Request Parameters - Page 3:
2025-08-20 19:31:36,490 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:36,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:37,256 - INFO - Response - Page 3:
2025-08-20 19:31:37,256 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:31:37,771 - INFO - Request Parameters - Page 4:
2025-08-20 19:31:37,771 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:37,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:38,537 - INFO - Response - Page 4:
2025-08-20 19:31:38,537 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:31:39,037 - INFO - Request Parameters - Page 5:
2025-08-20 19:31:39,037 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:39,037 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:39,833 - INFO - Response - Page 5:
2025-08-20 19:31:39,833 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:31:40,333 - INFO - Request Parameters - Page 6:
2025-08-20 19:31:40,333 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:40,333 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:41,052 - INFO - Response - Page 6:
2025-08-20 19:31:41,052 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:31:41,568 - INFO - Request Parameters - Page 7:
2025-08-20 19:31:41,568 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:41,568 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:42,318 - INFO - Response - Page 7:
2025-08-20 19:31:42,318 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:31:42,833 - INFO - Request Parameters - Page 8:
2025-08-20 19:31:42,833 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:42,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:43,630 - INFO - Response - Page 8:
2025-08-20 19:31:43,630 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:31:44,130 - INFO - Request Parameters - Page 9:
2025-08-20 19:31:44,130 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:44,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:44,927 - INFO - Response - Page 9:
2025-08-20 19:31:44,927 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:31:45,427 - INFO - Request Parameters - Page 10:
2025-08-20 19:31:45,427 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:45,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:46,224 - INFO - Response - Page 10:
2025-08-20 19:31:46,224 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:31:46,739 - INFO - Request Parameters - Page 11:
2025-08-20 19:31:46,739 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:46,739 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:47,442 - INFO - Response - Page 11:
2025-08-20 19:31:47,442 - INFO - 第 11 页获取到 26 条记录
2025-08-20 19:31:47,942 - INFO - 查询完成，共获取到 526 条记录
2025-08-20 19:31:47,942 - INFO - 获取到 526 条表单数据
2025-08-20 19:31:47,942 - INFO - 当前日期 2025-08-06 有 1 条MySQL数据需要处理
2025-08-20 19:31:47,942 - INFO - 开始更新记录 - 表单实例ID: FINST-DKB66TA1SETXDIPVA1F6LCF863DQ2XDDJS0EMB
2025-08-20 19:31:48,552 - INFO - 更新表单数据成功: FINST-DKB66TA1SETXDIPVA1F6LCF863DQ2XDDJS0EMB
2025-08-20 19:31:48,552 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 11217.6}, {'field': 'total_amount', 'old_value': 11217.6, 'new_value': 22435.2}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 19:31:48,552 - INFO - 日期 2025-08-06 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 19:31:48,552 - INFO - 开始处理日期: 2025-08-07
2025-08-20 19:31:48,552 - INFO - Request Parameters - Page 1:
2025-08-20 19:31:48,552 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:48,552 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:49,333 - INFO - Response - Page 1:
2025-08-20 19:31:49,333 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:31:49,833 - INFO - Request Parameters - Page 2:
2025-08-20 19:31:49,833 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:49,833 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:50,583 - INFO - Response - Page 2:
2025-08-20 19:31:50,583 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:31:51,094 - INFO - Request Parameters - Page 3:
2025-08-20 19:31:51,094 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:51,094 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:51,859 - INFO - Response - Page 3:
2025-08-20 19:31:51,859 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:31:52,359 - INFO - Request Parameters - Page 4:
2025-08-20 19:31:52,359 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:52,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:53,093 - INFO - Response - Page 4:
2025-08-20 19:31:53,093 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:31:53,609 - INFO - Request Parameters - Page 5:
2025-08-20 19:31:53,609 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:53,609 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:54,375 - INFO - Response - Page 5:
2025-08-20 19:31:54,375 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:31:54,890 - INFO - Request Parameters - Page 6:
2025-08-20 19:31:54,890 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:54,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:55,687 - INFO - Response - Page 6:
2025-08-20 19:31:55,687 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:31:56,203 - INFO - Request Parameters - Page 7:
2025-08-20 19:31:56,203 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:56,203 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:56,921 - INFO - Response - Page 7:
2025-08-20 19:31:56,921 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:31:57,421 - INFO - Request Parameters - Page 8:
2025-08-20 19:31:57,421 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:57,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:58,171 - INFO - Response - Page 8:
2025-08-20 19:31:58,171 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:31:58,671 - INFO - Request Parameters - Page 9:
2025-08-20 19:31:58,671 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:58,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:31:59,421 - INFO - Response - Page 9:
2025-08-20 19:31:59,421 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:31:59,921 - INFO - Request Parameters - Page 10:
2025-08-20 19:31:59,921 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:31:59,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:00,687 - INFO - Response - Page 10:
2025-08-20 19:32:00,687 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:32:01,202 - INFO - Request Parameters - Page 11:
2025-08-20 19:32:01,202 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:01,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:01,952 - INFO - Response - Page 11:
2025-08-20 19:32:01,952 - INFO - 第 11 页获取到 46 条记录
2025-08-20 19:32:02,468 - INFO - 查询完成，共获取到 546 条记录
2025-08-20 19:32:02,468 - INFO - 获取到 546 条表单数据
2025-08-20 19:32:02,468 - INFO - 当前日期 2025-08-07 有 1 条MySQL数据需要处理
2025-08-20 19:32:02,468 - INFO - 开始更新记录 - 表单实例ID: FINST-YFF667C1DZTXSNKI8JXLMBCM4VDQ340I982EMC5
2025-08-20 19:32:03,046 - INFO - 更新表单数据成功: FINST-YFF667C1DZTXSNKI8JXLMBCM4VDQ340I982EMC5
2025-08-20 19:32:03,046 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 7860.9}, {'field': 'total_amount', 'old_value': 7860.9, 'new_value': 15721.8}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 19:32:03,046 - INFO - 日期 2025-08-07 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 19:32:03,046 - INFO - 开始处理日期: 2025-08-08
2025-08-20 19:32:03,046 - INFO - Request Parameters - Page 1:
2025-08-20 19:32:03,046 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:03,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:03,843 - INFO - Response - Page 1:
2025-08-20 19:32:03,843 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:32:04,358 - INFO - Request Parameters - Page 2:
2025-08-20 19:32:04,358 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:04,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:05,155 - INFO - Response - Page 2:
2025-08-20 19:32:05,155 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:32:05,671 - INFO - Request Parameters - Page 3:
2025-08-20 19:32:05,671 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:05,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:06,374 - INFO - Response - Page 3:
2025-08-20 19:32:06,374 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:32:06,890 - INFO - Request Parameters - Page 4:
2025-08-20 19:32:06,890 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:06,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:07,671 - INFO - Response - Page 4:
2025-08-20 19:32:07,671 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:32:08,186 - INFO - Request Parameters - Page 5:
2025-08-20 19:32:08,186 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:08,186 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:08,999 - INFO - Response - Page 5:
2025-08-20 19:32:08,999 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:32:09,514 - INFO - Request Parameters - Page 6:
2025-08-20 19:32:09,514 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:09,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:10,233 - INFO - Response - Page 6:
2025-08-20 19:32:10,233 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:32:10,749 - INFO - Request Parameters - Page 7:
2025-08-20 19:32:10,749 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:10,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:11,561 - INFO - Response - Page 7:
2025-08-20 19:32:11,561 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:32:12,061 - INFO - Request Parameters - Page 8:
2025-08-20 19:32:12,061 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:12,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:12,858 - INFO - Response - Page 8:
2025-08-20 19:32:12,858 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:32:13,358 - INFO - Request Parameters - Page 9:
2025-08-20 19:32:13,358 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:13,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:14,077 - INFO - Response - Page 9:
2025-08-20 19:32:14,077 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:32:14,592 - INFO - Request Parameters - Page 10:
2025-08-20 19:32:14,592 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:14,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:15,373 - INFO - Response - Page 10:
2025-08-20 19:32:15,373 - INFO - 第 10 页获取到 47 条记录
2025-08-20 19:32:15,889 - INFO - 查询完成，共获取到 497 条记录
2025-08-20 19:32:15,889 - INFO - 获取到 497 条表单数据
2025-08-20 19:32:15,889 - INFO - 当前日期 2025-08-08 有 1 条MySQL数据需要处理
2025-08-20 19:32:15,889 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1XFVXL3HJCF0JA56A4RB53Q37TT3EMD
2025-08-20 19:32:16,405 - INFO - 更新表单数据成功: FINST-V7966QC1XFVXL3HJCF0JA56A4RB53Q37TT3EMD
2025-08-20 19:32:16,405 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 11564.3}, {'field': 'total_amount', 'old_value': 11564.3, 'new_value': 23128.6}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 19:32:16,405 - INFO - 日期 2025-08-08 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 19:32:16,405 - INFO - 开始处理日期: 2025-08-09
2025-08-20 19:32:16,405 - INFO - Request Parameters - Page 1:
2025-08-20 19:32:16,405 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:16,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:17,201 - INFO - Response - Page 1:
2025-08-20 19:32:17,217 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:32:17,733 - INFO - Request Parameters - Page 2:
2025-08-20 19:32:17,733 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:17,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:18,514 - INFO - Response - Page 2:
2025-08-20 19:32:18,514 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:32:19,029 - INFO - Request Parameters - Page 3:
2025-08-20 19:32:19,029 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:19,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:19,811 - INFO - Response - Page 3:
2025-08-20 19:32:19,811 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:32:20,326 - INFO - Request Parameters - Page 4:
2025-08-20 19:32:20,326 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:20,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:21,092 - INFO - Response - Page 4:
2025-08-20 19:32:21,092 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:32:21,607 - INFO - Request Parameters - Page 5:
2025-08-20 19:32:21,607 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:21,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:22,482 - INFO - Response - Page 5:
2025-08-20 19:32:22,482 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:32:23,014 - INFO - Request Parameters - Page 6:
2025-08-20 19:32:23,014 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:23,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:23,826 - INFO - Response - Page 6:
2025-08-20 19:32:23,826 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:32:24,326 - INFO - Request Parameters - Page 7:
2025-08-20 19:32:24,326 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:24,326 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:25,076 - INFO - Response - Page 7:
2025-08-20 19:32:25,076 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:32:25,592 - INFO - Request Parameters - Page 8:
2025-08-20 19:32:25,592 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:25,592 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:26,295 - INFO - Response - Page 8:
2025-08-20 19:32:26,295 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:32:26,810 - INFO - Request Parameters - Page 9:
2025-08-20 19:32:26,810 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:26,810 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:27,498 - INFO - Response - Page 9:
2025-08-20 19:32:27,498 - INFO - 第 9 页获取到 47 条记录
2025-08-20 19:32:28,013 - INFO - 查询完成，共获取到 447 条记录
2025-08-20 19:32:28,013 - INFO - 获取到 447 条表单数据
2025-08-20 19:32:28,013 - INFO - 当前日期 2025-08-09 有 2 条MySQL数据需要处理
2025-08-20 19:32:28,013 - INFO - 开始更新记录 - 表单实例ID: FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM6A
2025-08-20 19:32:28,545 - INFO - 更新表单数据成功: FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM6A
2025-08-20 19:32:28,545 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 30027.0}, {'field': 'total_amount', 'old_value': 30027.0, 'new_value': 60054.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 19:32:28,545 - INFO - 日期 2025-08-09 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 19:32:28,545 - INFO - 开始处理日期: 2025-08-10
2025-08-20 19:32:28,545 - INFO - Request Parameters - Page 1:
2025-08-20 19:32:28,545 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:28,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:29,263 - INFO - Response - Page 1:
2025-08-20 19:32:29,263 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:32:29,779 - INFO - Request Parameters - Page 2:
2025-08-20 19:32:29,779 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:29,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:30,560 - INFO - Response - Page 2:
2025-08-20 19:32:30,560 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:32:31,060 - INFO - Request Parameters - Page 3:
2025-08-20 19:32:31,060 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:31,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:31,810 - INFO - Response - Page 3:
2025-08-20 19:32:31,810 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:32:32,310 - INFO - Request Parameters - Page 4:
2025-08-20 19:32:32,310 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:32,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:33,107 - INFO - Response - Page 4:
2025-08-20 19:32:33,107 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:32:33,622 - INFO - Request Parameters - Page 5:
2025-08-20 19:32:33,622 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:33,622 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:34,357 - INFO - Response - Page 5:
2025-08-20 19:32:34,357 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:32:34,872 - INFO - Request Parameters - Page 6:
2025-08-20 19:32:34,872 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:34,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:35,622 - INFO - Response - Page 6:
2025-08-20 19:32:35,622 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:32:36,138 - INFO - Request Parameters - Page 7:
2025-08-20 19:32:36,138 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:36,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:36,935 - INFO - Response - Page 7:
2025-08-20 19:32:36,935 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:32:37,435 - INFO - Request Parameters - Page 8:
2025-08-20 19:32:37,435 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:37,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:38,231 - INFO - Response - Page 8:
2025-08-20 19:32:38,231 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:32:38,747 - INFO - Request Parameters - Page 9:
2025-08-20 19:32:38,747 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:38,747 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:39,466 - INFO - Response - Page 9:
2025-08-20 19:32:39,466 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:32:39,981 - INFO - Request Parameters - Page 10:
2025-08-20 19:32:39,981 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:39,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:40,716 - INFO - Response - Page 10:
2025-08-20 19:32:40,716 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:32:41,231 - INFO - Request Parameters - Page 11:
2025-08-20 19:32:41,231 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:41,231 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:41,950 - INFO - Response - Page 11:
2025-08-20 19:32:41,950 - INFO - 第 11 页获取到 27 条记录
2025-08-20 19:32:42,466 - INFO - 查询完成，共获取到 527 条记录
2025-08-20 19:32:42,466 - INFO - 获取到 527 条表单数据
2025-08-20 19:32:42,466 - INFO - 当前日期 2025-08-10 有 1 条MySQL数据需要处理
2025-08-20 19:32:42,466 - INFO - 开始更新记录 - 表单实例ID: FINST-RUF66N81CTUX29UVDY2W0CG4IX9N3VHIEI6EM0T
2025-08-20 19:32:42,981 - INFO - 更新表单数据成功: FINST-RUF66N81CTUX29UVDY2W0CG4IX9N3VHIEI6EM0T
2025-08-20 19:32:42,981 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 22242.2}, {'field': 'total_amount', 'old_value': 22242.2, 'new_value': 44484.4}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-20 19:32:42,981 - INFO - 日期 2025-08-10 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-20 19:32:42,981 - INFO - 开始处理日期: 2025-08-13
2025-08-20 19:32:42,981 - INFO - Request Parameters - Page 1:
2025-08-20 19:32:42,981 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:42,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:43,731 - INFO - Response - Page 1:
2025-08-20 19:32:43,731 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:32:44,247 - INFO - Request Parameters - Page 2:
2025-08-20 19:32:44,247 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:44,247 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:44,997 - INFO - Response - Page 2:
2025-08-20 19:32:44,997 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:32:45,512 - INFO - Request Parameters - Page 3:
2025-08-20 19:32:45,512 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:45,512 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:46,356 - INFO - Response - Page 3:
2025-08-20 19:32:46,356 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:32:46,872 - INFO - Request Parameters - Page 4:
2025-08-20 19:32:46,872 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:46,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:47,622 - INFO - Response - Page 4:
2025-08-20 19:32:47,622 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:32:48,121 - INFO - Request Parameters - Page 5:
2025-08-20 19:32:48,121 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:48,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:48,840 - INFO - Response - Page 5:
2025-08-20 19:32:48,840 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:32:49,340 - INFO - Request Parameters - Page 6:
2025-08-20 19:32:49,340 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:49,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:50,137 - INFO - Response - Page 6:
2025-08-20 19:32:50,137 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:32:50,653 - INFO - Request Parameters - Page 7:
2025-08-20 19:32:50,653 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:50,653 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:51,413 - INFO - Response - Page 7:
2025-08-20 19:32:51,413 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:32:51,913 - INFO - Request Parameters - Page 8:
2025-08-20 19:32:51,913 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:51,913 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:52,804 - INFO - Response - Page 8:
2025-08-20 19:32:52,804 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:32:53,304 - INFO - Request Parameters - Page 9:
2025-08-20 19:32:53,304 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:53,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:54,069 - INFO - Response - Page 9:
2025-08-20 19:32:54,069 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:32:54,585 - INFO - Request Parameters - Page 10:
2025-08-20 19:32:54,585 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:54,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:55,351 - INFO - Response - Page 10:
2025-08-20 19:32:55,351 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:32:55,866 - INFO - Request Parameters - Page 11:
2025-08-20 19:32:55,866 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:55,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:56,647 - INFO - Response - Page 11:
2025-08-20 19:32:56,647 - INFO - 第 11 页获取到 48 条记录
2025-08-20 19:32:57,147 - INFO - 查询完成，共获取到 548 条记录
2025-08-20 19:32:57,147 - INFO - 获取到 548 条表单数据
2025-08-20 19:32:57,147 - INFO - 当前日期 2025-08-13 有 1 条MySQL数据需要处理
2025-08-20 19:32:57,147 - INFO - 日期 2025-08-13 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 19:32:57,147 - INFO - 开始处理日期: 2025-08-18
2025-08-20 19:32:57,147 - INFO - Request Parameters - Page 1:
2025-08-20 19:32:57,147 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:57,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:58,007 - INFO - Response - Page 1:
2025-08-20 19:32:58,007 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:32:58,522 - INFO - Request Parameters - Page 2:
2025-08-20 19:32:58,522 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:58,522 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:32:59,272 - INFO - Response - Page 2:
2025-08-20 19:32:59,272 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:32:59,772 - INFO - Request Parameters - Page 3:
2025-08-20 19:32:59,772 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:32:59,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:00,538 - INFO - Response - Page 3:
2025-08-20 19:33:00,538 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:33:01,053 - INFO - Request Parameters - Page 4:
2025-08-20 19:33:01,053 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:01,053 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:01,725 - INFO - Response - Page 4:
2025-08-20 19:33:01,725 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:33:02,241 - INFO - Request Parameters - Page 5:
2025-08-20 19:33:02,241 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:02,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:02,959 - INFO - Response - Page 5:
2025-08-20 19:33:02,959 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:33:03,475 - INFO - Request Parameters - Page 6:
2025-08-20 19:33:03,475 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:03,475 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:04,272 - INFO - Response - Page 6:
2025-08-20 19:33:04,272 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:33:04,788 - INFO - Request Parameters - Page 7:
2025-08-20 19:33:04,788 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:04,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:05,522 - INFO - Response - Page 7:
2025-08-20 19:33:05,522 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:33:06,022 - INFO - Request Parameters - Page 8:
2025-08-20 19:33:06,022 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:06,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:06,819 - INFO - Response - Page 8:
2025-08-20 19:33:06,819 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:33:07,319 - INFO - Request Parameters - Page 9:
2025-08-20 19:33:07,319 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:07,319 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:08,069 - INFO - Response - Page 9:
2025-08-20 19:33:08,069 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:33:08,569 - INFO - Request Parameters - Page 10:
2025-08-20 19:33:08,569 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:08,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:09,334 - INFO - Response - Page 10:
2025-08-20 19:33:09,334 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:33:09,834 - INFO - Request Parameters - Page 11:
2025-08-20 19:33:09,834 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:09,834 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:10,522 - INFO - Response - Page 11:
2025-08-20 19:33:10,522 - INFO - 第 11 页获取到 26 条记录
2025-08-20 19:33:11,022 - INFO - 查询完成，共获取到 526 条记录
2025-08-20 19:33:11,022 - INFO - 获取到 526 条表单数据
2025-08-20 19:33:11,022 - INFO - 当前日期 2025-08-18 有 1 条MySQL数据需要处理
2025-08-20 19:33:11,022 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 19:33:11,022 - INFO - 开始处理日期: 2025-08-19
2025-08-20 19:33:11,022 - INFO - Request Parameters - Page 1:
2025-08-20 19:33:11,022 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:11,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:11,834 - INFO - Response - Page 1:
2025-08-20 19:33:11,834 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:33:12,350 - INFO - Request Parameters - Page 2:
2025-08-20 19:33:12,350 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:12,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:13,100 - INFO - Response - Page 2:
2025-08-20 19:33:13,100 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:33:13,600 - INFO - Request Parameters - Page 3:
2025-08-20 19:33:13,600 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:13,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:14,474 - INFO - Response - Page 3:
2025-08-20 19:33:14,474 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:33:14,974 - INFO - Request Parameters - Page 4:
2025-08-20 19:33:14,974 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:14,974 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:15,678 - INFO - Response - Page 4:
2025-08-20 19:33:15,678 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:33:16,193 - INFO - Request Parameters - Page 5:
2025-08-20 19:33:16,193 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:16,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:16,959 - INFO - Response - Page 5:
2025-08-20 19:33:16,959 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:33:17,474 - INFO - Request Parameters - Page 6:
2025-08-20 19:33:17,474 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:17,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:18,193 - INFO - Response - Page 6:
2025-08-20 19:33:18,193 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:33:18,693 - INFO - Request Parameters - Page 7:
2025-08-20 19:33:18,693 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:18,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:19,474 - INFO - Response - Page 7:
2025-08-20 19:33:19,474 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:33:19,990 - INFO - Request Parameters - Page 8:
2025-08-20 19:33:19,990 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:19,990 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:20,771 - INFO - Response - Page 8:
2025-08-20 19:33:20,771 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:33:21,287 - INFO - Request Parameters - Page 9:
2025-08-20 19:33:21,287 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:21,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:22,021 - INFO - Response - Page 9:
2025-08-20 19:33:22,021 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:33:22,521 - INFO - Request Parameters - Page 10:
2025-08-20 19:33:22,521 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:22,521 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:23,302 - INFO - Response - Page 10:
2025-08-20 19:33:23,302 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:33:23,818 - INFO - Request Parameters - Page 11:
2025-08-20 19:33:23,818 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:23,818 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:24,489 - INFO - Response - Page 11:
2025-08-20 19:33:24,489 - INFO - 第 11 页获取到 30 条记录
2025-08-20 19:33:24,989 - INFO - 查询完成，共获取到 530 条记录
2025-08-20 19:33:24,989 - INFO - 获取到 530 条表单数据
2025-08-20 19:33:24,989 - INFO - 当前日期 2025-08-19 有 165 条MySQL数据需要处理
2025-08-20 19:33:24,989 - INFO - 开始批量插入 21 条新记录
2025-08-20 19:33:25,193 - INFO - 批量插入响应状态码: 200
2025-08-20 19:33:25,193 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 11:33:25 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1020', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2D121D9D-BCC8-7F74-9E10-B06F8921B117', 'x-acs-trace-id': 'e4f071bc2402ea5ae2c8e089f174a254', 'etag': '1u0MWwptio4rACdn2F4zhFQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 19:33:25,193 - INFO - 批量插入响应体: {'result': ['FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEM7P', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEM8P', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEM9P', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMAP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMBP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMCP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMDP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMEP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMFP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMGP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMHP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMIP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMJP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMKP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMLP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMMP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMNP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMOP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMPP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMQP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMRP']}
2025-08-20 19:33:25,193 - INFO - 批量插入表单数据成功，批次 1，共 21 条记录
2025-08-20 19:33:25,193 - INFO - 成功插入的数据ID: ['FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEM7P', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEM8P', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEM9P', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMAP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMBP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMCP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMDP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMEP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMFP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMGP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMHP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMIP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMJP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMKP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMLP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMMP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMNP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMOP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMPP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMQP', 'FINST-2PF662C12O4YSBDZ72KWYAZFI36T2KGD9WJEMRP']
2025-08-20 19:33:30,208 - INFO - 批量插入完成，共 21 条记录
2025-08-20 19:33:30,208 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 21 条，错误: 0 条
2025-08-20 19:33:30,208 - INFO - 开始处理日期: 2025-08-20
2025-08-20 19:33:30,208 - INFO - Request Parameters - Page 1:
2025-08-20 19:33:30,208 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:33:30,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:33:30,770 - INFO - Response - Page 1:
2025-08-20 19:33:30,770 - INFO - 第 1 页获取到 3 条记录
2025-08-20 19:33:31,270 - INFO - 查询完成，共获取到 3 条记录
2025-08-20 19:33:31,270 - INFO - 获取到 3 条表单数据
2025-08-20 19:33:31,270 - INFO - 当前日期 2025-08-20 有 4 条MySQL数据需要处理
2025-08-20 19:33:31,270 - INFO - 开始批量插入 1 条新记录
2025-08-20 19:33:31,427 - INFO - 批量插入响应状态码: 200
2025-08-20 19:33:31,427 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 11:33:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '320E4395-8BB6-7E7B-97C3-87B0BA86D837', 'x-acs-trace-id': '59cad67fe73489db5287660f9ac4dee3', 'etag': '6Lfr0+wd0ryWzW6gQn6HFTg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 19:33:31,427 - INFO - 批量插入响应体: {'result': ['FINST-K8C66U61KT6YGR767OFLTBRZJWNX2L9I9WJEMV1']}
2025-08-20 19:33:31,427 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-20 19:33:31,427 - INFO - 成功插入的数据ID: ['FINST-K8C66U61KT6YGR767OFLTBRZJWNX2L9I9WJEMV1']
2025-08-20 19:33:36,442 - INFO - 批量插入完成，共 1 条记录
2025-08-20 19:33:36,442 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-20 19:33:36,442 - INFO - 数据同步完成！更新: 8 条，插入: 22 条，错误: 2 条
2025-08-20 19:34:36,449 - INFO - 开始同步昨天与今天的销售数据: 2025-08-19 至 2025-08-20
2025-08-20 19:34:36,449 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-20 19:34:36,449 - INFO - 查询参数: ('2025-08-19', '2025-08-20')
2025-08-20 19:34:36,637 - INFO - MySQL查询成功，时间段: 2025-08-19 至 2025-08-20，共获取 577 条记录
2025-08-20 19:34:36,637 - INFO - 获取到 2 个日期需要处理: ['2025-08-19', '2025-08-20']
2025-08-20 19:34:36,637 - INFO - 开始处理日期: 2025-08-19
2025-08-20 19:34:36,637 - INFO - Request Parameters - Page 1:
2025-08-20 19:34:36,637 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:36,637 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:37,449 - INFO - Response - Page 1:
2025-08-20 19:34:37,449 - INFO - 第 1 页获取到 50 条记录
2025-08-20 19:34:37,949 - INFO - Request Parameters - Page 2:
2025-08-20 19:34:37,949 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:37,949 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:38,715 - INFO - Response - Page 2:
2025-08-20 19:34:38,715 - INFO - 第 2 页获取到 50 条记录
2025-08-20 19:34:39,230 - INFO - Request Parameters - Page 3:
2025-08-20 19:34:39,230 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:39,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:40,011 - INFO - Response - Page 3:
2025-08-20 19:34:40,011 - INFO - 第 3 页获取到 50 条记录
2025-08-20 19:34:40,527 - INFO - Request Parameters - Page 4:
2025-08-20 19:34:40,527 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:40,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:41,230 - INFO - Response - Page 4:
2025-08-20 19:34:41,230 - INFO - 第 4 页获取到 50 条记录
2025-08-20 19:34:41,746 - INFO - Request Parameters - Page 5:
2025-08-20 19:34:41,746 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:41,746 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:42,496 - INFO - Response - Page 5:
2025-08-20 19:34:42,496 - INFO - 第 5 页获取到 50 条记录
2025-08-20 19:34:42,996 - INFO - Request Parameters - Page 6:
2025-08-20 19:34:42,996 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:42,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:43,808 - INFO - Response - Page 6:
2025-08-20 19:34:43,808 - INFO - 第 6 页获取到 50 条记录
2025-08-20 19:34:44,324 - INFO - Request Parameters - Page 7:
2025-08-20 19:34:44,324 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:44,324 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:45,183 - INFO - Response - Page 7:
2025-08-20 19:34:45,183 - INFO - 第 7 页获取到 50 条记录
2025-08-20 19:34:45,683 - INFO - Request Parameters - Page 8:
2025-08-20 19:34:45,683 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:45,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:46,480 - INFO - Response - Page 8:
2025-08-20 19:34:46,480 - INFO - 第 8 页获取到 50 条记录
2025-08-20 19:34:46,995 - INFO - Request Parameters - Page 9:
2025-08-20 19:34:46,995 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:46,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:47,745 - INFO - Response - Page 9:
2025-08-20 19:34:47,745 - INFO - 第 9 页获取到 50 条记录
2025-08-20 19:34:48,261 - INFO - Request Parameters - Page 10:
2025-08-20 19:34:48,261 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:48,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:49,089 - INFO - Response - Page 10:
2025-08-20 19:34:49,089 - INFO - 第 10 页获取到 50 条记录
2025-08-20 19:34:49,589 - INFO - Request Parameters - Page 11:
2025-08-20 19:34:49,589 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:49,589 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:50,339 - INFO - Response - Page 11:
2025-08-20 19:34:50,339 - INFO - 第 11 页获取到 50 条记录
2025-08-20 19:34:50,839 - INFO - Request Parameters - Page 12:
2025-08-20 19:34:50,839 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:50,839 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:51,365 - INFO - Response - Page 12:
2025-08-20 19:34:51,365 - INFO - 第 12 页获取到 1 条记录
2025-08-20 19:34:51,865 - INFO - 查询完成，共获取到 551 条记录
2025-08-20 19:34:51,865 - INFO - 获取到 551 条表单数据
2025-08-20 19:34:51,865 - INFO - 当前日期 2025-08-19 有 552 条MySQL数据需要处理
2025-08-20 19:34:51,881 - INFO - 开始批量插入 1 条新记录
2025-08-20 19:34:52,053 - INFO - 批量插入响应状态码: 200
2025-08-20 19:34:52,053 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 11:34:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '646C2AB8-0223-7E30-AD89-E7D07A3B25E1', 'x-acs-trace-id': 'b2262a98554df35d321cdec20858fb1c', 'etag': '6TbfrhNGpjRm459tW+0b9CA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 19:34:52,053 - INFO - 批量插入响应体: {'result': ['FINST-34B66L91WQ6YH8MQ6X81E7V8T1C229H8BWJEMB4']}
2025-08-20 19:34:52,053 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-20 19:34:52,053 - INFO - 成功插入的数据ID: ['FINST-34B66L91WQ6YH8MQ6X81E7V8T1C229H8BWJEMB4']
2025-08-20 19:34:57,068 - INFO - 批量插入完成，共 1 条记录
2025-08-20 19:34:57,068 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-20 19:34:57,068 - INFO - 开始处理日期: 2025-08-20
2025-08-20 19:34:57,068 - INFO - Request Parameters - Page 1:
2025-08-20 19:34:57,068 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 19:34:57,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 19:34:57,677 - INFO - Response - Page 1:
2025-08-20 19:34:57,677 - INFO - 第 1 页获取到 4 条记录
2025-08-20 19:34:58,193 - INFO - 查询完成，共获取到 4 条记录
2025-08-20 19:34:58,193 - INFO - 获取到 4 条表单数据
2025-08-20 19:34:58,193 - INFO - 当前日期 2025-08-20 有 4 条MySQL数据需要处理
2025-08-20 19:34:58,193 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 19:34:58,193 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 0 条
2025-08-20 19:34:58,193 - INFO - 同步完成
2025-08-20 22:30:33,381 - INFO - 使用默认增量同步（当天更新数据）
2025-08-20 22:30:33,381 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-20 22:30:33,381 - INFO - 查询参数: ('2025-08-20',)
2025-08-20 22:30:33,569 - INFO - MySQL查询成功，增量数据（日期: 2025-08-20），共获取 246 条记录
2025-08-20 22:30:33,569 - INFO - 获取到 14 个日期需要处理: ['2025-08-01', '2025-08-02', '2025-08-03', '2025-08-04', '2025-08-05', '2025-08-06', '2025-08-07', '2025-08-08', '2025-08-09', '2025-08-10', '2025-08-13', '2025-08-18', '2025-08-19', '2025-08-20']
2025-08-20 22:30:33,569 - INFO - 开始处理日期: 2025-08-01
2025-08-20 22:30:33,569 - INFO - Request Parameters - Page 1:
2025-08-20 22:30:33,569 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:33,569 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:41,693 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FB188F32-95A9-77A8-8525-C296FE43B553 Response: {'code': 'ServiceUnavailable', 'requestid': 'FB188F32-95A9-77A8-8525-C296FE43B553', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FB188F32-95A9-77A8-8525-C296FE43B553)
2025-08-20 22:30:41,693 - INFO - 开始处理日期: 2025-08-02
2025-08-20 22:30:41,693 - INFO - Request Parameters - Page 1:
2025-08-20 22:30:41,693 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:41,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:49,802 - ERROR - 处理日期 2025-08-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A29DBD70-9BC4-7B07-8F9A-************ Response: {'code': 'ServiceUnavailable', 'requestid': 'A29DBD70-9BC4-7B07-8F9A-************', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A29DBD70-9BC4-7B07-8F9A-************)
2025-08-20 22:30:49,802 - INFO - 开始处理日期: 2025-08-03
2025-08-20 22:30:49,802 - INFO - Request Parameters - Page 1:
2025-08-20 22:30:49,802 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:49,802 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:50,708 - INFO - Response - Page 1:
2025-08-20 22:30:50,708 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:30:51,224 - INFO - Request Parameters - Page 2:
2025-08-20 22:30:51,224 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:51,224 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:52,036 - INFO - Response - Page 2:
2025-08-20 22:30:52,036 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:30:52,536 - INFO - Request Parameters - Page 3:
2025-08-20 22:30:52,536 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:52,536 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:53,255 - INFO - Response - Page 3:
2025-08-20 22:30:53,255 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:30:53,771 - INFO - Request Parameters - Page 4:
2025-08-20 22:30:53,771 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:53,771 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:54,505 - INFO - Response - Page 4:
2025-08-20 22:30:54,505 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:30:55,016 - INFO - Request Parameters - Page 5:
2025-08-20 22:30:55,016 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:55,016 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:55,797 - INFO - Response - Page 5:
2025-08-20 22:30:55,797 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:30:56,297 - INFO - Request Parameters - Page 6:
2025-08-20 22:30:56,297 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:56,297 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:57,031 - INFO - Response - Page 6:
2025-08-20 22:30:57,031 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:30:57,547 - INFO - Request Parameters - Page 7:
2025-08-20 22:30:57,547 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:57,547 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:58,297 - INFO - Response - Page 7:
2025-08-20 22:30:58,297 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:30:58,812 - INFO - Request Parameters - Page 8:
2025-08-20 22:30:58,812 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:30:58,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:30:59,562 - INFO - Response - Page 8:
2025-08-20 22:30:59,562 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:31:00,062 - INFO - Request Parameters - Page 9:
2025-08-20 22:31:00,062 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:00,062 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:00,859 - INFO - Response - Page 9:
2025-08-20 22:31:00,859 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:31:01,359 - INFO - Request Parameters - Page 10:
2025-08-20 22:31:01,359 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:01,359 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:02,140 - INFO - Response - Page 10:
2025-08-20 22:31:02,140 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:31:02,656 - INFO - Request Parameters - Page 11:
2025-08-20 22:31:02,656 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:02,656 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:03,265 - INFO - Response - Page 11:
2025-08-20 22:31:03,265 - INFO - 第 11 页获取到 10 条记录
2025-08-20 22:31:03,781 - INFO - 查询完成，共获取到 510 条记录
2025-08-20 22:31:03,781 - INFO - 获取到 510 条表单数据
2025-08-20 22:31:03,781 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-20 22:31:03,781 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:31:03,781 - INFO - 开始处理日期: 2025-08-04
2025-08-20 22:31:03,781 - INFO - Request Parameters - Page 1:
2025-08-20 22:31:03,781 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:03,781 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:04,515 - INFO - Response - Page 1:
2025-08-20 22:31:04,515 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:31:05,031 - INFO - Request Parameters - Page 2:
2025-08-20 22:31:05,031 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:05,031 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:05,781 - INFO - Response - Page 2:
2025-08-20 22:31:05,781 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:31:06,296 - INFO - Request Parameters - Page 3:
2025-08-20 22:31:06,296 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:06,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:06,999 - INFO - Response - Page 3:
2025-08-20 22:31:06,999 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:31:07,515 - INFO - Request Parameters - Page 4:
2025-08-20 22:31:07,515 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:07,515 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:08,328 - INFO - Response - Page 4:
2025-08-20 22:31:08,328 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:31:08,827 - INFO - Request Parameters - Page 5:
2025-08-20 22:31:08,827 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:08,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:09,609 - INFO - Response - Page 5:
2025-08-20 22:31:09,609 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:31:10,109 - INFO - Request Parameters - Page 6:
2025-08-20 22:31:10,109 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:10,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:10,890 - INFO - Response - Page 6:
2025-08-20 22:31:10,890 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:31:11,405 - INFO - Request Parameters - Page 7:
2025-08-20 22:31:11,405 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:11,405 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:12,140 - INFO - Response - Page 7:
2025-08-20 22:31:12,140 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:31:12,655 - INFO - Request Parameters - Page 8:
2025-08-20 22:31:12,655 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:12,655 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:13,437 - INFO - Response - Page 8:
2025-08-20 22:31:13,437 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:31:13,952 - INFO - Request Parameters - Page 9:
2025-08-20 22:31:13,952 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:13,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:14,733 - INFO - Response - Page 9:
2025-08-20 22:31:14,733 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:31:15,249 - INFO - Request Parameters - Page 10:
2025-08-20 22:31:15,249 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:15,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:16,030 - INFO - Response - Page 10:
2025-08-20 22:31:16,030 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:31:16,546 - INFO - Request Parameters - Page 11:
2025-08-20 22:31:16,546 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:16,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:17,218 - INFO - Response - Page 11:
2025-08-20 22:31:17,218 - INFO - 第 11 页获取到 21 条记录
2025-08-20 22:31:17,718 - INFO - 查询完成，共获取到 521 条记录
2025-08-20 22:31:17,718 - INFO - 获取到 521 条表单数据
2025-08-20 22:31:17,718 - INFO - 当前日期 2025-08-04 有 1 条MySQL数据需要处理
2025-08-20 22:31:17,718 - INFO - 日期 2025-08-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:31:17,718 - INFO - 开始处理日期: 2025-08-05
2025-08-20 22:31:17,718 - INFO - Request Parameters - Page 1:
2025-08-20 22:31:17,718 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:17,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:18,452 - INFO - Response - Page 1:
2025-08-20 22:31:18,452 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:31:18,968 - INFO - Request Parameters - Page 2:
2025-08-20 22:31:18,968 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:18,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:19,686 - INFO - Response - Page 2:
2025-08-20 22:31:19,686 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:31:20,202 - INFO - Request Parameters - Page 3:
2025-08-20 22:31:20,202 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:20,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:21,014 - INFO - Response - Page 3:
2025-08-20 22:31:21,014 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:31:21,530 - INFO - Request Parameters - Page 4:
2025-08-20 22:31:21,530 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:21,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:22,264 - INFO - Response - Page 4:
2025-08-20 22:31:22,264 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:31:22,764 - INFO - Request Parameters - Page 5:
2025-08-20 22:31:22,764 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:22,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:23,545 - INFO - Response - Page 5:
2025-08-20 22:31:23,545 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:31:24,045 - INFO - Request Parameters - Page 6:
2025-08-20 22:31:24,045 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:24,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:24,811 - INFO - Response - Page 6:
2025-08-20 22:31:24,811 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:31:25,311 - INFO - Request Parameters - Page 7:
2025-08-20 22:31:25,311 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:25,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:26,061 - INFO - Response - Page 7:
2025-08-20 22:31:26,061 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:31:26,561 - INFO - Request Parameters - Page 8:
2025-08-20 22:31:26,561 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:26,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:27,326 - INFO - Response - Page 8:
2025-08-20 22:31:27,326 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:31:27,842 - INFO - Request Parameters - Page 9:
2025-08-20 22:31:27,842 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:27,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:28,654 - INFO - Response - Page 9:
2025-08-20 22:31:28,654 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:31:29,170 - INFO - Request Parameters - Page 10:
2025-08-20 22:31:29,170 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:29,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:29,951 - INFO - Response - Page 10:
2025-08-20 22:31:29,951 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:31:30,467 - INFO - Request Parameters - Page 11:
2025-08-20 22:31:30,467 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:30,467 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754323200000, 1754409599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:31,201 - INFO - Response - Page 11:
2025-08-20 22:31:31,201 - INFO - 第 11 页获取到 36 条记录
2025-08-20 22:31:31,701 - INFO - 查询完成，共获取到 536 条记录
2025-08-20 22:31:31,701 - INFO - 获取到 536 条表单数据
2025-08-20 22:31:31,701 - INFO - 当前日期 2025-08-05 有 1 条MySQL数据需要处理
2025-08-20 22:31:31,701 - INFO - 日期 2025-08-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:31:31,701 - INFO - 开始处理日期: 2025-08-06
2025-08-20 22:31:31,701 - INFO - Request Parameters - Page 1:
2025-08-20 22:31:31,701 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:31,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:32,435 - INFO - Response - Page 1:
2025-08-20 22:31:32,435 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:31:32,935 - INFO - Request Parameters - Page 2:
2025-08-20 22:31:32,935 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:32,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:33,717 - INFO - Response - Page 2:
2025-08-20 22:31:33,717 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:31:34,217 - INFO - Request Parameters - Page 3:
2025-08-20 22:31:34,217 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:34,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:35,013 - INFO - Response - Page 3:
2025-08-20 22:31:35,013 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:31:35,529 - INFO - Request Parameters - Page 4:
2025-08-20 22:31:35,529 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:35,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:36,279 - INFO - Response - Page 4:
2025-08-20 22:31:36,279 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:31:36,795 - INFO - Request Parameters - Page 5:
2025-08-20 22:31:36,795 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:36,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:37,576 - INFO - Response - Page 5:
2025-08-20 22:31:37,576 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:31:38,091 - INFO - Request Parameters - Page 6:
2025-08-20 22:31:38,091 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:38,091 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:38,841 - INFO - Response - Page 6:
2025-08-20 22:31:38,841 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:31:39,341 - INFO - Request Parameters - Page 7:
2025-08-20 22:31:39,341 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:39,341 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:40,060 - INFO - Response - Page 7:
2025-08-20 22:31:40,060 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:31:40,576 - INFO - Request Parameters - Page 8:
2025-08-20 22:31:40,576 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:40,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:41,310 - INFO - Response - Page 8:
2025-08-20 22:31:41,310 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:31:41,826 - INFO - Request Parameters - Page 9:
2025-08-20 22:31:41,826 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:41,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:42,638 - INFO - Response - Page 9:
2025-08-20 22:31:42,638 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:31:43,154 - INFO - Request Parameters - Page 10:
2025-08-20 22:31:43,154 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:43,154 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:43,904 - INFO - Response - Page 10:
2025-08-20 22:31:43,904 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:31:44,419 - INFO - Request Parameters - Page 11:
2025-08-20 22:31:44,419 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:44,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754409600000, 1754495999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:45,153 - INFO - Response - Page 11:
2025-08-20 22:31:45,153 - INFO - 第 11 页获取到 26 条记录
2025-08-20 22:31:45,669 - INFO - 查询完成，共获取到 526 条记录
2025-08-20 22:31:45,669 - INFO - 获取到 526 条表单数据
2025-08-20 22:31:45,669 - INFO - 当前日期 2025-08-06 有 1 条MySQL数据需要处理
2025-08-20 22:31:45,669 - INFO - 日期 2025-08-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:31:45,669 - INFO - 开始处理日期: 2025-08-07
2025-08-20 22:31:45,669 - INFO - Request Parameters - Page 1:
2025-08-20 22:31:45,669 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:45,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:46,419 - INFO - Response - Page 1:
2025-08-20 22:31:46,419 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:31:46,919 - INFO - Request Parameters - Page 2:
2025-08-20 22:31:46,919 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:46,919 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:47,685 - INFO - Response - Page 2:
2025-08-20 22:31:47,685 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:31:48,200 - INFO - Request Parameters - Page 3:
2025-08-20 22:31:48,200 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:48,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:48,919 - INFO - Response - Page 3:
2025-08-20 22:31:48,919 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:31:49,419 - INFO - Request Parameters - Page 4:
2025-08-20 22:31:49,419 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:49,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:50,200 - INFO - Response - Page 4:
2025-08-20 22:31:50,200 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:31:50,700 - INFO - Request Parameters - Page 5:
2025-08-20 22:31:50,700 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:50,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:51,481 - INFO - Response - Page 5:
2025-08-20 22:31:51,481 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:31:51,981 - INFO - Request Parameters - Page 6:
2025-08-20 22:31:51,981 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:51,981 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:52,700 - INFO - Response - Page 6:
2025-08-20 22:31:52,700 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:31:53,200 - INFO - Request Parameters - Page 7:
2025-08-20 22:31:53,200 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:53,200 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:53,934 - INFO - Response - Page 7:
2025-08-20 22:31:53,934 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:31:54,434 - INFO - Request Parameters - Page 8:
2025-08-20 22:31:54,434 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:54,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:55,164 - INFO - Response - Page 8:
2025-08-20 22:31:55,164 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:31:55,664 - INFO - Request Parameters - Page 9:
2025-08-20 22:31:55,664 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:55,664 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:56,429 - INFO - Response - Page 9:
2025-08-20 22:31:56,429 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:31:56,929 - INFO - Request Parameters - Page 10:
2025-08-20 22:31:56,929 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:56,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:57,710 - INFO - Response - Page 10:
2025-08-20 22:31:57,710 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:31:58,210 - INFO - Request Parameters - Page 11:
2025-08-20 22:31:58,210 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:58,210 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754496000000, 1754582399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:31:58,992 - INFO - Response - Page 11:
2025-08-20 22:31:58,992 - INFO - 第 11 页获取到 46 条记录
2025-08-20 22:31:59,492 - INFO - 查询完成，共获取到 546 条记录
2025-08-20 22:31:59,492 - INFO - 获取到 546 条表单数据
2025-08-20 22:31:59,507 - INFO - 当前日期 2025-08-07 有 1 条MySQL数据需要处理
2025-08-20 22:31:59,507 - INFO - 日期 2025-08-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:31:59,507 - INFO - 开始处理日期: 2025-08-08
2025-08-20 22:31:59,507 - INFO - Request Parameters - Page 1:
2025-08-20 22:31:59,507 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:31:59,507 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:00,257 - INFO - Response - Page 1:
2025-08-20 22:32:00,257 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:32:00,757 - INFO - Request Parameters - Page 2:
2025-08-20 22:32:00,757 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:00,757 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:01,523 - INFO - Response - Page 2:
2025-08-20 22:32:01,523 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:32:02,038 - INFO - Request Parameters - Page 3:
2025-08-20 22:32:02,038 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:02,038 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:02,757 - INFO - Response - Page 3:
2025-08-20 22:32:02,757 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:32:03,257 - INFO - Request Parameters - Page 4:
2025-08-20 22:32:03,257 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:03,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:03,991 - INFO - Response - Page 4:
2025-08-20 22:32:03,991 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:32:04,491 - INFO - Request Parameters - Page 5:
2025-08-20 22:32:04,491 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:04,491 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:05,210 - INFO - Response - Page 5:
2025-08-20 22:32:05,210 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:32:05,726 - INFO - Request Parameters - Page 6:
2025-08-20 22:32:05,726 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:05,726 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:06,491 - INFO - Response - Page 6:
2025-08-20 22:32:06,491 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:32:07,007 - INFO - Request Parameters - Page 7:
2025-08-20 22:32:07,007 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:07,007 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:07,819 - INFO - Response - Page 7:
2025-08-20 22:32:07,819 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:32:08,335 - INFO - Request Parameters - Page 8:
2025-08-20 22:32:08,335 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:08,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:09,116 - INFO - Response - Page 8:
2025-08-20 22:32:09,116 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:32:09,632 - INFO - Request Parameters - Page 9:
2025-08-20 22:32:09,632 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:09,632 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:10,335 - INFO - Response - Page 9:
2025-08-20 22:32:10,335 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:32:10,835 - INFO - Request Parameters - Page 10:
2025-08-20 22:32:10,835 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:10,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754582400000, 1754668799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:11,616 - INFO - Response - Page 10:
2025-08-20 22:32:11,616 - INFO - 第 10 页获取到 47 条记录
2025-08-20 22:32:12,131 - INFO - 查询完成，共获取到 497 条记录
2025-08-20 22:32:12,131 - INFO - 获取到 497 条表单数据
2025-08-20 22:32:12,131 - INFO - 当前日期 2025-08-08 有 1 条MySQL数据需要处理
2025-08-20 22:32:12,131 - INFO - 日期 2025-08-08 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:32:12,131 - INFO - 开始处理日期: 2025-08-09
2025-08-20 22:32:12,131 - INFO - Request Parameters - Page 1:
2025-08-20 22:32:12,131 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:12,131 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:12,913 - INFO - Response - Page 1:
2025-08-20 22:32:12,913 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:32:13,428 - INFO - Request Parameters - Page 2:
2025-08-20 22:32:13,428 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:13,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:14,209 - INFO - Response - Page 2:
2025-08-20 22:32:14,209 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:32:14,709 - INFO - Request Parameters - Page 3:
2025-08-20 22:32:14,709 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:14,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:15,444 - INFO - Response - Page 3:
2025-08-20 22:32:15,444 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:32:15,959 - INFO - Request Parameters - Page 4:
2025-08-20 22:32:15,959 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:15,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:16,678 - INFO - Response - Page 4:
2025-08-20 22:32:16,678 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:32:17,194 - INFO - Request Parameters - Page 5:
2025-08-20 22:32:17,194 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:17,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:17,975 - INFO - Response - Page 5:
2025-08-20 22:32:17,975 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:32:18,490 - INFO - Request Parameters - Page 6:
2025-08-20 22:32:18,490 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:18,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:19,272 - INFO - Response - Page 6:
2025-08-20 22:32:19,272 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:32:19,787 - INFO - Request Parameters - Page 7:
2025-08-20 22:32:19,787 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:19,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:20,568 - INFO - Response - Page 7:
2025-08-20 22:32:20,568 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:32:21,068 - INFO - Request Parameters - Page 8:
2025-08-20 22:32:21,068 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:21,068 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:21,896 - INFO - Response - Page 8:
2025-08-20 22:32:21,896 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:32:22,396 - INFO - Request Parameters - Page 9:
2025-08-20 22:32:22,396 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:22,396 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:23,146 - INFO - Response - Page 9:
2025-08-20 22:32:23,146 - INFO - 第 9 页获取到 47 条记录
2025-08-20 22:32:23,646 - INFO - 查询完成，共获取到 447 条记录
2025-08-20 22:32:23,646 - INFO - 获取到 447 条表单数据
2025-08-20 22:32:23,646 - INFO - 当前日期 2025-08-09 有 2 条MySQL数据需要处理
2025-08-20 22:32:23,646 - INFO - 日期 2025-08-09 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:32:23,646 - INFO - 开始处理日期: 2025-08-10
2025-08-20 22:32:23,646 - INFO - Request Parameters - Page 1:
2025-08-20 22:32:23,646 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:23,646 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:24,412 - INFO - Response - Page 1:
2025-08-20 22:32:24,412 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:32:24,928 - INFO - Request Parameters - Page 2:
2025-08-20 22:32:24,928 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:24,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:25,678 - INFO - Response - Page 2:
2025-08-20 22:32:25,678 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:32:26,193 - INFO - Request Parameters - Page 3:
2025-08-20 22:32:26,193 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:26,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:26,927 - INFO - Response - Page 3:
2025-08-20 22:32:26,927 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:32:27,427 - INFO - Request Parameters - Page 4:
2025-08-20 22:32:27,427 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:27,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:28,146 - INFO - Response - Page 4:
2025-08-20 22:32:28,146 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:32:28,662 - INFO - Request Parameters - Page 5:
2025-08-20 22:32:28,662 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:28,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:29,443 - INFO - Response - Page 5:
2025-08-20 22:32:29,443 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:32:29,943 - INFO - Request Parameters - Page 6:
2025-08-20 22:32:29,943 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:29,943 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:30,662 - INFO - Response - Page 6:
2025-08-20 22:32:30,662 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:32:31,177 - INFO - Request Parameters - Page 7:
2025-08-20 22:32:31,177 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:31,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:31,912 - INFO - Response - Page 7:
2025-08-20 22:32:31,912 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:32:32,411 - INFO - Request Parameters - Page 8:
2025-08-20 22:32:32,411 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:32,411 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:33,146 - INFO - Response - Page 8:
2025-08-20 22:32:33,146 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:32:33,661 - INFO - Request Parameters - Page 9:
2025-08-20 22:32:33,661 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:33,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:34,396 - INFO - Response - Page 9:
2025-08-20 22:32:34,396 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:32:34,911 - INFO - Request Parameters - Page 10:
2025-08-20 22:32:34,911 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:34,911 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:35,677 - INFO - Response - Page 10:
2025-08-20 22:32:35,677 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:32:36,193 - INFO - Request Parameters - Page 11:
2025-08-20 22:32:36,193 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:36,193 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:36,880 - INFO - Response - Page 11:
2025-08-20 22:32:36,880 - INFO - 第 11 页获取到 27 条记录
2025-08-20 22:32:37,380 - INFO - 查询完成，共获取到 527 条记录
2025-08-20 22:32:37,380 - INFO - 获取到 527 条表单数据
2025-08-20 22:32:37,380 - INFO - 当前日期 2025-08-10 有 1 条MySQL数据需要处理
2025-08-20 22:32:37,380 - INFO - 日期 2025-08-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:32:37,380 - INFO - 开始处理日期: 2025-08-13
2025-08-20 22:32:37,380 - INFO - Request Parameters - Page 1:
2025-08-20 22:32:37,380 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:37,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:38,099 - INFO - Response - Page 1:
2025-08-20 22:32:38,099 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:32:38,614 - INFO - Request Parameters - Page 2:
2025-08-20 22:32:38,614 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:38,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:39,380 - INFO - Response - Page 2:
2025-08-20 22:32:39,380 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:32:39,880 - INFO - Request Parameters - Page 3:
2025-08-20 22:32:39,880 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:39,880 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:40,630 - INFO - Response - Page 3:
2025-08-20 22:32:40,630 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:32:41,145 - INFO - Request Parameters - Page 4:
2025-08-20 22:32:41,145 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:41,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:41,895 - INFO - Response - Page 4:
2025-08-20 22:32:41,895 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:32:42,395 - INFO - Request Parameters - Page 5:
2025-08-20 22:32:42,395 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:42,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:43,130 - INFO - Response - Page 5:
2025-08-20 22:32:43,130 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:32:43,630 - INFO - Request Parameters - Page 6:
2025-08-20 22:32:43,630 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:43,630 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:44,380 - INFO - Response - Page 6:
2025-08-20 22:32:44,380 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:32:44,895 - INFO - Request Parameters - Page 7:
2025-08-20 22:32:44,895 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:44,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:45,645 - INFO - Response - Page 7:
2025-08-20 22:32:45,645 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:32:46,161 - INFO - Request Parameters - Page 8:
2025-08-20 22:32:46,161 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:46,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:46,879 - INFO - Response - Page 8:
2025-08-20 22:32:46,879 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:32:47,395 - INFO - Request Parameters - Page 9:
2025-08-20 22:32:47,395 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:47,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:48,145 - INFO - Response - Page 9:
2025-08-20 22:32:48,145 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:32:48,661 - INFO - Request Parameters - Page 10:
2025-08-20 22:32:48,661 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:48,661 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:49,379 - INFO - Response - Page 10:
2025-08-20 22:32:49,379 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:32:49,895 - INFO - Request Parameters - Page 11:
2025-08-20 22:32:49,895 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:49,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755014400000, 1755100799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:50,676 - INFO - Response - Page 11:
2025-08-20 22:32:50,676 - INFO - 第 11 页获取到 48 条记录
2025-08-20 22:32:51,192 - INFO - 查询完成，共获取到 548 条记录
2025-08-20 22:32:51,192 - INFO - 获取到 548 条表单数据
2025-08-20 22:32:51,192 - INFO - 当前日期 2025-08-13 有 1 条MySQL数据需要处理
2025-08-20 22:32:51,192 - INFO - 日期 2025-08-13 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:32:51,192 - INFO - 开始处理日期: 2025-08-18
2025-08-20 22:32:51,192 - INFO - Request Parameters - Page 1:
2025-08-20 22:32:51,192 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:51,192 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:51,942 - INFO - Response - Page 1:
2025-08-20 22:32:51,942 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:32:52,457 - INFO - Request Parameters - Page 2:
2025-08-20 22:32:52,457 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:52,457 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:53,160 - INFO - Response - Page 2:
2025-08-20 22:32:53,160 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:32:53,660 - INFO - Request Parameters - Page 3:
2025-08-20 22:32:53,660 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:53,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:54,410 - INFO - Response - Page 3:
2025-08-20 22:32:54,426 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:32:54,941 - INFO - Request Parameters - Page 4:
2025-08-20 22:32:54,941 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:54,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:55,733 - INFO - Response - Page 4:
2025-08-20 22:32:55,733 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:32:56,249 - INFO - Request Parameters - Page 5:
2025-08-20 22:32:56,249 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:56,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:57,030 - INFO - Response - Page 5:
2025-08-20 22:32:57,030 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:32:57,530 - INFO - Request Parameters - Page 6:
2025-08-20 22:32:57,530 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:57,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:58,264 - INFO - Response - Page 6:
2025-08-20 22:32:58,264 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:32:58,764 - INFO - Request Parameters - Page 7:
2025-08-20 22:32:58,764 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:32:58,764 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:32:59,530 - INFO - Response - Page 7:
2025-08-20 22:32:59,530 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:33:00,046 - INFO - Request Parameters - Page 8:
2025-08-20 22:33:00,046 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:00,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:00,717 - INFO - Response - Page 8:
2025-08-20 22:33:00,717 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:33:01,217 - INFO - Request Parameters - Page 9:
2025-08-20 22:33:01,217 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:01,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:01,999 - INFO - Response - Page 9:
2025-08-20 22:33:01,999 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:33:02,514 - INFO - Request Parameters - Page 10:
2025-08-20 22:33:02,514 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:02,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:03,295 - INFO - Response - Page 10:
2025-08-20 22:33:03,295 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:33:03,795 - INFO - Request Parameters - Page 11:
2025-08-20 22:33:03,795 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:03,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755446400000, 1755532799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:04,514 - INFO - Response - Page 11:
2025-08-20 22:33:04,514 - INFO - 第 11 页获取到 26 条记录
2025-08-20 22:33:05,030 - INFO - 查询完成，共获取到 526 条记录
2025-08-20 22:33:05,030 - INFO - 获取到 526 条表单数据
2025-08-20 22:33:05,030 - INFO - 当前日期 2025-08-18 有 1 条MySQL数据需要处理
2025-08-20 22:33:05,030 - INFO - 日期 2025-08-18 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:33:05,030 - INFO - 开始处理日期: 2025-08-19
2025-08-20 22:33:05,030 - INFO - Request Parameters - Page 1:
2025-08-20 22:33:05,030 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:05,030 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:05,811 - INFO - Response - Page 1:
2025-08-20 22:33:05,811 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:33:06,311 - INFO - Request Parameters - Page 2:
2025-08-20 22:33:06,311 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:06,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:07,076 - INFO - Response - Page 2:
2025-08-20 22:33:07,076 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:33:07,576 - INFO - Request Parameters - Page 3:
2025-08-20 22:33:07,576 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:07,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:08,342 - INFO - Response - Page 3:
2025-08-20 22:33:08,342 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:33:08,842 - INFO - Request Parameters - Page 4:
2025-08-20 22:33:08,842 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:08,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:09,545 - INFO - Response - Page 4:
2025-08-20 22:33:09,545 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:33:10,045 - INFO - Request Parameters - Page 5:
2025-08-20 22:33:10,045 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:10,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:10,795 - INFO - Response - Page 5:
2025-08-20 22:33:10,795 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:33:11,311 - INFO - Request Parameters - Page 6:
2025-08-20 22:33:11,311 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:11,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:12,045 - INFO - Response - Page 6:
2025-08-20 22:33:12,045 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:33:12,545 - INFO - Request Parameters - Page 7:
2025-08-20 22:33:12,545 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:12,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:13,295 - INFO - Response - Page 7:
2025-08-20 22:33:13,295 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:33:13,795 - INFO - Request Parameters - Page 8:
2025-08-20 22:33:13,795 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:13,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:14,560 - INFO - Response - Page 8:
2025-08-20 22:33:14,560 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:33:15,060 - INFO - Request Parameters - Page 9:
2025-08-20 22:33:15,060 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:15,060 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:15,779 - INFO - Response - Page 9:
2025-08-20 22:33:15,779 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:33:16,295 - INFO - Request Parameters - Page 10:
2025-08-20 22:33:16,295 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:16,295 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:17,013 - INFO - Response - Page 10:
2025-08-20 22:33:17,013 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:33:17,513 - INFO - Request Parameters - Page 11:
2025-08-20 22:33:17,513 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:17,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:18,263 - INFO - Response - Page 11:
2025-08-20 22:33:18,263 - INFO - 第 11 页获取到 50 条记录
2025-08-20 22:33:18,763 - INFO - Request Parameters - Page 12:
2025-08-20 22:33:18,763 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:18,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:19,310 - INFO - Response - Page 12:
2025-08-20 22:33:19,310 - INFO - 第 12 页获取到 2 条记录
2025-08-20 22:33:19,810 - INFO - 查询完成，共获取到 552 条记录
2025-08-20 22:33:19,810 - INFO - 获取到 552 条表单数据
2025-08-20 22:33:19,826 - INFO - 当前日期 2025-08-19 有 165 条MySQL数据需要处理
2025-08-20 22:33:19,826 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:33:19,826 - INFO - 开始处理日期: 2025-08-20
2025-08-20 22:33:19,826 - INFO - Request Parameters - Page 1:
2025-08-20 22:33:19,826 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:33:19,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:33:20,373 - INFO - Response - Page 1:
2025-08-20 22:33:20,373 - INFO - 第 1 页获取到 4 条记录
2025-08-20 22:33:20,873 - INFO - 查询完成，共获取到 4 条记录
2025-08-20 22:33:20,873 - INFO - 获取到 4 条表单数据
2025-08-20 22:33:20,873 - INFO - 当前日期 2025-08-20 有 58 条MySQL数据需要处理
2025-08-20 22:33:20,873 - INFO - 开始批量插入 54 条新记录
2025-08-20 22:33:21,107 - INFO - 批量插入响应状态码: 200
2025-08-20 22:33:21,107 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 14:33:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0E49D6D4-EF57-780E-A823-60250ED29E92', 'x-acs-trace-id': 'ee243424cfc4e5d787459bcaa09dd5e9', 'etag': '2jnsUZ2s5Grb4Oa1/9qLMOg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 22:33:21,107 - INFO - 批量插入响应体: {'result': ['FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM83', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM93', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMA3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMB3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMC3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMD3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEME3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMF3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMG3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMH3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMI3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMJ3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMK3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEML3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMM3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMN3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMO3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMP3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMQ3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMR3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMS3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMT3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMU3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMV3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMW3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMX3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMY3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMZ3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM04', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM14', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM24', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM34', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM44', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM54', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM64', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM74', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM84', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM94', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMA4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMB4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMC4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMD4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEME4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMF4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMG4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMH4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMI4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMJ4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMK4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEML4']}
2025-08-20 22:33:21,107 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-20 22:33:21,107 - INFO - 成功插入的数据ID: ['FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM83', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM93', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMA3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMB3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMC3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMD3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEME3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMF3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMG3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMH3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMI3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMJ3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMK3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEML3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMM3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMN3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMO3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMP3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMQ3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMR3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMS3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMT3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMU3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMV3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMW3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMX3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMY3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMZ3', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM04', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM14', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM24', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM34', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM44', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM54', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM64', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM74', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM84', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEM94', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMA4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMB4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMC4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMD4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEME4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMF4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMG4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMH4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMI4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMJ4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEMK4', 'FINST-3RE66ZB18S6YL0BY63WTZ8W9HXY6364SO2KEML4']
2025-08-20 22:33:26,310 - INFO - 批量插入响应状态码: 200
2025-08-20 22:33:26,310 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 14:33:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '204', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '68ED871D-A0FD-79AC-AC87-903ABE1FC29F', 'x-acs-trace-id': '85a1ff0d7c64463d7d56faa56ab54339', 'etag': '23dKMCQum2fdVO2eiVriyAw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-20 22:33:26,310 - INFO - 批量插入响应体: {'result': ['FINST-1OC66A91VT6YC796DQI70BOUYJ3B345WO2KEM33', 'FINST-1OC66A91VT6YC796DQI70BOUYJ3B345WO2KEM43', 'FINST-1OC66A91VT6YC796DQI70BOUYJ3B345WO2KEM53', 'FINST-1OC66A91VT6YC796DQI70BOUYJ3B345WO2KEM63']}
2025-08-20 22:33:26,310 - INFO - 批量插入表单数据成功，批次 2，共 4 条记录
2025-08-20 22:33:26,310 - INFO - 成功插入的数据ID: ['FINST-1OC66A91VT6YC796DQI70BOUYJ3B345WO2KEM33', 'FINST-1OC66A91VT6YC796DQI70BOUYJ3B345WO2KEM43', 'FINST-1OC66A91VT6YC796DQI70BOUYJ3B345WO2KEM53', 'FINST-1OC66A91VT6YC796DQI70BOUYJ3B345WO2KEM63']
2025-08-20 22:33:31,325 - INFO - 批量插入完成，共 54 条记录
2025-08-20 22:33:31,325 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 54 条，错误: 0 条
2025-08-20 22:33:31,325 - INFO - 数据同步完成！更新: 0 条，插入: 54 条，错误: 2 条
2025-08-20 22:34:31,332 - INFO - 开始同步昨天与今天的销售数据: 2025-08-19 至 2025-08-20
2025-08-20 22:34:31,332 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-20 22:34:31,332 - INFO - 查询参数: ('2025-08-19', '2025-08-20')
2025-08-20 22:34:31,520 - INFO - MySQL查询成功，时间段: 2025-08-19 至 2025-08-20，共获取 632 条记录
2025-08-20 22:34:31,520 - INFO - 获取到 2 个日期需要处理: ['2025-08-19', '2025-08-20']
2025-08-20 22:34:31,520 - INFO - 开始处理日期: 2025-08-19
2025-08-20 22:34:31,520 - INFO - Request Parameters - Page 1:
2025-08-20 22:34:31,520 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:31,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:32,379 - INFO - Response - Page 1:
2025-08-20 22:34:32,379 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:34:32,895 - INFO - Request Parameters - Page 2:
2025-08-20 22:34:32,895 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:32,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:33,660 - INFO - Response - Page 2:
2025-08-20 22:34:33,660 - INFO - 第 2 页获取到 50 条记录
2025-08-20 22:34:34,160 - INFO - Request Parameters - Page 3:
2025-08-20 22:34:34,160 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:34,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:34,879 - INFO - Response - Page 3:
2025-08-20 22:34:34,879 - INFO - 第 3 页获取到 50 条记录
2025-08-20 22:34:35,395 - INFO - Request Parameters - Page 4:
2025-08-20 22:34:35,395 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:35,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:36,160 - INFO - Response - Page 4:
2025-08-20 22:34:36,160 - INFO - 第 4 页获取到 50 条记录
2025-08-20 22:34:36,676 - INFO - Request Parameters - Page 5:
2025-08-20 22:34:36,676 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:36,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:37,379 - INFO - Response - Page 5:
2025-08-20 22:34:37,379 - INFO - 第 5 页获取到 50 条记录
2025-08-20 22:34:37,879 - INFO - Request Parameters - Page 6:
2025-08-20 22:34:37,879 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:37,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:38,644 - INFO - Response - Page 6:
2025-08-20 22:34:38,644 - INFO - 第 6 页获取到 50 条记录
2025-08-20 22:34:39,144 - INFO - Request Parameters - Page 7:
2025-08-20 22:34:39,144 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:39,144 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:39,879 - INFO - Response - Page 7:
2025-08-20 22:34:39,879 - INFO - 第 7 页获取到 50 条记录
2025-08-20 22:34:40,379 - INFO - Request Parameters - Page 8:
2025-08-20 22:34:40,379 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:40,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:41,144 - INFO - Response - Page 8:
2025-08-20 22:34:41,144 - INFO - 第 8 页获取到 50 条记录
2025-08-20 22:34:41,644 - INFO - Request Parameters - Page 9:
2025-08-20 22:34:41,644 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:41,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:42,441 - INFO - Response - Page 9:
2025-08-20 22:34:42,441 - INFO - 第 9 页获取到 50 条记录
2025-08-20 22:34:42,957 - INFO - Request Parameters - Page 10:
2025-08-20 22:34:42,957 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:42,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:43,675 - INFO - Response - Page 10:
2025-08-20 22:34:43,675 - INFO - 第 10 页获取到 50 条记录
2025-08-20 22:34:44,175 - INFO - Request Parameters - Page 11:
2025-08-20 22:34:44,175 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:44,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:44,941 - INFO - Response - Page 11:
2025-08-20 22:34:44,941 - INFO - 第 11 页获取到 50 条记录
2025-08-20 22:34:45,441 - INFO - Request Parameters - Page 12:
2025-08-20 22:34:45,441 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:45,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755532800000, 1755619199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:45,972 - INFO - Response - Page 12:
2025-08-20 22:34:45,972 - INFO - 第 12 页获取到 2 条记录
2025-08-20 22:34:46,488 - INFO - 查询完成，共获取到 552 条记录
2025-08-20 22:34:46,488 - INFO - 获取到 552 条表单数据
2025-08-20 22:34:46,488 - INFO - 当前日期 2025-08-19 有 552 条MySQL数据需要处理
2025-08-20 22:34:46,503 - INFO - 日期 2025-08-19 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:34:46,503 - INFO - 开始处理日期: 2025-08-20
2025-08-20 22:34:46,503 - INFO - Request Parameters - Page 1:
2025-08-20 22:34:46,503 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:46,503 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:47,222 - INFO - Response - Page 1:
2025-08-20 22:34:47,222 - INFO - 第 1 页获取到 50 条记录
2025-08-20 22:34:47,722 - INFO - Request Parameters - Page 2:
2025-08-20 22:34:47,722 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-20 22:34:47,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-20 22:34:48,284 - INFO - Response - Page 2:
2025-08-20 22:34:48,284 - INFO - 第 2 页获取到 8 条记录
2025-08-20 22:34:48,784 - INFO - 查询完成，共获取到 58 条记录
2025-08-20 22:34:48,784 - INFO - 获取到 58 条表单数据
2025-08-20 22:34:48,784 - INFO - 当前日期 2025-08-20 有 58 条MySQL数据需要处理
2025-08-20 22:34:48,784 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:34:48,784 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-20 22:34:48,784 - INFO - 同步完成
