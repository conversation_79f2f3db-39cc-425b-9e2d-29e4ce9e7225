2025-08-15 01:30:33,857 - INFO - 使用默认增量同步（当天更新数据）
2025-08-15 01:30:33,857 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-15 01:30:33,857 - INFO - 查询参数: ('2025-08-15',)
2025-08-15 01:30:33,951 - INFO - MySQL查询成功，增量数据（日期: 2025-08-15），共获取 0 条记录
2025-08-15 01:30:33,951 - ERROR - 未获取到MySQL数据
2025-08-15 01:31:33,961 - INFO - 开始同步昨天与今天的销售数据: 2025-08-14 至 2025-08-15
2025-08-15 01:31:33,961 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-15 01:31:33,961 - INFO - 查询参数: ('2025-08-14', '2025-08-15')
2025-08-15 01:31:34,117 - INFO - MySQL查询成功，时间段: 2025-08-14 至 2025-08-15，共获取 26 条记录
2025-08-15 01:31:34,117 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 01:31:34,117 - INFO - 开始处理日期: 2025-08-14
2025-08-15 01:31:34,117 - INFO - Request Parameters - Page 1:
2025-08-15 01:31:34,117 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 01:31:34,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 01:31:42,258 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 93F65E3F-C80D-7988-8317-A88991497BB4 Response: {'code': 'ServiceUnavailable', 'requestid': '93F65E3F-C80D-7988-8317-A88991497BB4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 93F65E3F-C80D-7988-8317-A88991497BB4)
2025-08-15 01:31:42,258 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 01:31:42,258 - INFO - 同步完成
2025-08-15 04:30:32,980 - INFO - 使用默认增量同步（当天更新数据）
2025-08-15 04:30:32,980 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-15 04:30:32,980 - INFO - 查询参数: ('2025-08-15',)
2025-08-15 04:30:33,137 - INFO - MySQL查询成功，增量数据（日期: 2025-08-15），共获取 2 条记录
2025-08-15 04:30:33,137 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 04:30:33,137 - INFO - 开始处理日期: 2025-08-14
2025-08-15 04:30:33,152 - INFO - Request Parameters - Page 1:
2025-08-15 04:30:33,152 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 04:30:33,152 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 04:30:41,262 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0481FF2F-FE99-75D2-A441-B92A2C3F579F Response: {'code': 'ServiceUnavailable', 'requestid': '0481FF2F-FE99-75D2-A441-B92A2C3F579F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0481FF2F-FE99-75D2-A441-B92A2C3F579F)
2025-08-15 04:30:41,262 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 04:31:41,272 - INFO - 开始同步昨天与今天的销售数据: 2025-08-14 至 2025-08-15
2025-08-15 04:31:41,272 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-15 04:31:41,272 - INFO - 查询参数: ('2025-08-14', '2025-08-15')
2025-08-15 04:31:41,428 - INFO - MySQL查询成功，时间段: 2025-08-14 至 2025-08-15，共获取 50 条记录
2025-08-15 04:31:41,428 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 04:31:41,428 - INFO - 开始处理日期: 2025-08-14
2025-08-15 04:31:41,428 - INFO - Request Parameters - Page 1:
2025-08-15 04:31:41,428 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 04:31:41,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 04:31:42,069 - INFO - Response - Page 1:
2025-08-15 04:31:42,069 - INFO - 第 1 页获取到 6 条记录
2025-08-15 04:31:42,584 - INFO - 查询完成，共获取到 6 条记录
2025-08-15 04:31:42,584 - INFO - 获取到 6 条表单数据
2025-08-15 04:31:42,584 - INFO - 当前日期 2025-08-14 有 46 条MySQL数据需要处理
2025-08-15 04:31:42,584 - INFO - 开始批量插入 40 条新记录
2025-08-15 04:31:42,819 - INFO - 批量插入响应状态码: 200
2025-08-15 04:31:42,819 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 14 Aug 2025 20:31:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1932', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3CEC1037-144F-7E01-8DEA-5B71F1A3263E', 'x-acs-trace-id': 'e1425d1d723399b68e9b6bf5b5d9e7c0', 'etag': '17ybCwfA67VDlGxgyO5hR0Q2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 04:31:42,819 - INFO - 批量插入响应体: {'result': ['FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM2E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM3E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM4E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM5E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM6E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM7E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM8E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM9E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMAE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMBE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMCE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMDE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMEE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMFE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMGE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMHE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMIE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMJE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMKE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMLE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMME', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMNE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMOE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMPE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMQE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMRE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMSE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMTE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMUE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMVE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMWE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMXE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMYE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMZE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM0F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM1F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM2F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM3F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM4F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM5F']}
2025-08-15 04:31:42,819 - INFO - 批量插入表单数据成功，批次 1，共 40 条记录
2025-08-15 04:31:42,819 - INFO - 成功插入的数据ID: ['FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM2E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM3E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM4E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM5E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM6E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM7E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM8E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM9E', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMAE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMBE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMCE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMDE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMEE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMFE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMGE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMHE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMIE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMJE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMKE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMLE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMME', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMNE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMOE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMPE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMQE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMRE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMSE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMTE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMUE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMVE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMWE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMXE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMYE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEMZE', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM0F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM1F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM2F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM3F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM4F', 'FINST-DIC66I91YS0YTNMABOZAYBSA4DDR2FJIUUBEM5F']
2025-08-15 04:31:47,834 - INFO - 批量插入完成，共 40 条记录
2025-08-15 04:31:47,834 - INFO - 日期 2025-08-14 处理完成 - 更新: 0 条，插入: 40 条，错误: 0 条
2025-08-15 04:31:47,834 - INFO - 数据同步完成！更新: 0 条，插入: 40 条，错误: 0 条
2025-08-15 04:31:47,834 - INFO - 同步完成
2025-08-15 07:30:33,729 - INFO - 使用默认增量同步（当天更新数据）
2025-08-15 07:30:33,729 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-15 07:30:33,729 - INFO - 查询参数: ('2025-08-15',)
2025-08-15 07:30:33,900 - INFO - MySQL查询成功，增量数据（日期: 2025-08-15），共获取 4 条记录
2025-08-15 07:30:33,900 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 07:30:33,900 - INFO - 开始处理日期: 2025-08-14
2025-08-15 07:30:33,900 - INFO - Request Parameters - Page 1:
2025-08-15 07:30:33,900 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 07:30:33,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 07:30:42,025 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F687C416-E433-78A9-A615-956E6B8EE75A Response: {'code': 'ServiceUnavailable', 'requestid': 'F687C416-E433-78A9-A615-956E6B8EE75A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F687C416-E433-78A9-A615-956E6B8EE75A)
2025-08-15 07:30:42,025 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 07:31:42,036 - INFO - 开始同步昨天与今天的销售数据: 2025-08-14 至 2025-08-15
2025-08-15 07:31:42,036 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-15 07:31:42,036 - INFO - 查询参数: ('2025-08-14', '2025-08-15')
2025-08-15 07:31:42,208 - INFO - MySQL查询成功，时间段: 2025-08-14 至 2025-08-15，共获取 99 条记录
2025-08-15 07:31:42,208 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 07:31:42,208 - INFO - 开始处理日期: 2025-08-14
2025-08-15 07:31:42,208 - INFO - Request Parameters - Page 1:
2025-08-15 07:31:42,208 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 07:31:42,208 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 07:31:50,317 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FA42B0B6-A3A0-7DC8-99DB-8DB84AEA9D2A Response: {'code': 'ServiceUnavailable', 'requestid': 'FA42B0B6-A3A0-7DC8-99DB-8DB84AEA9D2A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FA42B0B6-A3A0-7DC8-99DB-8DB84AEA9D2A)
2025-08-15 07:31:50,317 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 07:31:50,317 - INFO - 同步完成
2025-08-15 10:30:33,884 - INFO - 使用默认增量同步（当天更新数据）
2025-08-15 10:30:33,884 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-15 10:30:33,884 - INFO - 查询参数: ('2025-08-15',)
2025-08-15 10:30:34,071 - INFO - MySQL查询成功，增量数据（日期: 2025-08-15），共获取 136 条记录
2025-08-15 10:30:34,071 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 10:30:34,071 - INFO - 开始处理日期: 2025-08-14
2025-08-15 10:30:34,071 - INFO - Request Parameters - Page 1:
2025-08-15 10:30:34,071 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 10:30:34,071 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 10:30:42,196 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DF534DF4-7307-744B-BBC7-D31C58B9482C Response: {'code': 'ServiceUnavailable', 'requestid': 'DF534DF4-7307-744B-BBC7-D31C58B9482C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DF534DF4-7307-744B-BBC7-D31C58B9482C)
2025-08-15 10:30:42,196 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 10:31:42,206 - INFO - 开始同步昨天与今天的销售数据: 2025-08-14 至 2025-08-15
2025-08-15 10:31:42,206 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-15 10:31:42,206 - INFO - 查询参数: ('2025-08-14', '2025-08-15')
2025-08-15 10:31:42,378 - INFO - MySQL查询成功，时间段: 2025-08-14 至 2025-08-15，共获取 483 条记录
2025-08-15 10:31:42,378 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 10:31:42,394 - INFO - 开始处理日期: 2025-08-14
2025-08-15 10:31:42,394 - INFO - Request Parameters - Page 1:
2025-08-15 10:31:42,394 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 10:31:42,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 10:31:50,503 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 762E6274-CF91-7206-822F-777373F02D75 Response: {'code': 'ServiceUnavailable', 'requestid': '762E6274-CF91-7206-822F-777373F02D75', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 762E6274-CF91-7206-822F-777373F02D75)
2025-08-15 10:31:50,503 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 10:31:50,503 - INFO - 同步完成
2025-08-15 13:30:33,695 - INFO - 使用默认增量同步（当天更新数据）
2025-08-15 13:30:33,695 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-15 13:30:33,695 - INFO - 查询参数: ('2025-08-15',)
2025-08-15 13:30:33,883 - INFO - MySQL查询成功，增量数据（日期: 2025-08-15），共获取 143 条记录
2025-08-15 13:30:33,883 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 13:30:33,883 - INFO - 开始处理日期: 2025-08-14
2025-08-15 13:30:33,883 - INFO - Request Parameters - Page 1:
2025-08-15 13:30:33,883 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 13:30:33,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 13:30:42,054 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D3F16166-E0F2-71B7-BCBE-48A43F86C2CB Response: {'code': 'ServiceUnavailable', 'requestid': 'D3F16166-E0F2-71B7-BCBE-48A43F86C2CB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D3F16166-E0F2-71B7-BCBE-48A43F86C2CB)
2025-08-15 13:30:42,054 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 13:31:42,065 - INFO - 开始同步昨天与今天的销售数据: 2025-08-14 至 2025-08-15
2025-08-15 13:31:42,065 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-15 13:31:42,065 - INFO - 查询参数: ('2025-08-14', '2025-08-15')
2025-08-15 13:31:42,237 - INFO - MySQL查询成功，时间段: 2025-08-14 至 2025-08-15，共获取 490 条记录
2025-08-15 13:31:42,237 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 13:31:42,252 - INFO - 开始处理日期: 2025-08-14
2025-08-15 13:31:42,252 - INFO - Request Parameters - Page 1:
2025-08-15 13:31:42,252 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 13:31:42,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 13:31:43,112 - INFO - Response - Page 1:
2025-08-15 13:31:43,112 - INFO - 第 1 页获取到 46 条记录
2025-08-15 13:31:43,627 - INFO - 查询完成，共获取到 46 条记录
2025-08-15 13:31:43,627 - INFO - 获取到 46 条表单数据
2025-08-15 13:31:43,627 - INFO - 当前日期 2025-08-14 有 474 条MySQL数据需要处理
2025-08-15 13:31:43,627 - INFO - 开始批量插入 428 条新记录
2025-08-15 13:31:43,846 - INFO - 批量插入响应状态码: 200
2025-08-15 13:31:43,846 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '31A513BA-0DA9-790F-9B46-1FCB8E7B6E99', 'x-acs-trace-id': 'f476c54359cd5d9294fd080c77d3fed1', 'etag': '24xNi36qvbTBt/E39y6pGkw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:31:43,846 - INFO - 批量插入响应体: {'result': ['FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMV6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMW6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMX6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMY6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMZ6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM07', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM17', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM27', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM37', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM47', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM57', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM67', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM77', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM87', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM97', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMA7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMB7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMC7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMD7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEME7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMF7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMG7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMH7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMI7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMJ7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMK7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEML7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMM7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMN7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMO7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMP7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMQ7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMR7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMS7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMT7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMU7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMV7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMW7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMX7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMY7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMZ7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM08', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM18', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM28', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM38', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM48', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM58', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM68', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM78', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM88']}
2025-08-15 13:31:43,846 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-15 13:31:43,846 - INFO - 成功插入的数据ID: ['FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMV6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMW6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMX6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMY6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMZ6', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM07', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM17', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM27', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM37', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM47', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM57', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM67', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM77', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM87', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM97', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMA7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMB7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMC7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMD7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEME7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMF7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMG7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMH7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMI7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMJ7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMK7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEML7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMM7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMN7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMO7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMP7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMQ7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMR7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMS7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMT7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMU7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMV7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMW7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMX7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMY7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEMZ7', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM08', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM18', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM28', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM38', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM48', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM58', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM68', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM78', 'FINST-SFD66081721YSPSM8H8VA5TIU2743HIZ4ECEM88']
2025-08-15 13:31:49,096 - INFO - 批量插入响应状态码: 200
2025-08-15 13:31:49,096 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '38B150C1-84D7-7872-9861-B7DF88DB1448', 'x-acs-trace-id': 'bb3cda73d3f3a76121262b985c4f8245', 'etag': '2anm/8N9sbeccChNEAveWbQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:31:49,096 - INFO - 批量插入响应体: {'result': ['FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMSL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMTL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMUL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMVL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMWL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMXL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMYL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMZL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM0M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM1M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM2M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM3M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM4M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM5M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM6M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM7M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM8M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM9M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMAM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMBM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMCM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMDM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMEM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMFM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMGM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMHM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMIM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMJM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMKM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMLM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMMM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMNM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMOM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMPM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMQM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMRM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMSM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMTM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMUM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMVM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMWM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMXM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMYM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMZM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM0N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM1N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM2N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ37K35ECEM3N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ37K35ECEM4N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ37K35ECEM5N']}
2025-08-15 13:31:49,096 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-15 13:31:49,096 - INFO - 成功插入的数据ID: ['FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMSL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMTL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMUL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMVL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMWL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMXL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMYL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMZL', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM0M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM1M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM2M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM3M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM4M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM5M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM6M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM7M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM8M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM9M', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMAM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMBM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMCM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMDM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMEM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMFM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMGM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMHM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMIM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMJM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMKM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMLM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMMM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMNM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMOM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMPM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMQM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMRM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMSM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMTM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMUM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMVM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMWM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMXM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMYM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEMZM', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM0N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM1N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ36K35ECEM2N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ37K35ECEM3N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ37K35ECEM4N', 'FINST-WU766AA19R0Y4LKY7LVOQ6HAYSSQ37K35ECEM5N']
2025-08-15 13:31:54,361 - INFO - 批量插入响应状态码: 200
2025-08-15 13:31:54,361 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:31:54 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6E3D7FA1-5FF5-7837-BF5D-693519026A9A', 'x-acs-trace-id': 'd6225b085705f07445cb2a9fdf25e0cc', 'etag': '2gz4TGoISXluhoboiNiEu3A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:31:54,361 - INFO - 批量插入响应体: {'result': ['FINST-CZD66191321YOEWW8X1A4AV93CZB3YL75ECEM9P', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMAP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMBP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMCP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMDP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMEP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMFP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMGP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMHP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMIP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMJP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMKP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMLP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMMP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMNP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMOP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMPP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMQP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMRP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMSP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMTP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMUP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMVP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMWP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMXP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMYP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMZP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM0Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM1Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM2Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM3Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM4Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM5Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM6Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM7Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM8Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM9Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMAQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMBQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMCQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMDQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMEQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMFQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMGQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMHQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMIQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMJQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMKQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMLQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMMQ']}
2025-08-15 13:31:54,361 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-15 13:31:54,361 - INFO - 成功插入的数据ID: ['FINST-CZD66191321YOEWW8X1A4AV93CZB3YL75ECEM9P', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMAP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMBP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMCP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMDP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMEP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMFP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMGP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMHP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMIP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMJP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMKP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMLP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMMP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMNP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMOP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMPP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMQP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMRP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMSP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMTP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMUP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMVP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMWP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMXP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMYP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMZP', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM0Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM1Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM2Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM3Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM4Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM5Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM6Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM7Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM8Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEM9Q', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMAQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMBQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMCQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMDQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMEQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMFQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMGQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMHQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMIQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMJQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMKQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMLQ', 'FINST-CZD66191321YOEWW8X1A4AV93CZB3ZL75ECEMMQ']
2025-08-15 13:31:59,627 - INFO - 批量插入响应状态码: 200
2025-08-15 13:31:59,627 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6CF254AB-5327-7F08-8012-098789A80F0B', 'x-acs-trace-id': '02e0724f91a6b5fb278fdaf72e064abd', 'etag': '2S6RJolcDz7syHbiZXo4GfQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:31:59,627 - INFO - 批量插入响应体: {'result': ['FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM2G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM3G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM4G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM5G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM6G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM7G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM8G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM9G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMAG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMBG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMCG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMDG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMEG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMFG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMGG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMHG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMIG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMJG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMKG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMLG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMMG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMNG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMOG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMPG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMQG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMRG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMSG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMTG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMUG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMVG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMWG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMXG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMYG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMZG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM0H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM1H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM2H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM3H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM4H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM5H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM6H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM7H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM8H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM9H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMAH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMBH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMCH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMDH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMEH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMFH']}
2025-08-15 13:31:59,627 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-08-15 13:31:59,627 - INFO - 成功插入的数据ID: ['FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM2G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM3G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM4G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM5G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM6G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM7G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM8G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM9G', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMAG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMBG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMCG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMDG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMEG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMFG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMGG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMHG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMIG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMJG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMKG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMLG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMMG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMNG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMOG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMPG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMQG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMRG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMSG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMTG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMUG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMVG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMWG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMXG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMYG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMZG', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM0H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM1H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM2H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM3H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM4H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM5H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM6H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM7H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM8H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEM9H', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMAH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMBH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMCH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMDH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMEH', 'FINST-ZP966U81ZGYXYLQ073JX06ILS1ZR2SOB5ECEMFH']
2025-08-15 13:32:04,877 - INFO - 批量插入响应状态码: 200
2025-08-15 13:32:04,877 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '545E60C3-7015-746C-B5F1-7788F50FAC20', 'x-acs-trace-id': '9b933e984e733e3f0dba9e1398d1a190', 'etag': '2OOwqZGXuk71lSSNTvaSm6A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:32:04,877 - INFO - 批量插入响应体: {'result': ['FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMIY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMJY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMKY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMLY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMMY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMNY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMOY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMPY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMQY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMRY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMSY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMTY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMUY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMVY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMWY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMXY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMYY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMZY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM0Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM1Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM2Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM3Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM4Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM5Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM6Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM7Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM8Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM9Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMAZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMBZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMCZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMDZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMEZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMFZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMGZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMHZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMIZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMJZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMKZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMLZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMMZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMNZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMOZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMPZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMQZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMRZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMSZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMTZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMUZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMVZ']}
2025-08-15 13:32:04,877 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-08-15 13:32:04,877 - INFO - 成功插入的数据ID: ['FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMIY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMJY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMKY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMLY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMMY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMNY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMOY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMPY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMQY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMRY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMSY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMTY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMUY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMVY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMWY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMXY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMYY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMZY', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM0Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM1Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM2Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM3Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEM4Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM5Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM6Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM7Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM8Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEM9Z', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMAZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMBZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMCZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMDZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMEZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMFZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMGZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMHZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMIZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMJZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMKZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMLZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMMZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMNZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMOZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMPZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMQZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMRZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMSZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMTZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMUZ', 'FINST-07E66I91YOZXK7EW5TI5ABJVE0C73GQF5ECEMVZ']
2025-08-15 13:32:10,127 - INFO - 批量插入响应状态码: 200
2025-08-15 13:32:10,127 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:32:10 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1F65C851-5F2B-7857-A534-3FF4F4D9BFEB', 'x-acs-trace-id': '552bcfad17c11e63dcfcbea69af8126b', 'etag': '2nWVWsqdS1VrcjdwE2SSbvw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:32:10,127 - INFO - 批量插入响应体: {'result': ['FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM2B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM3B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM4B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM5B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM6B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM7B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM8B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM9B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMAB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMBB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMCB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMDB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMEB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMFB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMGB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMHB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMIB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMJB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMKB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMLB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMMB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMNB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMOB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMPB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMQB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMRB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMSB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMTB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMUB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMVB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMWB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMXB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMYB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMZB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM0C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM1C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM2C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM3C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM4C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM5C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM6C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM7C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM8C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM9C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMAC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMBC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMCC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMDC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMEC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMFC']}
2025-08-15 13:32:10,127 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-08-15 13:32:10,127 - INFO - 成功插入的数据ID: ['FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM2B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM3B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM4B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM5B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM6B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM7B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM8B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM9B', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMAB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMBB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMCB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMDB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMEB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMFB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMGB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMHB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMIB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMJB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMKB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMLB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMMB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMNB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMOB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMPB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMQB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMRB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMSB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMTB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMUB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMVB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMWB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMXB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMYB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMZB', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM0C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM1C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM2C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM3C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM4C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM5C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM6C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM7C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM8C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEM9C', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMAC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMBC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMCC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMDC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMEC', 'FINST-NWE664C19P0YRELMEAK3V7NNH9UT36SJ5ECEMFC']
2025-08-15 13:32:15,377 - INFO - 批量插入响应状态码: 200
2025-08-15 13:32:15,377 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:32:15 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CB4E8550-65AD-785A-A95F-CA185F8F2653', 'x-acs-trace-id': 'a1846388fd5c230e76f4354ff44e4349', 'etag': '2OePHorh48Bec7rKUeTvLGA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:32:15,377 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2XTN5ECEM2M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2XTN5ECEM3M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM4M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM5M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM6M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM7M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM8M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM9M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMAM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMBM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMCM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMDM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMEM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMFM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMGM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMHM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMIM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMJM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMKM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMLM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMMM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMNM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMOM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMPM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMQM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMRM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMSM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMTM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMUM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMVM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMWM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMXM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMYM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMZM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM0N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM1N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM2N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM3N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM4N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM5N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM6N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM7N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM8N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM9N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMAN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMBN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMCN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMDN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMEN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMFN']}
2025-08-15 13:32:15,377 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-08-15 13:32:15,377 - INFO - 成功插入的数据ID: ['FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2XTN5ECEM2M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2XTN5ECEM3M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM4M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM5M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM6M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM7M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM8M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM9M', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMAM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMBM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMCM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMDM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMEM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMFM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMGM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMHM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMIM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMJM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMKM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMLM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMMM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMNM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMOM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMPM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMQM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMRM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMSM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMTM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMUM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMVM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMWM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMXM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMYM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMZM', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM0N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM1N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM2N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM3N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM4N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM5N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM6N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM7N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM8N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEM9N', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMAN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMBN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMCN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMDN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMEN', 'FINST-2FD66I71FQZX8HKS60GW47OE2QLQ2YTN5ECEMFN']
2025-08-15 13:32:20,643 - INFO - 批量插入响应状态码: 200
2025-08-15 13:32:20,643 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:32:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2462', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A3E7E177-4761-7A23-9B96-A42E0EA858C4', 'x-acs-trace-id': '023e576995bb0e2718ce3cb952396062', 'etag': '2t7zcS1poH74Fgw28n49Vyg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:32:20,643 - INFO - 批量插入响应体: {'result': ['FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMCG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMDG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMEG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMFG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMGG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMHG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMIG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMJG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMKG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMLG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMMG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMNG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMOG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMPG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMQG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMRG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMSG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMTG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMUG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMVG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMWG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMXG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMYG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMZG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM0H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM1H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM2H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM3H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM4H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM5H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM6H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM7H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM8H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM9H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMAH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMBH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMCH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMDH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMEH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMFH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMGH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMHH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMIH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMJH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMKH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMLH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMMH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMNH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMOH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMPH1']}
2025-08-15 13:32:20,643 - INFO - 批量插入表单数据成功，批次 8，共 50 条记录
2025-08-15 13:32:20,643 - INFO - 成功插入的数据ID: ['FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMCG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMDG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMEG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMFG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMGG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMHG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMIG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMJG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMKG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMLG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMMG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMNG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMOG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMPG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMQG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMRG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMSG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMTG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMUG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMVG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMWG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMXG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMYG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMZG1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM0H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM1H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM2H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM3H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM4H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM5H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM6H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM7H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM8H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEM9H1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMAH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMBH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMCH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMDH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMEH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMFH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMGH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMHH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMIH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMJH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMKH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMLH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMMH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMNH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMOH1', 'FINST-MKF66PA1JVUXPWVOEU35L4BB2I5K38WR5ECEMPH1']
2025-08-15 13:32:25,893 - INFO - 批量插入响应状态码: 200
2025-08-15 13:32:25,893 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 05:32:26 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1356', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E0B12F0C-0257-7AF4-BC02-229180218123', 'x-acs-trace-id': '9da3eb9ec3066e5dcda8afd9a57aac95', 'etag': '1zL1abzjRYMnhmnuLCOIvgQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 13:32:25,893 - INFO - 批量插入响应体: {'result': ['FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEM6J', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEM7J', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEM8J', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEM9J', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMAJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMBJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMCJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMDJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMEJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMFJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMGJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMHJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMIJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMJJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMKJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMLJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMMJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMNJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMOJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMPJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMQJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMRJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMSJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMTJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMUJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMVJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMWJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMXJ']}
2025-08-15 13:32:25,893 - INFO - 批量插入表单数据成功，批次 9，共 28 条记录
2025-08-15 13:32:25,893 - INFO - 成功插入的数据ID: ['FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEM6J', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEM7J', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEM8J', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEM9J', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMAJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMBJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMCJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMDJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMEJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMFJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMGJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMHJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMIJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMJJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMKJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMLJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMMJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMNJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMOJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMPJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMQJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMRJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMSJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMTJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMUJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMVJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMWJ', 'FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMXJ']
2025-08-15 13:32:30,908 - INFO - 批量插入完成，共 428 条记录
2025-08-15 13:32:30,908 - INFO - 日期 2025-08-14 处理完成 - 更新: 0 条，插入: 428 条，错误: 0 条
2025-08-15 13:32:30,908 - INFO - 数据同步完成！更新: 0 条，插入: 428 条，错误: 0 条
2025-08-15 13:32:30,908 - INFO - 同步完成
2025-08-15 16:30:33,854 - INFO - 使用默认增量同步（当天更新数据）
2025-08-15 16:30:33,854 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-15 16:30:33,854 - INFO - 查询参数: ('2025-08-15',)
2025-08-15 16:30:34,026 - INFO - MySQL查询成功，增量数据（日期: 2025-08-15），共获取 144 条记录
2025-08-15 16:30:34,026 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 16:30:34,026 - INFO - 开始处理日期: 2025-08-14
2025-08-15 16:30:34,042 - INFO - Request Parameters - Page 1:
2025-08-15 16:30:34,042 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 16:30:34,042 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 16:30:42,146 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: DC4EF60F-8F35-7953-BEE5-093E84A74589 Response: {'code': 'ServiceUnavailable', 'requestid': 'DC4EF60F-8F35-7953-BEE5-093E84A74589', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: DC4EF60F-8F35-7953-BEE5-093E84A74589)
2025-08-15 16:30:42,146 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 16:31:42,157 - INFO - 开始同步昨天与今天的销售数据: 2025-08-14 至 2025-08-15
2025-08-15 16:31:42,157 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-15 16:31:42,157 - INFO - 查询参数: ('2025-08-14', '2025-08-15')
2025-08-15 16:31:42,344 - INFO - MySQL查询成功，时间段: 2025-08-14 至 2025-08-15，共获取 491 条记录
2025-08-15 16:31:42,344 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 16:31:42,360 - INFO - 开始处理日期: 2025-08-14
2025-08-15 16:31:42,360 - INFO - Request Parameters - Page 1:
2025-08-15 16:31:42,360 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 16:31:42,360 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 16:31:43,297 - INFO - Response - Page 1:
2025-08-15 16:31:43,297 - INFO - 第 1 页获取到 50 条记录
2025-08-15 16:31:43,813 - INFO - Request Parameters - Page 2:
2025-08-15 16:31:43,813 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 16:31:43,813 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 16:31:44,579 - INFO - Response - Page 2:
2025-08-15 16:31:44,579 - INFO - 第 2 页获取到 50 条记录
2025-08-15 16:31:45,079 - INFO - Request Parameters - Page 3:
2025-08-15 16:31:45,079 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 16:31:45,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 16:31:53,188 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 26E896F7-87FA-7DC4-9C72-39011AA6E3D3 Response: {'code': 'ServiceUnavailable', 'requestid': '26E896F7-87FA-7DC4-9C72-39011AA6E3D3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 26E896F7-87FA-7DC4-9C72-39011AA6E3D3)
2025-08-15 16:31:53,188 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-15 16:31:53,188 - INFO - 同步完成
2025-08-15 19:30:34,139 - INFO - 使用默认增量同步（当天更新数据）
2025-08-15 19:30:34,139 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-15 19:30:34,139 - INFO - 查询参数: ('2025-08-15',)
2025-08-15 19:30:34,311 - INFO - MySQL查询成功，增量数据（日期: 2025-08-15），共获取 154 条记录
2025-08-15 19:30:34,311 - INFO - 获取到 4 个日期需要处理: ['2025-07-24', '2025-07-28', '2025-07-29', '2025-08-14']
2025-08-15 19:30:34,327 - INFO - 开始处理日期: 2025-07-24
2025-08-15 19:30:34,327 - INFO - Request Parameters - Page 1:
2025-08-15 19:30:34,327 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:34,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:42,449 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 31347AF6-F6AE-7F62-A6DA-397AFE5395B2 Response: {'code': 'ServiceUnavailable', 'requestid': '31347AF6-F6AE-7F62-A6DA-397AFE5395B2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 31347AF6-F6AE-7F62-A6DA-397AFE5395B2)
2025-08-15 19:30:42,449 - INFO - 开始处理日期: 2025-07-28
2025-08-15 19:30:42,449 - INFO - Request Parameters - Page 1:
2025-08-15 19:30:42,449 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:42,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:50,560 - ERROR - 处理日期 2025-07-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1EB4EF62-E0AD-745C-9C6F-41FA137C379A Response: {'code': 'ServiceUnavailable', 'requestid': '1EB4EF62-E0AD-745C-9C6F-41FA137C379A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1EB4EF62-E0AD-745C-9C6F-41FA137C379A)
2025-08-15 19:30:50,560 - INFO - 开始处理日期: 2025-07-29
2025-08-15 19:30:50,560 - INFO - Request Parameters - Page 1:
2025-08-15 19:30:50,560 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:50,560 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:51,325 - INFO - Response - Page 1:
2025-08-15 19:30:51,325 - INFO - 第 1 页获取到 50 条记录
2025-08-15 19:30:51,841 - INFO - Request Parameters - Page 2:
2025-08-15 19:30:51,841 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:51,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:52,607 - INFO - Response - Page 2:
2025-08-15 19:30:52,607 - INFO - 第 2 页获取到 50 条记录
2025-08-15 19:30:53,123 - INFO - Request Parameters - Page 3:
2025-08-15 19:30:53,123 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:53,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:54,045 - INFO - Response - Page 3:
2025-08-15 19:30:54,045 - INFO - 第 3 页获取到 50 条记录
2025-08-15 19:30:54,545 - INFO - Request Parameters - Page 4:
2025-08-15 19:30:54,545 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:54,545 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:55,279 - INFO - Response - Page 4:
2025-08-15 19:30:55,279 - INFO - 第 4 页获取到 50 条记录
2025-08-15 19:30:55,779 - INFO - Request Parameters - Page 5:
2025-08-15 19:30:55,779 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:55,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:56,498 - INFO - Response - Page 5:
2025-08-15 19:30:56,498 - INFO - 第 5 页获取到 50 条记录
2025-08-15 19:30:56,998 - INFO - Request Parameters - Page 6:
2025-08-15 19:30:56,998 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:56,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:57,780 - INFO - Response - Page 6:
2025-08-15 19:30:57,780 - INFO - 第 6 页获取到 50 条记录
2025-08-15 19:30:58,296 - INFO - Request Parameters - Page 7:
2025-08-15 19:30:58,296 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:58,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:30:59,061 - INFO - Response - Page 7:
2025-08-15 19:30:59,061 - INFO - 第 7 页获取到 50 条记录
2025-08-15 19:30:59,577 - INFO - Request Parameters - Page 8:
2025-08-15 19:30:59,577 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:30:59,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:00,343 - INFO - Response - Page 8:
2025-08-15 19:31:00,343 - INFO - 第 8 页获取到 50 条记录
2025-08-15 19:31:00,859 - INFO - Request Parameters - Page 9:
2025-08-15 19:31:00,859 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:00,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:01,640 - INFO - Response - Page 9:
2025-08-15 19:31:01,640 - INFO - 第 9 页获取到 50 条记录
2025-08-15 19:31:02,156 - INFO - Request Parameters - Page 10:
2025-08-15 19:31:02,156 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:02,156 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:02,953 - INFO - Response - Page 10:
2025-08-15 19:31:02,953 - INFO - 第 10 页获取到 50 条记录
2025-08-15 19:31:03,469 - INFO - Request Parameters - Page 11:
2025-08-15 19:31:03,469 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:03,469 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:04,250 - INFO - Response - Page 11:
2025-08-15 19:31:04,250 - INFO - 第 11 页获取到 50 条记录
2025-08-15 19:31:04,766 - INFO - Request Parameters - Page 12:
2025-08-15 19:31:04,766 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:04,766 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:05,422 - INFO - Response - Page 12:
2025-08-15 19:31:05,422 - INFO - 第 12 页获取到 28 条记录
2025-08-15 19:31:05,922 - INFO - 查询完成，共获取到 578 条记录
2025-08-15 19:31:05,922 - INFO - 获取到 578 条表单数据
2025-08-15 19:31:05,922 - INFO - 当前日期 2025-07-29 有 2 条MySQL数据需要处理
2025-08-15 19:31:05,922 - INFO - 开始更新记录 - 表单实例ID: FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMLH
2025-08-15 19:31:06,516 - INFO - 更新表单数据成功: FINST-MRA66WC1HAKXWDW9AM8SI5K4ME2G33SLJPPDMLH
2025-08-15 19:31:06,516 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 0.0, 'new_value': 1000.0}, {'field': 'total_amount', 'old_value': 7239.0, 'new_value': 8239.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-15 19:31:06,516 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMI
2025-08-15 19:31:07,047 - INFO - 更新表单数据成功: FINST-V7966QC1XBLXNAB46S253BTWT0QE2ZTPJPPDMI
2025-08-15 19:31:07,047 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1000.0}, {'field': 'total_amount', 'old_value': 9003.82, 'new_value': 10003.82}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-15 19:31:07,047 - INFO - 日期 2025-07-29 处理完成 - 更新: 2 条，插入: 0 条，错误: 0 条
2025-08-15 19:31:07,047 - INFO - 开始处理日期: 2025-08-14
2025-08-15 19:31:07,047 - INFO - Request Parameters - Page 1:
2025-08-15 19:31:07,047 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:07,047 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:07,829 - INFO - Response - Page 1:
2025-08-15 19:31:07,829 - INFO - 第 1 页获取到 50 条记录
2025-08-15 19:31:08,329 - INFO - Request Parameters - Page 2:
2025-08-15 19:31:08,329 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:08,329 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:09,095 - INFO - Response - Page 2:
2025-08-15 19:31:09,095 - INFO - 第 2 页获取到 50 条记录
2025-08-15 19:31:09,610 - INFO - Request Parameters - Page 3:
2025-08-15 19:31:09,610 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:09,610 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:10,298 - INFO - Response - Page 3:
2025-08-15 19:31:10,298 - INFO - 第 3 页获取到 50 条记录
2025-08-15 19:31:10,814 - INFO - Request Parameters - Page 4:
2025-08-15 19:31:10,814 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:10,814 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:11,611 - INFO - Response - Page 4:
2025-08-15 19:31:11,611 - INFO - 第 4 页获取到 50 条记录
2025-08-15 19:31:12,126 - INFO - Request Parameters - Page 5:
2025-08-15 19:31:12,126 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:12,126 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:12,877 - INFO - Response - Page 5:
2025-08-15 19:31:12,877 - INFO - 第 5 页获取到 50 条记录
2025-08-15 19:31:13,392 - INFO - Request Parameters - Page 6:
2025-08-15 19:31:13,392 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:13,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:14,174 - INFO - Response - Page 6:
2025-08-15 19:31:14,174 - INFO - 第 6 页获取到 50 条记录
2025-08-15 19:31:14,690 - INFO - Request Parameters - Page 7:
2025-08-15 19:31:14,690 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:14,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:15,424 - INFO - Response - Page 7:
2025-08-15 19:31:15,424 - INFO - 第 7 页获取到 50 条记录
2025-08-15 19:31:15,940 - INFO - Request Parameters - Page 8:
2025-08-15 19:31:15,940 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:15,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:16,690 - INFO - Response - Page 8:
2025-08-15 19:31:16,690 - INFO - 第 8 页获取到 50 条记录
2025-08-15 19:31:17,206 - INFO - Request Parameters - Page 9:
2025-08-15 19:31:17,206 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:17,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:17,925 - INFO - Response - Page 9:
2025-08-15 19:31:17,925 - INFO - 第 9 页获取到 50 条记录
2025-08-15 19:31:18,440 - INFO - Request Parameters - Page 10:
2025-08-15 19:31:18,440 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:31:18,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:31:19,128 - INFO - Response - Page 10:
2025-08-15 19:31:19,128 - INFO - 第 10 页获取到 24 条记录
2025-08-15 19:31:19,644 - INFO - 查询完成，共获取到 474 条记录
2025-08-15 19:31:19,644 - INFO - 获取到 474 条表单数据
2025-08-15 19:31:19,644 - INFO - 当前日期 2025-08-14 有 144 条MySQL数据需要处理
2025-08-15 19:31:19,644 - INFO - 开始更新记录 - 表单实例ID: FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMYY
2025-08-15 19:31:20,269 - INFO - 更新表单数据成功: FINST-07E66I91YOZXK7EW5TI5ABJVE0C73FQF5ECEMYY
2025-08-15 19:31:20,269 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 1337.6, 'new_value': 0.0}, {'field': 'offline_amount', 'old_value': 35942.5, 'new_value': 18562.0}, {'field': 'total_amount', 'old_value': 37280.1, 'new_value': 18562.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-15 19:31:20,269 - INFO - 开始更新记录 - 表单实例ID: FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMOJ
2025-08-15 19:31:20,847 - INFO - 更新表单数据成功: FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMOJ
2025-08-15 19:31:20,847 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8623.0, 'new_value': 8356.0}, {'field': 'total_amount', 'old_value': 8623.0, 'new_value': 8356.0}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/b9ab6a7d39864ee899e7d513929cf7d0.png?Expires=2070521113&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=7fisK5Vp17TXjdm2s5sc%2FOVv4gE%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/5c4fde8dcd8c46f881e20ca37df79dcb.jpg?Expires=2070523207&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=%2BbT1fM0IKNI%2F5mbzPXVRQdr8QgQ%3D'}]
2025-08-15 19:31:20,847 - INFO - 开始更新记录 - 表单实例ID: FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMHJ
2025-08-15 19:31:21,410 - INFO - 更新表单数据成功: FINST-ORA66F81EP0YLU9AEF1W2632N7J135YV5ECEMHJ
2025-08-15 19:31:21,410 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3269.72}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3269.72}, {'field': 'order_count', 'old_value': 0, 'new_value': 24}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/cf062a6b5fff4b8eae0c617abb80f078.png?Expires=2070523207&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=iE5jWy%2FBUnpASWeVt92Z5Hqya7U%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/91a88a754a25437cbb4a9465fd72da8f.jpg?Expires=2070523207&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=OM4HqzCDcvQMs2R%2FANQIa3746s4%3D'}]
2025-08-15 19:31:21,410 - INFO - 开始批量插入 3 条新记录
2025-08-15 19:31:21,582 - INFO - 批量插入响应状态码: 200
2025-08-15 19:31:21,582 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 11:31:21 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5374F601-C904-7B66-B684-552234AB732A', 'x-acs-trace-id': '456ccb4ba9017940c8e97a58869b4588', 'etag': '1vxXDty+KBZ7i46G6VTsL3g3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 19:31:21,582 - INFO - 批量插入响应体: {'result': ['FINST-PAB66N719Y1Y9L5PCRKPX6CHHEAE26QGZQCEMC', 'FINST-PAB66N719Y1Y9L5PCRKPX6CHHEAE26QGZQCEMD', 'FINST-PAB66N719Y1Y9L5PCRKPX6CHHEAE26QGZQCEME']}
2025-08-15 19:31:21,582 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-15 19:31:21,582 - INFO - 成功插入的数据ID: ['FINST-PAB66N719Y1Y9L5PCRKPX6CHHEAE26QGZQCEMC', 'FINST-PAB66N719Y1Y9L5PCRKPX6CHHEAE26QGZQCEMD', 'FINST-PAB66N719Y1Y9L5PCRKPX6CHHEAE26QGZQCEME']
2025-08-15 19:31:26,598 - INFO - 批量插入完成，共 3 条记录
2025-08-15 19:31:26,598 - INFO - 日期 2025-08-14 处理完成 - 更新: 3 条，插入: 3 条，错误: 0 条
2025-08-15 19:31:26,598 - INFO - 数据同步完成！更新: 5 条，插入: 3 条，错误: 2 条
2025-08-15 19:32:26,621 - INFO - 开始同步昨天与今天的销售数据: 2025-08-14 至 2025-08-15
2025-08-15 19:32:26,621 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-15 19:32:26,621 - INFO - 查询参数: ('2025-08-14', '2025-08-15')
2025-08-15 19:32:26,793 - INFO - MySQL查询成功，时间段: 2025-08-14 至 2025-08-15，共获取 494 条记录
2025-08-15 19:32:26,793 - INFO - 获取到 1 个日期需要处理: ['2025-08-14']
2025-08-15 19:32:26,809 - INFO - 开始处理日期: 2025-08-14
2025-08-15 19:32:26,809 - INFO - Request Parameters - Page 1:
2025-08-15 19:32:26,809 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:26,809 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:27,606 - INFO - Response - Page 1:
2025-08-15 19:32:27,606 - INFO - 第 1 页获取到 50 条记录
2025-08-15 19:32:28,121 - INFO - Request Parameters - Page 2:
2025-08-15 19:32:28,121 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:28,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:28,840 - INFO - Response - Page 2:
2025-08-15 19:32:28,840 - INFO - 第 2 页获取到 50 条记录
2025-08-15 19:32:29,340 - INFO - Request Parameters - Page 3:
2025-08-15 19:32:29,340 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:29,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:30,044 - INFO - Response - Page 3:
2025-08-15 19:32:30,044 - INFO - 第 3 页获取到 50 条记录
2025-08-15 19:32:30,559 - INFO - Request Parameters - Page 4:
2025-08-15 19:32:30,559 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:30,559 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:31,341 - INFO - Response - Page 4:
2025-08-15 19:32:31,341 - INFO - 第 4 页获取到 50 条记录
2025-08-15 19:32:31,856 - INFO - Request Parameters - Page 5:
2025-08-15 19:32:31,856 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:31,856 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:32,685 - INFO - Response - Page 5:
2025-08-15 19:32:32,700 - INFO - 第 5 页获取到 50 条记录
2025-08-15 19:32:33,216 - INFO - Request Parameters - Page 6:
2025-08-15 19:32:33,216 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:33,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:34,154 - INFO - Response - Page 6:
2025-08-15 19:32:34,169 - INFO - 第 6 页获取到 50 条记录
2025-08-15 19:32:34,685 - INFO - Request Parameters - Page 7:
2025-08-15 19:32:34,685 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:34,685 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:35,482 - INFO - Response - Page 7:
2025-08-15 19:32:35,482 - INFO - 第 7 页获取到 50 条记录
2025-08-15 19:32:35,998 - INFO - Request Parameters - Page 8:
2025-08-15 19:32:35,998 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:35,998 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:36,779 - INFO - Response - Page 8:
2025-08-15 19:32:36,779 - INFO - 第 8 页获取到 50 条记录
2025-08-15 19:32:37,279 - INFO - Request Parameters - Page 9:
2025-08-15 19:32:37,279 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:37,279 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:38,061 - INFO - Response - Page 9:
2025-08-15 19:32:38,061 - INFO - 第 9 页获取到 50 条记录
2025-08-15 19:32:38,577 - INFO - Request Parameters - Page 10:
2025-08-15 19:32:38,577 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 19:32:38,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 19:32:39,217 - INFO - Response - Page 10:
2025-08-15 19:32:39,217 - INFO - 第 10 页获取到 27 条记录
2025-08-15 19:32:39,733 - INFO - 查询完成，共获取到 477 条记录
2025-08-15 19:32:39,733 - INFO - 获取到 477 条表单数据
2025-08-15 19:32:39,733 - INFO - 当前日期 2025-08-14 有 477 条MySQL数据需要处理
2025-08-15 19:32:39,749 - INFO - 日期 2025-08-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-15 19:32:39,749 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-15 19:32:39,749 - INFO - 同步完成
2025-08-15 22:30:34,105 - INFO - 使用默认增量同步（当天更新数据）
2025-08-15 22:30:34,105 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-15 22:30:34,105 - INFO - 查询参数: ('2025-08-15',)
2025-08-15 22:30:34,293 - INFO - MySQL查询成功，增量数据（日期: 2025-08-15），共获取 196 条记录
2025-08-15 22:30:34,293 - INFO - 获取到 5 个日期需要处理: ['2025-07-24', '2025-07-28', '2025-07-29', '2025-08-14', '2025-08-15']
2025-08-15 22:30:34,293 - INFO - 开始处理日期: 2025-07-24
2025-08-15 22:30:34,293 - INFO - Request Parameters - Page 1:
2025-08-15 22:30:34,293 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:30:34,293 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753286400000, 1753372799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:30:42,419 - ERROR - 处理日期 2025-07-24 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 375AEEC7-5459-753F-AA2C-30F8D4D36406 Response: {'code': 'ServiceUnavailable', 'requestid': '375AEEC7-5459-753F-AA2C-30F8D4D36406', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 375AEEC7-5459-753F-AA2C-30F8D4D36406)
2025-08-15 22:30:42,419 - INFO - 开始处理日期: 2025-07-28
2025-08-15 22:30:42,419 - INFO - Request Parameters - Page 1:
2025-08-15 22:30:42,419 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:30:42,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:30:43,216 - INFO - Response - Page 1:
2025-08-15 22:30:43,216 - INFO - 第 1 页获取到 50 条记录
2025-08-15 22:30:43,716 - INFO - Request Parameters - Page 2:
2025-08-15 22:30:43,716 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:30:43,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:30:44,498 - INFO - Response - Page 2:
2025-08-15 22:30:44,498 - INFO - 第 2 页获取到 50 条记录
2025-08-15 22:30:45,014 - INFO - Request Parameters - Page 3:
2025-08-15 22:30:45,014 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:30:45,014 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753632000000, 1753718399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:30:53,135 - ERROR - 处理日期 2025-07-28 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D7F3F390-A319-7930-A51F-27BB6902A486 Response: {'code': 'ServiceUnavailable', 'requestid': 'D7F3F390-A319-7930-A51F-27BB6902A486', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D7F3F390-A319-7930-A51F-27BB6902A486)
2025-08-15 22:30:53,135 - INFO - 开始处理日期: 2025-07-29
2025-08-15 22:30:53,135 - INFO - Request Parameters - Page 1:
2025-08-15 22:30:53,135 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:30:53,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:30:58,340 - INFO - Response - Page 1:
2025-08-15 22:30:58,340 - INFO - 第 1 页获取到 50 条记录
2025-08-15 22:30:58,855 - INFO - Request Parameters - Page 2:
2025-08-15 22:30:58,855 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:30:58,855 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:30:59,668 - INFO - Response - Page 2:
2025-08-15 22:30:59,668 - INFO - 第 2 页获取到 50 条记录
2025-08-15 22:31:00,184 - INFO - Request Parameters - Page 3:
2025-08-15 22:31:00,184 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:00,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:00,981 - INFO - Response - Page 3:
2025-08-15 22:31:00,981 - INFO - 第 3 页获取到 50 条记录
2025-08-15 22:31:01,481 - INFO - Request Parameters - Page 4:
2025-08-15 22:31:01,481 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:01,481 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:02,168 - INFO - Response - Page 4:
2025-08-15 22:31:02,168 - INFO - 第 4 页获取到 50 条记录
2025-08-15 22:31:02,684 - INFO - Request Parameters - Page 5:
2025-08-15 22:31:02,684 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:02,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:03,450 - INFO - Response - Page 5:
2025-08-15 22:31:03,450 - INFO - 第 5 页获取到 50 条记录
2025-08-15 22:31:03,950 - INFO - Request Parameters - Page 6:
2025-08-15 22:31:03,950 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:03,950 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:04,700 - INFO - Response - Page 6:
2025-08-15 22:31:04,700 - INFO - 第 6 页获取到 50 条记录
2025-08-15 22:31:05,216 - INFO - Request Parameters - Page 7:
2025-08-15 22:31:05,216 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:05,216 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:05,919 - INFO - Response - Page 7:
2025-08-15 22:31:05,919 - INFO - 第 7 页获取到 50 条记录
2025-08-15 22:31:06,419 - INFO - Request Parameters - Page 8:
2025-08-15 22:31:06,419 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:06,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:07,185 - INFO - Response - Page 8:
2025-08-15 22:31:07,185 - INFO - 第 8 页获取到 50 条记录
2025-08-15 22:31:07,701 - INFO - Request Parameters - Page 9:
2025-08-15 22:31:07,701 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:07,701 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:08,435 - INFO - Response - Page 9:
2025-08-15 22:31:08,435 - INFO - 第 9 页获取到 50 条记录
2025-08-15 22:31:08,951 - INFO - Request Parameters - Page 10:
2025-08-15 22:31:08,951 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:08,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:09,686 - INFO - Response - Page 10:
2025-08-15 22:31:09,686 - INFO - 第 10 页获取到 50 条记录
2025-08-15 22:31:10,201 - INFO - Request Parameters - Page 11:
2025-08-15 22:31:10,201 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:10,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:10,967 - INFO - Response - Page 11:
2025-08-15 22:31:10,967 - INFO - 第 11 页获取到 50 条记录
2025-08-15 22:31:11,483 - INFO - Request Parameters - Page 12:
2025-08-15 22:31:11,483 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:11,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 12, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753718400000, 1753804799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:12,108 - INFO - Response - Page 12:
2025-08-15 22:31:12,108 - INFO - 第 12 页获取到 28 条记录
2025-08-15 22:31:12,624 - INFO - 查询完成，共获取到 578 条记录
2025-08-15 22:31:12,624 - INFO - 获取到 578 条表单数据
2025-08-15 22:31:12,624 - INFO - 当前日期 2025-07-29 有 2 条MySQL数据需要处理
2025-08-15 22:31:12,624 - INFO - 日期 2025-07-29 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-15 22:31:12,624 - INFO - 开始处理日期: 2025-08-14
2025-08-15 22:31:12,624 - INFO - Request Parameters - Page 1:
2025-08-15 22:31:12,624 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:12,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:13,374 - INFO - Response - Page 1:
2025-08-15 22:31:13,374 - INFO - 第 1 页获取到 50 条记录
2025-08-15 22:31:13,874 - INFO - Request Parameters - Page 2:
2025-08-15 22:31:13,874 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:13,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:14,640 - INFO - Response - Page 2:
2025-08-15 22:31:14,640 - INFO - 第 2 页获取到 50 条记录
2025-08-15 22:31:15,155 - INFO - Request Parameters - Page 3:
2025-08-15 22:31:15,155 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:15,155 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:15,890 - INFO - Response - Page 3:
2025-08-15 22:31:15,890 - INFO - 第 3 页获取到 50 条记录
2025-08-15 22:31:16,406 - INFO - Request Parameters - Page 4:
2025-08-15 22:31:16,406 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:16,406 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:17,171 - INFO - Response - Page 4:
2025-08-15 22:31:17,171 - INFO - 第 4 页获取到 50 条记录
2025-08-15 22:31:17,687 - INFO - Request Parameters - Page 5:
2025-08-15 22:31:17,687 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:17,687 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:18,391 - INFO - Response - Page 5:
2025-08-15 22:31:18,391 - INFO - 第 5 页获取到 50 条记录
2025-08-15 22:31:18,891 - INFO - Request Parameters - Page 6:
2025-08-15 22:31:18,891 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:18,891 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:19,578 - INFO - Response - Page 6:
2025-08-15 22:31:19,578 - INFO - 第 6 页获取到 50 条记录
2025-08-15 22:31:20,078 - INFO - Request Parameters - Page 7:
2025-08-15 22:31:20,078 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:20,078 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:20,828 - INFO - Response - Page 7:
2025-08-15 22:31:20,828 - INFO - 第 7 页获取到 50 条记录
2025-08-15 22:31:21,344 - INFO - Request Parameters - Page 8:
2025-08-15 22:31:21,344 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:21,344 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:22,001 - INFO - Response - Page 8:
2025-08-15 22:31:22,001 - INFO - 第 8 页获取到 50 条记录
2025-08-15 22:31:22,501 - INFO - Request Parameters - Page 9:
2025-08-15 22:31:22,501 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:22,501 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:23,251 - INFO - Response - Page 9:
2025-08-15 22:31:23,251 - INFO - 第 9 页获取到 50 条记录
2025-08-15 22:31:23,767 - INFO - Request Parameters - Page 10:
2025-08-15 22:31:23,767 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:23,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:24,485 - INFO - Response - Page 10:
2025-08-15 22:31:24,485 - INFO - 第 10 页获取到 27 条记录
2025-08-15 22:31:25,001 - INFO - 查询完成，共获取到 477 条记录
2025-08-15 22:31:25,001 - INFO - 获取到 477 条表单数据
2025-08-15 22:31:25,001 - INFO - 当前日期 2025-08-14 有 144 条MySQL数据需要处理
2025-08-15 22:31:25,001 - INFO - 日期 2025-08-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-15 22:31:25,001 - INFO - 开始处理日期: 2025-08-15
2025-08-15 22:31:25,001 - INFO - Request Parameters - Page 1:
2025-08-15 22:31:25,001 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:31:25,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:31:25,501 - INFO - Response - Page 1:
2025-08-15 22:31:25,501 - INFO - 查询完成，共获取到 0 条记录
2025-08-15 22:31:25,501 - INFO - 获取到 0 条表单数据
2025-08-15 22:31:25,501 - INFO - 当前日期 2025-08-15 有 39 条MySQL数据需要处理
2025-08-15 22:31:25,501 - INFO - 开始批量插入 39 条新记录
2025-08-15 22:31:25,736 - INFO - 批量插入响应状态码: 200
2025-08-15 22:31:25,736 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 14:31:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1884', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BDBE7B34-BFBA-7FA1-873B-04834D8A5AE5', 'x-acs-trace-id': 'a63396b11fa3695b3ef6600a6d0a3e92', 'etag': '1MZx9fBi8aHD4X8w4YXA9Pg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-15 22:31:25,736 - INFO - 批量插入响应体: {'result': ['FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMF4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMG4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMH4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMI4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMJ4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMK4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEML4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMM4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMN4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMO4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMP4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMQ4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMR4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMS4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMT4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMU4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMV4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMW4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMX4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMY4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMZ4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM05', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM15', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM25', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM35', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM45', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM55', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM65', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM75', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM85', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM95', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMA5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMB5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMC5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMD5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEME5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMF5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMG5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMH5']}
2025-08-15 22:31:25,751 - INFO - 批量插入表单数据成功，批次 1，共 39 条记录
2025-08-15 22:31:25,751 - INFO - 成功插入的数据ID: ['FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMF4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMG4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMH4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMI4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMJ4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMK4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEML4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMM4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMN4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMO4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMP4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMQ4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMR4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMS4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMT4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMU4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMV4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMW4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMX4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMY4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMZ4', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM05', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM15', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM25', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM35', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM45', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM55', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM65', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM75', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM85', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEM95', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMA5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMB5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMC5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMD5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEME5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMF5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMG5', 'FINST-N3G66S81DY1YWPRGEUDMW8IG7U2V3OKZEXCEMH5']
2025-08-15 22:31:30,768 - INFO - 批量插入完成，共 39 条记录
2025-08-15 22:31:30,768 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 39 条，错误: 0 条
2025-08-15 22:31:30,768 - INFO - 数据同步完成！更新: 0 条，插入: 39 条，错误: 2 条
2025-08-15 22:32:30,791 - INFO - 开始同步昨天与今天的销售数据: 2025-08-14 至 2025-08-15
2025-08-15 22:32:30,791 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-15 22:32:30,791 - INFO - 查询参数: ('2025-08-14', '2025-08-15')
2025-08-15 22:32:30,979 - INFO - MySQL查询成功，时间段: 2025-08-14 至 2025-08-15，共获取 536 条记录
2025-08-15 22:32:30,979 - INFO - 获取到 2 个日期需要处理: ['2025-08-14', '2025-08-15']
2025-08-15 22:32:30,979 - INFO - 开始处理日期: 2025-08-14
2025-08-15 22:32:30,979 - INFO - Request Parameters - Page 1:
2025-08-15 22:32:30,979 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:30,979 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:31,791 - INFO - Response - Page 1:
2025-08-15 22:32:31,791 - INFO - 第 1 页获取到 50 条记录
2025-08-15 22:32:32,307 - INFO - Request Parameters - Page 2:
2025-08-15 22:32:32,307 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:32,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:32,995 - INFO - Response - Page 2:
2025-08-15 22:32:32,995 - INFO - 第 2 页获取到 50 条记录
2025-08-15 22:32:33,495 - INFO - Request Parameters - Page 3:
2025-08-15 22:32:33,495 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:33,495 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:34,276 - INFO - Response - Page 3:
2025-08-15 22:32:34,276 - INFO - 第 3 页获取到 50 条记录
2025-08-15 22:32:34,792 - INFO - Request Parameters - Page 4:
2025-08-15 22:32:34,792 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:34,792 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:35,479 - INFO - Response - Page 4:
2025-08-15 22:32:35,479 - INFO - 第 4 页获取到 50 条记录
2025-08-15 22:32:35,995 - INFO - Request Parameters - Page 5:
2025-08-15 22:32:35,995 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:35,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:36,714 - INFO - Response - Page 5:
2025-08-15 22:32:36,714 - INFO - 第 5 页获取到 50 条记录
2025-08-15 22:32:37,230 - INFO - Request Parameters - Page 6:
2025-08-15 22:32:37,230 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:37,230 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:37,933 - INFO - Response - Page 6:
2025-08-15 22:32:37,933 - INFO - 第 6 页获取到 50 条记录
2025-08-15 22:32:38,433 - INFO - Request Parameters - Page 7:
2025-08-15 22:32:38,433 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:38,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:39,152 - INFO - Response - Page 7:
2025-08-15 22:32:39,152 - INFO - 第 7 页获取到 50 条记录
2025-08-15 22:32:39,668 - INFO - Request Parameters - Page 8:
2025-08-15 22:32:39,668 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:39,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:40,418 - INFO - Response - Page 8:
2025-08-15 22:32:40,418 - INFO - 第 8 页获取到 50 条记录
2025-08-15 22:32:40,918 - INFO - Request Parameters - Page 9:
2025-08-15 22:32:40,918 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:40,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:41,637 - INFO - Response - Page 9:
2025-08-15 22:32:41,637 - INFO - 第 9 页获取到 50 条记录
2025-08-15 22:32:42,153 - INFO - Request Parameters - Page 10:
2025-08-15 22:32:42,153 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:42,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:42,840 - INFO - Response - Page 10:
2025-08-15 22:32:42,840 - INFO - 第 10 页获取到 27 条记录
2025-08-15 22:32:43,356 - INFO - 查询完成，共获取到 477 条记录
2025-08-15 22:32:43,356 - INFO - 获取到 477 条表单数据
2025-08-15 22:32:43,356 - INFO - 当前日期 2025-08-14 有 477 条MySQL数据需要处理
2025-08-15 22:32:43,372 - INFO - 日期 2025-08-14 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-15 22:32:43,372 - INFO - 开始处理日期: 2025-08-15
2025-08-15 22:32:43,372 - INFO - Request Parameters - Page 1:
2025-08-15 22:32:43,372 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-15 22:32:43,372 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-15 22:32:44,075 - INFO - Response - Page 1:
2025-08-15 22:32:44,075 - INFO - 第 1 页获取到 39 条记录
2025-08-15 22:32:44,575 - INFO - 查询完成，共获取到 39 条记录
2025-08-15 22:32:44,575 - INFO - 获取到 39 条表单数据
2025-08-15 22:32:44,575 - INFO - 当前日期 2025-08-15 有 39 条MySQL数据需要处理
2025-08-15 22:32:44,575 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-15 22:32:44,575 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-15 22:32:44,575 - INFO - 同步完成
