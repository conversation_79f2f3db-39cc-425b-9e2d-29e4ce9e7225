2025-08-21 01:30:33,460 - INFO - 使用默认增量同步（当天更新数据）
2025-08-21 01:30:33,460 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-21 01:30:33,460 - INFO - 查询参数: ('2025-08-21',)
2025-08-21 01:30:33,631 - INFO - MySQL查询成功，增量数据（日期: 2025-08-21），共获取 3 条记录
2025-08-21 01:30:33,631 - INFO - 获取到 1 个日期需要处理: ['2025-08-20']
2025-08-21 01:30:33,631 - INFO - 开始处理日期: 2025-08-20
2025-08-21 01:30:33,631 - INFO - Request Parameters - Page 1:
2025-08-21 01:30:33,631 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 01:30:33,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 01:30:41,741 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A0F40095-3E51-7B97-92E8-6893DE5765BB Response: {'code': 'ServiceUnavailable', 'requestid': 'A0F40095-3E51-7B97-92E8-6893DE5765BB', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A0F40095-3E51-7B97-92E8-6893DE5765BB)
2025-08-21 01:30:41,741 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-21 01:31:41,749 - INFO - 开始同步昨天与今天的销售数据: 2025-08-20 至 2025-08-21
2025-08-21 01:31:41,749 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-21 01:31:41,749 - INFO - 查询参数: ('2025-08-20', '2025-08-21')
2025-08-21 01:31:41,921 - INFO - MySQL查询成功，时间段: 2025-08-20 至 2025-08-21，共获取 138 条记录
2025-08-21 01:31:41,921 - INFO - 获取到 1 个日期需要处理: ['2025-08-20']
2025-08-21 01:31:41,921 - INFO - 开始处理日期: 2025-08-20
2025-08-21 01:31:41,921 - INFO - Request Parameters - Page 1:
2025-08-21 01:31:41,921 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 01:31:41,921 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 01:31:42,718 - INFO - Response - Page 1:
2025-08-21 01:31:42,718 - INFO - 第 1 页获取到 50 条记录
2025-08-21 01:31:43,234 - INFO - Request Parameters - Page 2:
2025-08-21 01:31:43,234 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 01:31:43,234 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 01:31:43,765 - INFO - Response - Page 2:
2025-08-21 01:31:43,765 - INFO - 第 2 页获取到 8 条记录
2025-08-21 01:31:44,281 - INFO - 查询完成，共获取到 58 条记录
2025-08-21 01:31:44,281 - INFO - 获取到 58 条表单数据
2025-08-21 01:31:44,281 - INFO - 当前日期 2025-08-20 有 131 条MySQL数据需要处理
2025-08-21 01:31:44,281 - INFO - 开始批量插入 73 条新记录
2025-08-21 01:31:44,515 - INFO - 批量插入响应状态码: 200
2025-08-21 01:31:44,515 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 17:31:44 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '948C33CA-75F8-761E-95D5-7EAFD6FB7C98', 'x-acs-trace-id': '8c7de292c8c2b533fabd19d2299a1779', 'etag': '2dsRrfZObGLciaQS5eR6uhQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 01:31:44,515 - INFO - 批量插入响应体: {'result': ['FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM53', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM63', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM73', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM83', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM93', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMA3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMB3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMC3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMD3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEME3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMF3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMG3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMH3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMI3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMJ3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMK3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEML3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMM3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMN3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMO3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMP3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMQ3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMR3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMS3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMT3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMU3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMV3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMW3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMX3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMY3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMZ3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM04', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM14', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM24', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM34', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM44', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM54', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM64', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM74', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM84', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM94', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMA4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMB4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMC4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMD4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEME4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMF4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMG4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMH4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMI4']}
2025-08-21 01:31:44,515 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-21 01:31:44,515 - INFO - 成功插入的数据ID: ['FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM53', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM63', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM73', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM83', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEM93', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMA3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMB3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMC3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMD3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEME3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMF3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMG3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMH3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMI3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMJ3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMK3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEML3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMM3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMN3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMO3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMP3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMQ3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMR3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMS3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMT3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMU3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMV3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMW3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMX3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMY3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMZ3', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM04', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM14', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM24', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM34', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM44', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM54', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM64', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM74', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM84', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEM94', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMA4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMB4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMC4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMD4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEME4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMF4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMG4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMH4', 'FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02CN629KEMI4']
2025-08-21 01:31:49,733 - INFO - 批量插入响应状态码: 200
2025-08-21 01:31:49,733 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Wed, 20 Aug 2025 17:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1116', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4BC6437A-71F5-76D0-A481-29DCB9BABA72', 'x-acs-trace-id': '9014a67e2f5bce9301e4b0b72bfbd4dc', 'etag': '1LETCXgiCSox8I21er3kx4w6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 01:31:49,733 - INFO - 批量插入响应体: {'result': ['FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM3R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM4R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM5R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM6R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM7R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM8R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM9R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMAR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMBR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMCR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMDR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMER', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMFR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMGR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMHR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMIR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMJR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMKR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMLR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMMR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMNR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMOR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMPR']}
2025-08-21 01:31:49,733 - INFO - 批量插入表单数据成功，批次 2，共 23 条记录
2025-08-21 01:31:49,733 - INFO - 成功插入的数据ID: ['FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM3R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM4R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM5R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM6R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM7R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM8R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEM9R', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMAR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMBR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMCR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMDR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMER', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMFR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMGR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMHR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMIR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMJR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMKR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMLR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMMR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMNR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMOR', 'FINST-2PF66TC14L4Y4IOVDQD836BAB95L3ZNA29KEMPR']
2025-08-21 01:31:54,749 - INFO - 批量插入完成，共 73 条记录
2025-08-21 01:31:54,749 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 73 条，错误: 0 条
2025-08-21 01:31:54,749 - INFO - 数据同步完成！更新: 0 条，插入: 73 条，错误: 0 条
2025-08-21 01:31:54,749 - INFO - 同步完成
2025-08-21 04:30:33,772 - INFO - 使用默认增量同步（当天更新数据）
2025-08-21 04:30:33,772 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-21 04:30:33,772 - INFO - 查询参数: ('2025-08-21',)
2025-08-21 04:30:33,944 - INFO - MySQL查询成功，增量数据（日期: 2025-08-21），共获取 3 条记录
2025-08-21 04:30:33,944 - INFO - 获取到 1 个日期需要处理: ['2025-08-20']
2025-08-21 04:30:33,944 - INFO - 开始处理日期: 2025-08-20
2025-08-21 04:30:33,944 - INFO - Request Parameters - Page 1:
2025-08-21 04:30:33,944 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 04:30:33,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 04:30:42,069 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 20143AC9-CFDC-766F-90F4-77CBE067276C Response: {'code': 'ServiceUnavailable', 'requestid': '20143AC9-CFDC-766F-90F4-77CBE067276C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 20143AC9-CFDC-766F-90F4-77CBE067276C)
2025-08-21 04:30:42,069 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-21 04:31:42,078 - INFO - 开始同步昨天与今天的销售数据: 2025-08-20 至 2025-08-21
2025-08-21 04:31:42,078 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-21 04:31:42,078 - INFO - 查询参数: ('2025-08-20', '2025-08-21')
2025-08-21 04:31:42,249 - INFO - MySQL查询成功，时间段: 2025-08-20 至 2025-08-21，共获取 138 条记录
2025-08-21 04:31:42,249 - INFO - 获取到 1 个日期需要处理: ['2025-08-20']
2025-08-21 04:31:42,249 - INFO - 开始处理日期: 2025-08-20
2025-08-21 04:31:42,249 - INFO - Request Parameters - Page 1:
2025-08-21 04:31:42,249 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 04:31:42,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 04:31:50,374 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 9F42417B-C01B-7299-BB65-3DE948B31C63 Response: {'code': 'ServiceUnavailable', 'requestid': '9F42417B-C01B-7299-BB65-3DE948B31C63', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 9F42417B-C01B-7299-BB65-3DE948B31C63)
2025-08-21 04:31:50,374 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-21 04:31:50,374 - INFO - 同步完成
2025-08-21 07:30:33,673 - INFO - 使用默认增量同步（当天更新数据）
2025-08-21 07:30:33,673 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-21 07:30:33,673 - INFO - 查询参数: ('2025-08-21',)
2025-08-21 07:30:33,860 - INFO - MySQL查询成功，增量数据（日期: 2025-08-21），共获取 3 条记录
2025-08-21 07:30:33,860 - INFO - 获取到 1 个日期需要处理: ['2025-08-20']
2025-08-21 07:30:33,860 - INFO - 开始处理日期: 2025-08-20
2025-08-21 07:30:33,860 - INFO - Request Parameters - Page 1:
2025-08-21 07:30:33,860 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 07:30:33,860 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 07:30:41,970 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E336EE18-E262-73B3-86EA-2374957BFC89 Response: {'code': 'ServiceUnavailable', 'requestid': 'E336EE18-E262-73B3-86EA-2374957BFC89', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E336EE18-E262-73B3-86EA-2374957BFC89)
2025-08-21 07:30:41,985 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-21 07:31:41,996 - INFO - 开始同步昨天与今天的销售数据: 2025-08-20 至 2025-08-21
2025-08-21 07:31:41,996 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-21 07:31:41,996 - INFO - 查询参数: ('2025-08-20', '2025-08-21')
2025-08-21 07:31:42,167 - INFO - MySQL查询成功，时间段: 2025-08-20 至 2025-08-21，共获取 138 条记录
2025-08-21 07:31:42,167 - INFO - 获取到 1 个日期需要处理: ['2025-08-20']
2025-08-21 07:31:42,167 - INFO - 开始处理日期: 2025-08-20
2025-08-21 07:31:42,167 - INFO - Request Parameters - Page 1:
2025-08-21 07:31:42,167 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 07:31:42,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 07:31:42,980 - INFO - Response - Page 1:
2025-08-21 07:31:42,980 - INFO - 第 1 页获取到 50 条记录
2025-08-21 07:31:43,480 - INFO - Request Parameters - Page 2:
2025-08-21 07:31:43,480 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 07:31:43,480 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 07:31:46,589 - INFO - Response - Page 2:
2025-08-21 07:31:46,589 - INFO - 第 2 页获取到 50 条记录
2025-08-21 07:31:47,105 - INFO - Request Parameters - Page 3:
2025-08-21 07:31:47,105 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 07:31:47,105 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 07:31:47,808 - INFO - Response - Page 3:
2025-08-21 07:31:47,808 - INFO - 第 3 页获取到 31 条记录
2025-08-21 07:31:48,308 - INFO - 查询完成，共获取到 131 条记录
2025-08-21 07:31:48,308 - INFO - 获取到 131 条表单数据
2025-08-21 07:31:48,308 - INFO - 当前日期 2025-08-20 有 131 条MySQL数据需要处理
2025-08-21 07:31:48,308 - INFO - 日期 2025-08-20 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-21 07:31:48,308 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-21 07:31:48,308 - INFO - 同步完成
2025-08-21 10:30:33,688 - INFO - 使用默认增量同步（当天更新数据）
2025-08-21 10:30:33,688 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-21 10:30:33,688 - INFO - 查询参数: ('2025-08-21',)
2025-08-21 10:30:33,876 - INFO - MySQL查询成功，增量数据（日期: 2025-08-21），共获取 142 条记录
2025-08-21 10:30:33,876 - INFO - 获取到 2 个日期需要处理: ['2025-08-20', '2025-08-21']
2025-08-21 10:30:33,876 - INFO - 开始处理日期: 2025-08-20
2025-08-21 10:30:33,876 - INFO - Request Parameters - Page 1:
2025-08-21 10:30:33,876 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 10:30:33,876 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 10:30:42,001 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BB83E54D-EC5F-72D6-9AD8-26881A34638F Response: {'code': 'ServiceUnavailable', 'requestid': 'BB83E54D-EC5F-72D6-9AD8-26881A34638F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BB83E54D-EC5F-72D6-9AD8-26881A34638F)
2025-08-21 10:30:42,001 - INFO - 开始处理日期: 2025-08-21
2025-08-21 10:30:42,001 - INFO - Request Parameters - Page 1:
2025-08-21 10:30:42,001 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 10:30:42,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755705600000, 1755791999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 10:30:43,782 - INFO - Response - Page 1:
2025-08-21 10:30:43,782 - INFO - 查询完成，共获取到 0 条记录
2025-08-21 10:30:43,782 - INFO - 获取到 0 条表单数据
2025-08-21 10:30:43,782 - INFO - 当前日期 2025-08-21 有 3 条MySQL数据需要处理
2025-08-21 10:30:43,782 - INFO - 开始批量插入 3 条新记录
2025-08-21 10:30:43,938 - INFO - 批量插入响应状态码: 200
2025-08-21 10:30:43,954 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 02:30:35 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '153', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '90C8603D-D169-7EF4-B67D-AF890DBA1694', 'x-acs-trace-id': '68f2975263919351279ba10b01fe871b', 'etag': '1/Ypt6kqmD5ti4rj+uQCflw3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 10:30:43,954 - INFO - 批量插入响应体: {'result': ['FINST-NYC66LB1FN7YTUB1DB3IP7M7LFSJ34H5BSKEMB', 'FINST-NYC66LB1FN7YTUB1DB3IP7M7LFSJ34H5BSKEMC', 'FINST-NYC66LB1FN7YTUB1DB3IP7M7LFSJ34H5BSKEMD']}
2025-08-21 10:30:43,954 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-21 10:30:43,954 - INFO - 成功插入的数据ID: ['FINST-NYC66LB1FN7YTUB1DB3IP7M7LFSJ34H5BSKEMB', 'FINST-NYC66LB1FN7YTUB1DB3IP7M7LFSJ34H5BSKEMC', 'FINST-NYC66LB1FN7YTUB1DB3IP7M7LFSJ34H5BSKEMD']
2025-08-21 10:30:48,969 - INFO - 批量插入完成，共 3 条记录
2025-08-21 10:30:48,969 - INFO - 日期 2025-08-21 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-08-21 10:30:48,969 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 1 条
2025-08-21 10:31:48,980 - INFO - 开始同步昨天与今天的销售数据: 2025-08-20 至 2025-08-21
2025-08-21 10:31:48,980 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-21 10:31:48,980 - INFO - 查询参数: ('2025-08-20', '2025-08-21')
2025-08-21 10:31:49,167 - INFO - MySQL查询成功，时间段: 2025-08-20 至 2025-08-21，共获取 509 条记录
2025-08-21 10:31:49,167 - INFO - 获取到 2 个日期需要处理: ['2025-08-20', '2025-08-21']
2025-08-21 10:31:49,167 - INFO - 开始处理日期: 2025-08-20
2025-08-21 10:31:49,167 - INFO - Request Parameters - Page 1:
2025-08-21 10:31:49,167 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 10:31:49,167 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 10:31:57,292 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CF1AF5E3-983B-7A22-89C7-BD4663290A1D Response: {'code': 'ServiceUnavailable', 'requestid': 'CF1AF5E3-983B-7A22-89C7-BD4663290A1D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CF1AF5E3-983B-7A22-89C7-BD4663290A1D)
2025-08-21 10:31:57,292 - INFO - 开始处理日期: 2025-08-21
2025-08-21 10:31:57,292 - INFO - Request Parameters - Page 1:
2025-08-21 10:31:57,292 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 10:31:57,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755705600000, 1755791999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 10:31:57,808 - INFO - Response - Page 1:
2025-08-21 10:31:57,808 - INFO - 第 1 页获取到 3 条记录
2025-08-21 10:31:58,323 - INFO - 查询完成，共获取到 3 条记录
2025-08-21 10:31:58,323 - INFO - 获取到 3 条表单数据
2025-08-21 10:31:58,323 - INFO - 当前日期 2025-08-21 有 3 条MySQL数据需要处理
2025-08-21 10:31:58,323 - INFO - 日期 2025-08-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-21 10:31:58,323 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-21 10:31:58,323 - INFO - 同步完成
2025-08-21 13:30:33,554 - INFO - 使用默认增量同步（当天更新数据）
2025-08-21 13:30:33,554 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-21 13:30:33,554 - INFO - 查询参数: ('2025-08-21',)
2025-08-21 13:30:33,741 - INFO - MySQL查询成功，增量数据（日期: 2025-08-21），共获取 148 条记录
2025-08-21 13:30:33,741 - INFO - 获取到 2 个日期需要处理: ['2025-08-20', '2025-08-21']
2025-08-21 13:30:33,741 - INFO - 开始处理日期: 2025-08-20
2025-08-21 13:30:33,741 - INFO - Request Parameters - Page 1:
2025-08-21 13:30:33,741 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 13:30:33,741 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 13:30:41,851 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 14C8D476-F3DD-7A20-B4A8-CE1B2C63EDE8 Response: {'code': 'ServiceUnavailable', 'requestid': '14C8D476-F3DD-7A20-B4A8-CE1B2C63EDE8', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 14C8D476-F3DD-7A20-B4A8-CE1B2C63EDE8)
2025-08-21 13:30:41,851 - INFO - 开始处理日期: 2025-08-21
2025-08-21 13:30:41,851 - INFO - Request Parameters - Page 1:
2025-08-21 13:30:41,851 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 13:30:41,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755705600000, 1755791999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 13:30:49,960 - ERROR - 处理日期 2025-08-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-0428-7F2D-8A61-FFDCC2039FEA Response: {'code': 'ServiceUnavailable', 'requestid': '********-0428-7F2D-8A61-FFDCC2039FEA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-0428-7F2D-8A61-FFDCC2039FEA)
2025-08-21 13:30:49,960 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-21 13:31:49,970 - INFO - 开始同步昨天与今天的销售数据: 2025-08-20 至 2025-08-21
2025-08-21 13:31:49,970 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-21 13:31:49,970 - INFO - 查询参数: ('2025-08-20', '2025-08-21')
2025-08-21 13:31:50,158 - INFO - MySQL查询成功，时间段: 2025-08-20 至 2025-08-21，共获取 518 条记录
2025-08-21 13:31:50,158 - INFO - 获取到 2 个日期需要处理: ['2025-08-20', '2025-08-21']
2025-08-21 13:31:50,158 - INFO - 开始处理日期: 2025-08-20
2025-08-21 13:31:50,158 - INFO - Request Parameters - Page 1:
2025-08-21 13:31:50,158 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 13:31:50,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 13:31:50,986 - INFO - Response - Page 1:
2025-08-21 13:31:50,986 - INFO - 第 1 页获取到 50 条记录
2025-08-21 13:31:51,517 - INFO - Request Parameters - Page 2:
2025-08-21 13:31:51,517 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 13:31:51,517 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 13:31:57,861 - INFO - Response - Page 2:
2025-08-21 13:31:57,861 - INFO - 第 2 页获取到 50 条记录
2025-08-21 13:31:58,361 - INFO - Request Parameters - Page 3:
2025-08-21 13:31:58,361 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 13:31:58,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 13:31:59,095 - INFO - Response - Page 3:
2025-08-21 13:31:59,095 - INFO - 第 3 页获取到 31 条记录
2025-08-21 13:31:59,595 - INFO - 查询完成，共获取到 131 条记录
2025-08-21 13:31:59,595 - INFO - 获取到 131 条表单数据
2025-08-21 13:31:59,595 - INFO - 当前日期 2025-08-20 有 498 条MySQL数据需要处理
2025-08-21 13:31:59,595 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEME3
2025-08-21 13:32:00,220 - INFO - 更新表单数据成功: FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEME3
2025-08-21 13:32:00,220 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000.0, 'new_value': 38000.0}, {'field': 'total_amount', 'old_value': 8000.0, 'new_value': 38000.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-21 13:32:00,220 - INFO - 开始更新记录 - 表单实例ID: FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMD3
2025-08-21 13:32:00,798 - INFO - 更新表单数据成功: FINST-F7D66UA14T6YQ52QEJJ1Q5N8VVS02BN629KEMD3
2025-08-21 13:32:00,798 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5950.0, 'new_value': 45949.0}, {'field': 'total_amount', 'old_value': 5950.0, 'new_value': 45949.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-21 13:32:00,798 - INFO - 开始批量插入 367 条新记录
2025-08-21 13:32:01,033 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:01,033 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:31:53 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2376', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '752DE9FA-7C7D-7EE4-9DD1-F338CE963F3A', 'x-acs-trace-id': 'f2ea298352831a142c81dacca0e97ef4', 'etag': '2yM9JDjDnJY9FYlACGZQJ9w6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:01,033 - INFO - 批量插入响应体: {'result': ['FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM0', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM1', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM2', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM3', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM4', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM5', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM6', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM7', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM8', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM9', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMA', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMB', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMC', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMD', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEME', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMF', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMG', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMH', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMI', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMJ', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMK', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEML', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMM', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMN', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMO', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMP', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMQ', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMR', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMS', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMT', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMU', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMV', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMW', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMX', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMY', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMZ', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM01', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM11', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM21', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM31', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM41', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM51', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM61', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM71', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM81', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM91', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEMA1', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEMB1', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEMC1', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEMD1']}
2025-08-21 13:32:01,033 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-21 13:32:01,033 - INFO - 成功插入的数据ID: ['FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM0', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM1', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM2', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM3', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM4', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM5', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM6', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM7', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM8', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM9', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMA', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMB', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMC', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMD', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEME', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMF', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMG', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMH', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMI', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMJ', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMK', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEML', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMM', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMN', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMO', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMP', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMQ', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMR', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMS', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMT', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMU', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMV', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMW', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMX', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMY', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEMZ', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43MCASYKEM01', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM11', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM21', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM31', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM41', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM51', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM61', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM71', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM81', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEM91', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEMA1', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEMB1', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEMC1', 'FINST-74766M71OT7YWPPWCI1YN7R5PXK43NCASYKEMD1']
2025-08-21 13:32:06,361 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:06,361 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6379935D-8117-7EF8-BB6C-9B3A6020124A', 'x-acs-trace-id': 'a7914543495fe882a6d2dd8ed6720e1d', 'etag': '2WuAm6YFpDl99MSGlCbqjhA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:06,361 - INFO - 批量插入响应体: {'result': ['FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMH1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMI1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMJ1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMK1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEML1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMM1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMN1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMO1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMP1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMQ1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMR1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMS1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMT1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMU1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMV1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMW1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMX1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMY1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMZ1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM02', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM12', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM22', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM32', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM42', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM52', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM62', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM72', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM82', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM92', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMA2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMB2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMC2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMD2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEME2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMF2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMG2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMH2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMI2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMJ2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMK2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEML2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMM2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMN2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMO2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMP2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMQ2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMR2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMS2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMT2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMU2']}
2025-08-21 13:32:06,361 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-21 13:32:06,361 - INFO - 成功插入的数据ID: ['FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMH1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMI1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMJ1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMK1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEML1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMM1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMN1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMO1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMP1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMQ1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMR1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMS1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMT1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMU1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMV1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMW1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMX1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMY1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMZ1', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM02', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM12', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM22', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM32', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM42', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM52', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM62', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM72', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM82', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEM92', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMA2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMB2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMC2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMD2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEME2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMF2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMG2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMH2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMI2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMJ2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMK2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEML2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMM2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMN2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMO2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMP2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMQ2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMR2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMS2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMT2', 'FINST-90E66JD11Q7YF69BBFD4U631D1CU3FGESYKEMU2']
2025-08-21 13:32:11,626 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:11,626 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '12123D92-1690-740B-B28C-7F8E6352197C', 'x-acs-trace-id': '151c755b23055e731f5a70e2f304b4d6', 'etag': '2351YdKByHmK5GguJMzErsg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:11,626 - INFO - 批量插入响应体: {'result': ['FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMS1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMT1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMU1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMV1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMW1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMX1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMY1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMZ1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM02', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM12', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM22', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM32', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM42', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM52', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM62', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM72', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM82', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM92', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMA2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMB2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMC2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMD2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEME2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMF2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMG2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMH2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMI2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMJ2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMK2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEML2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMM2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMN2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMO2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMP2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMQ2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMR2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMS2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMT2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMU2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMV2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMW2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMX2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMY2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMZ2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM03', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM13', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM23', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM33', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM43', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM53']}
2025-08-21 13:32:11,626 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-21 13:32:11,626 - INFO - 成功插入的数据ID: ['FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMS1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMT1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMU1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMV1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMW1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMX1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMY1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMZ1', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM02', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM12', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM22', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM32', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM42', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM52', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM62', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM72', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM82', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM92', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMA2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMB2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMC2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMD2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEME2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMF2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMG2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMH2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMI2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMJ2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMK2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEML2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMM2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMN2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMO2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMP2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMQ2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMR2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMS2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMT2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMU2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMV2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMW2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMX2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMY2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEMZ2', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM03', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM13', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM23', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM33', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM43', 'FINST-1V966BA1IP7YEP2273UWL4VW4D8T2GIISYKEM53']
2025-08-21 13:32:16,892 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:16,892 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:32:08 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DAC69FC2-97ED-786D-A487-72ED4297B6CB', 'x-acs-trace-id': '7858fd9fb48958044050616762bf240b', 'etag': '25n/rKz/plpQENW3/RvuAfw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:16,892 - INFO - 批量插入响应体: {'result': ['FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM81', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM91', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMA1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMB1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMC1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMD1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEME1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMF1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMG1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMH1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMI1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMJ1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMK1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEML1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMM1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMN1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMO1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMP1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMQ1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMR1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMS1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMT1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMU1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMV1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMW1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMX1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMY1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMZ1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM02', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM12', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM22', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM32', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM42', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM52', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM62', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM72', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM82', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM92', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMA2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMB2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMC2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMD2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEME2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMF2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMG2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMH2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMI2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMJ2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3RKMSYKEMK2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3RKMSYKEML2']}
2025-08-21 13:32:16,892 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-08-21 13:32:16,892 - INFO - 成功插入的数据ID: ['FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM81', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM91', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMA1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMB1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMC1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMD1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEME1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMF1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMG1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMH1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMI1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMJ1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMK1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEML1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMM1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMN1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMO1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMP1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMQ1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMR1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMS1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMT1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMU1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMV1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMW1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMX1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMY1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMZ1', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM02', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM12', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM22', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM32', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM42', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM52', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM62', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM72', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM82', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEM92', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMA2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMB2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMC2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMD2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEME2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMF2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMG2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMH2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMI2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3QKMSYKEMJ2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3RKMSYKEMK2', 'FINST-FIG66R81IP7Y74LFACS0MA2OMQIL3RKMSYKEML2']
2025-08-21 13:32:22,126 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:22,126 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:32:14 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2402', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D9EAFDD4-715A-7856-95FA-25937D0D046A', 'x-acs-trace-id': '1517aaa6e3b2f8ace08df73dcbe300bf', 'etag': '2uznGIZCCIhYiaFnd88Z2Jg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:22,126 - INFO - 批量插入响应体: {'result': ['FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMQ', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMR', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMS', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMT', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMU', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMV', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMW', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMX', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMY', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMZ', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM01', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM11', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM21', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM31', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM41', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM51', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM61', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM71', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM81', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM91', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMA1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMB1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMC1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMD1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEME1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMF1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMG1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMH1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMI1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMJ1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMK1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEML1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMM1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMN1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMO1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMP1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMQ1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMR1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMS1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMT1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMU1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMV1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMW1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMX1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEMY1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEMZ1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEM02', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEM12', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEM22', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEM32']}
2025-08-21 13:32:22,126 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-08-21 13:32:22,126 - INFO - 成功插入的数据ID: ['FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMQ', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMR', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMS', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMT', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMU', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMV', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMW', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMX', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMY', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMZ', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM01', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM11', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM21', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM31', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM41', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM51', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM61', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM71', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM81', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEM91', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMA1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMB1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMC1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMD1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEME1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMF1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMG1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMH1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMI1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMJ1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMK1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEML1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMM1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMN1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMO1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMP1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMQ1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMR1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMS1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMT1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMU1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMV1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMW1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2FMQSYKEMX1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEMY1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEMZ1', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEM02', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEM12', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEM22', 'FINST-LR5668B1CP7YM5609JYLB87PFUQH2GMQSYKEM32']
2025-08-21 13:32:27,371 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:27,371 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:32:19 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7F5B3DB6-ECDF-7D77-BBE7-98A8725CEAE6', 'x-acs-trace-id': 'da46d7eb26410a2786556f0c63df6027', 'etag': '2Yz1tXdVI6VeXDmu207SzBw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:27,371 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMB1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMC1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMD1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEME1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMF1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMG1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMH1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMI1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMJ1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMK1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEML1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMM1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMN1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMO1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMP1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMQ1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMR1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMS1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMT1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMU1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMV1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMW1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMX1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMY1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMZ1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM02', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM12', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM22', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM32', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM42', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM52', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM62', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM72', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM82', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM92', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMA2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMB2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMC2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMD2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEME2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMF2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMG2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMH2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMI2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMJ2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMK2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEML2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMM2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMN2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMO2']}
2025-08-21 13:32:27,371 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-08-21 13:32:27,371 - INFO - 成功插入的数据ID: ['FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMB1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMC1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMD1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEME1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMF1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMG1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMH1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMI1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMJ1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMK1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEML1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMM1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMN1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMO1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMP1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMQ1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMR1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMS1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMT1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMU1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMV1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMW1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMX1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMY1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMZ1', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM02', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM12', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM22', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM32', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM42', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM52', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM62', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM72', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM82', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEM92', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMA2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMB2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMC2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMD2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEME2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMF2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMG2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMH2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMI2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMJ2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMK2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEML2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMM2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMN2', 'FINST-K7666JC1OP7YZJPYEEH2J45TDOSO23OUSYKEMO2']
2025-08-21 13:32:32,621 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:32,637 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:32:24 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2382', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9DDD1CCB-9107-7700-B483-D270E172BE57', 'x-acs-trace-id': '4ebb138d52d30839d04229cde3a158f4', 'etag': '2Rj25Ht7LHL+8gVkVF/Yswg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:32,637 - INFO - 批量插入响应体: {'result': ['FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEM6', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEM7', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEM8', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEM9', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEMA', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMB', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMC', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMD', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEME', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMF', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMG', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMH', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMI', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMJ', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMK', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEML', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMM', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMN', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMO', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMP', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMQ', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMR', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMS', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMT', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMU', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMV', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMW', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMX', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMY', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMZ', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM01', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM11', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM21', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM31', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM41', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM51', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM61', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM71', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM81', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM91', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMA1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMB1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMC1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMD1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEME1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMF1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMG1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMH1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMI1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMJ1']}
2025-08-21 13:32:32,637 - INFO - 批量插入表单数据成功，批次 7，共 50 条记录
2025-08-21 13:32:32,637 - INFO - 成功插入的数据ID: ['FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEM6', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEM7', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEM8', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEM9', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ12QYSYKEMA', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMB', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMC', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMD', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEME', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMF', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMG', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMH', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMI', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMJ', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMK', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEML', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMM', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMN', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMO', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMP', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMQ', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMR', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMS', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMT', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMU', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMV', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMW', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMX', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMY', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMZ', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM01', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM11', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM21', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM31', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM41', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM51', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM61', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM71', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM81', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEM91', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMA1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMB1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMC1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMD1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEME1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMF1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMG1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMH1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMI1', 'FINST-X3766I91NP7YJSBACB3SI55GG4AZ13QYSYKEMJ1']
2025-08-21 13:32:37,809 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:37,809 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:32:29 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '828', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6109707D-BC66-7BC9-91B6-47FB6209CFD9', 'x-acs-trace-id': 'd4cf7ce43e0b22e8975afc23090d5d97', 'etag': '8tmSz9ETDoaP2Bqr+XpMF8Q8', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:37,809 - INFO - 批量插入响应体: {'result': ['FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM04', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM14', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM24', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM34', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM44', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM54', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM64', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM74', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM84', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM94', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMA4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMB4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMC4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMD4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEME4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMF4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMG4']}
2025-08-21 13:32:37,824 - INFO - 批量插入表单数据成功，批次 8，共 17 条记录
2025-08-21 13:32:37,824 - INFO - 成功插入的数据ID: ['FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM04', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM14', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM24', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM34', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM44', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM54', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM64', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM74', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM84', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEM94', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMA4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMB4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMC4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMD4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEME4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMF4', 'FINST-07E66I91XP7Y39E6699GW72WFKL837Q2TYKEMG4']
2025-08-21 13:32:42,840 - INFO - 批量插入完成，共 367 条记录
2025-08-21 13:32:42,840 - INFO - 日期 2025-08-20 处理完成 - 更新: 2 条，插入: 367 条，错误: 0 条
2025-08-21 13:32:42,840 - INFO - 开始处理日期: 2025-08-21
2025-08-21 13:32:42,840 - INFO - Request Parameters - Page 1:
2025-08-21 13:32:42,840 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 13:32:42,840 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755705600000, 1755791999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 13:32:43,324 - INFO - Response - Page 1:
2025-08-21 13:32:43,324 - INFO - 第 1 页获取到 3 条记录
2025-08-21 13:32:43,840 - INFO - 查询完成，共获取到 3 条记录
2025-08-21 13:32:43,840 - INFO - 获取到 3 条表单数据
2025-08-21 13:32:43,840 - INFO - 当前日期 2025-08-21 有 4 条MySQL数据需要处理
2025-08-21 13:32:43,840 - INFO - 开始批量插入 1 条新记录
2025-08-21 13:32:43,981 - INFO - 批量插入响应状态码: 200
2025-08-21 13:32:43,981 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Thu, 21 Aug 2025 05:32:36 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '3791F62F-8283-77F7-B98E-638C5E4AC1E1', 'x-acs-trace-id': 'aa4acc749ab6bd592cc0b8e573535eba', 'etag': '6X2Lf+/+gh4V8KyyTrs9+Qw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-21 13:32:43,981 - INFO - 批量插入响应体: {'result': ['FINST-G9G669D1FN7YDDTNBS2Y04RQWQ4A3DH7TYKEMT1']}
2025-08-21 13:32:43,981 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-21 13:32:43,981 - INFO - 成功插入的数据ID: ['FINST-G9G669D1FN7YDDTNBS2Y04RQWQ4A3DH7TYKEMT1']
2025-08-21 13:32:48,996 - INFO - 批量插入完成，共 1 条记录
2025-08-21 13:32:48,996 - INFO - 日期 2025-08-21 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-21 13:32:48,996 - INFO - 数据同步完成！更新: 2 条，插入: 368 条，错误: 0 条
2025-08-21 13:32:48,996 - INFO - 同步完成
2025-08-21 16:30:33,561 - INFO - 使用默认增量同步（当天更新数据）
2025-08-21 16:30:33,561 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-21 16:30:33,561 - INFO - 查询参数: ('2025-08-21',)
2025-08-21 16:30:33,748 - INFO - MySQL查询成功，增量数据（日期: 2025-08-21），共获取 151 条记录
2025-08-21 16:30:33,748 - INFO - 获取到 2 个日期需要处理: ['2025-08-20', '2025-08-21']
2025-08-21 16:30:33,748 - INFO - 开始处理日期: 2025-08-20
2025-08-21 16:30:33,748 - INFO - Request Parameters - Page 1:
2025-08-21 16:30:33,748 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 16:30:33,748 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 16:30:41,873 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E95CB78E-4592-7014-843C-209CAC6D8CDD Response: {'code': 'ServiceUnavailable', 'requestid': 'E95CB78E-4592-7014-843C-209CAC6D8CDD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E95CB78E-4592-7014-843C-209CAC6D8CDD)
2025-08-21 16:30:41,873 - INFO - 开始处理日期: 2025-08-21
2025-08-21 16:30:41,873 - INFO - Request Parameters - Page 1:
2025-08-21 16:30:41,873 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 16:30:41,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755705600000, 1755791999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 16:30:49,514 - INFO - Response - Page 1:
2025-08-21 16:30:49,514 - INFO - 第 1 页获取到 4 条记录
2025-08-21 16:30:50,030 - INFO - 查询完成，共获取到 4 条记录
2025-08-21 16:30:50,030 - INFO - 获取到 4 条表单数据
2025-08-21 16:30:50,030 - INFO - 当前日期 2025-08-21 有 4 条MySQL数据需要处理
2025-08-21 16:30:50,030 - INFO - 日期 2025-08-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-21 16:30:50,030 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-21 16:31:50,040 - INFO - 开始同步昨天与今天的销售数据: 2025-08-20 至 2025-08-21
2025-08-21 16:31:50,040 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-21 16:31:50,040 - INFO - 查询参数: ('2025-08-20', '2025-08-21')
2025-08-21 16:31:50,227 - INFO - MySQL查询成功，时间段: 2025-08-20 至 2025-08-21，共获取 524 条记录
2025-08-21 16:31:50,227 - INFO - 获取到 2 个日期需要处理: ['2025-08-20', '2025-08-21']
2025-08-21 16:31:50,227 - INFO - 开始处理日期: 2025-08-20
2025-08-21 16:31:50,227 - INFO - Request Parameters - Page 1:
2025-08-21 16:31:50,227 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 16:31:50,227 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 16:31:51,196 - INFO - Response - Page 1:
2025-08-21 16:31:51,196 - INFO - 第 1 页获取到 50 条记录
2025-08-21 16:31:51,696 - INFO - Request Parameters - Page 2:
2025-08-21 16:31:51,696 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 16:31:51,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755619200000, 1755705599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 16:31:59,805 - ERROR - 处理日期 2025-08-20 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: CA328C42-E743-7158-AAF5-AE815DAE894D Response: {'code': 'ServiceUnavailable', 'requestid': 'CA328C42-E743-7158-AAF5-AE815DAE894D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: CA328C42-E743-7158-AAF5-AE815DAE894D)
2025-08-21 16:31:59,805 - INFO - 开始处理日期: 2025-08-21
2025-08-21 16:31:59,805 - INFO - Request Parameters - Page 1:
2025-08-21 16:31:59,805 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-21 16:31:59,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755705600000, 1755791999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-21 16:32:00,977 - INFO - Response - Page 1:
2025-08-21 16:32:00,977 - INFO - 第 1 页获取到 4 条记录
2025-08-21 16:32:01,493 - INFO - 查询完成，共获取到 4 条记录
2025-08-21 16:32:01,493 - INFO - 获取到 4 条表单数据
2025-08-21 16:32:01,493 - INFO - 当前日期 2025-08-21 有 4 条MySQL数据需要处理
2025-08-21 16:32:01,493 - INFO - 日期 2025-08-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-21 16:32:01,493 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-21 16:32:01,493 - INFO - 同步完成
