2025-08-10 01:30:34,142 - INFO - 使用默认增量同步（当天更新数据）
2025-08-10 01:30:34,142 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-10 01:30:34,142 - INFO - 查询参数: ('2025-08-10',)
2025-08-10 01:30:34,314 - INFO - MySQL查询成功，增量数据（日期: 2025-08-10），共获取 3 条记录
2025-08-10 01:30:34,314 - INFO - 获取到 1 个日期需要处理: ['2025-08-09']
2025-08-10 01:30:34,314 - INFO - 开始处理日期: 2025-08-09
2025-08-10 01:30:34,314 - INFO - Request Parameters - Page 1:
2025-08-10 01:30:34,314 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 01:30:34,314 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 01:30:42,441 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 0EDC8362-0DE4-7F88-ADD9-AC1F7C32B82B Response: {'code': 'ServiceUnavailable', 'requestid': '0EDC8362-0DE4-7F88-ADD9-AC1F7C32B82B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 0EDC8362-0DE4-7F88-ADD9-AC1F7C32B82B)
2025-08-10 01:30:42,441 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-10 01:31:42,465 - INFO - 开始同步昨天与今天的销售数据: 2025-08-09 至 2025-08-10
2025-08-10 01:31:42,465 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-10 01:31:42,465 - INFO - 查询参数: ('2025-08-09', '2025-08-10')
2025-08-10 01:31:42,621 - INFO - MySQL查询成功，时间段: 2025-08-09 至 2025-08-10，共获取 122 条记录
2025-08-10 01:31:42,621 - INFO - 获取到 1 个日期需要处理: ['2025-08-09']
2025-08-10 01:31:42,621 - INFO - 开始处理日期: 2025-08-09
2025-08-10 01:31:42,621 - INFO - Request Parameters - Page 1:
2025-08-10 01:31:42,621 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 01:31:42,621 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 01:31:50,748 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: EDEE0F5B-1ED8-74B7-A79E-D7CBFA18677B Response: {'code': 'ServiceUnavailable', 'requestid': 'EDEE0F5B-1ED8-74B7-A79E-D7CBFA18677B', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: EDEE0F5B-1ED8-74B7-A79E-D7CBFA18677B)
2025-08-10 01:31:50,748 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-10 01:31:50,748 - INFO - 同步完成
2025-08-10 04:30:34,347 - INFO - 使用默认增量同步（当天更新数据）
2025-08-10 04:30:34,363 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-10 04:30:34,363 - INFO - 查询参数: ('2025-08-10',)
2025-08-10 04:30:34,519 - INFO - MySQL查询成功，增量数据（日期: 2025-08-10），共获取 3 条记录
2025-08-10 04:30:34,519 - INFO - 获取到 1 个日期需要处理: ['2025-08-09']
2025-08-10 04:30:34,519 - INFO - 开始处理日期: 2025-08-09
2025-08-10 04:30:34,519 - INFO - Request Parameters - Page 1:
2025-08-10 04:30:34,519 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 04:30:34,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 04:30:42,646 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E4E6B1D7-7AB9-7ECE-841B-744E66C5928D Response: {'code': 'ServiceUnavailable', 'requestid': 'E4E6B1D7-7AB9-7ECE-841B-744E66C5928D', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E4E6B1D7-7AB9-7ECE-841B-744E66C5928D)
2025-08-10 04:30:42,646 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-10 04:31:42,670 - INFO - 开始同步昨天与今天的销售数据: 2025-08-09 至 2025-08-10
2025-08-10 04:31:42,670 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-10 04:31:42,670 - INFO - 查询参数: ('2025-08-09', '2025-08-10')
2025-08-10 04:31:42,826 - INFO - MySQL查询成功，时间段: 2025-08-09 至 2025-08-10，共获取 122 条记录
2025-08-10 04:31:42,826 - INFO - 获取到 1 个日期需要处理: ['2025-08-09']
2025-08-10 04:31:42,826 - INFO - 开始处理日期: 2025-08-09
2025-08-10 04:31:42,842 - INFO - Request Parameters - Page 1:
2025-08-10 04:31:42,842 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 04:31:42,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 04:31:43,654 - INFO - Response - Page 1:
2025-08-10 04:31:43,654 - INFO - 第 1 页获取到 25 条记录
2025-08-10 04:31:44,155 - INFO - 查询完成，共获取到 25 条记录
2025-08-10 04:31:44,155 - INFO - 获取到 25 条表单数据
2025-08-10 04:31:44,155 - INFO - 当前日期 2025-08-09 有 121 条MySQL数据需要处理
2025-08-10 04:31:44,155 - INFO - 开始批量插入 96 条新记录
2025-08-10 04:31:44,389 - INFO - 批量插入响应状态码: 200
2025-08-10 04:31:44,389 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 09 Aug 2025 20:31:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '348C62B9-80C1-723D-AE10-7759F9F159F3', 'x-acs-trace-id': '0d7ff34816525ad8f45f858a878efa44', 'etag': '2UXKhhrsu6zoACly1QDZdnw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 04:31:44,389 - INFO - 批量插入响应体: {'result': ['FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMF4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMG4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMH4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMI4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMJ4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMK4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EML4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMM4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMN4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMO4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMP4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMQ4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMR4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMS4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMT4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMU4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMV4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMW4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMX4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMY4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMZ4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM05', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM15', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM25', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM35', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM45', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM55', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM65', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM75', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM85', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM95', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMA5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMB5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMC5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMD5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EME5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMF5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMG5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMH5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMI5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMJ5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMK5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EML5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMM5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMN5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMO5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMP5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMQ5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMR5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMS5']}
2025-08-10 04:31:44,389 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-10 04:31:44,389 - INFO - 成功插入的数据ID: ['FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMF4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMG4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMH4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMI4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMJ4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMK4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EML4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMM4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMN4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMO4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMP4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMQ4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMR4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMS4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMT4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMU4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMV4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMW4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMX4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMY4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMZ4', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM05', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM15', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM25', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM35', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM45', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM55', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM65', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM75', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM85', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EM95', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMA5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMB5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMC5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMD5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EME5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMF5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMG5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMH5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMI5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMJ5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMK5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EML5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMM5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMN5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMO5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMP5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMQ5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMR5', 'FINST-IQG66AD1QVUX2R147WD6R95OAGJT2IS6NP4EMS5']
2025-08-10 04:31:49,625 - INFO - 批量插入响应状态码: 200
2025-08-10 04:31:49,625 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 09 Aug 2025 20:31:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2220', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B62D14CC-B228-7793-A0B8-71116F4BD7D6', 'x-acs-trace-id': '958ba3140c4aa644cb4a383b68193cb8', 'etag': '2SENvBn5j4rHpnFyMGK9AKQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 04:31:49,625 - INFO - 批量插入响应体: {'result': ['FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMSA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMTA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMUA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMVA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMWA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMXA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMYA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMZA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM0B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM1B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM2B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM3B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM4B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM5B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM6B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM7B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM8B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM9B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMAB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMBB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMCB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMDB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMEB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMFB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMGB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMHB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMIB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMJB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMKB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMLB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMMB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMNB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMOB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMPB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMQB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMRB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMSB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMTB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMUB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMVB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMWB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMXB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMYB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMZB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM0C', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM1C']}
2025-08-10 04:31:49,625 - INFO - 批量插入表单数据成功，批次 2，共 46 条记录
2025-08-10 04:31:49,625 - INFO - 成功插入的数据ID: ['FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMSA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMTA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMUA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMVA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMWA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMXA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMYA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMZA', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM0B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM1B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM2B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM3B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM4B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM5B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM6B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM7B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM8B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM9B', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMAB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMBB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMCB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMDB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMEB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMFB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMGB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMHB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMIB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMJB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMKB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMLB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMMB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMNB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMOB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMPB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMQB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMRB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMSB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMTB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMUB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMVB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMWB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMXB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMYB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EMZB', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM0C', 'FINST-8P666U91BTUX46V4CMW6SAIQIZ313TTANP4EM1C']
2025-08-10 04:31:54,641 - INFO - 批量插入完成，共 96 条记录
2025-08-10 04:31:54,641 - INFO - 日期 2025-08-09 处理完成 - 更新: 0 条，插入: 96 条，错误: 0 条
2025-08-10 04:31:54,641 - INFO - 数据同步完成！更新: 0 条，插入: 96 条，错误: 0 条
2025-08-10 04:31:54,641 - INFO - 同步完成
2025-08-10 07:30:33,754 - INFO - 使用默认增量同步（当天更新数据）
2025-08-10 07:30:33,754 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-10 07:30:33,754 - INFO - 查询参数: ('2025-08-10',)
2025-08-10 07:30:33,926 - INFO - MySQL查询成功，增量数据（日期: 2025-08-10），共获取 9 条记录
2025-08-10 07:30:33,926 - INFO - 获取到 1 个日期需要处理: ['2025-08-09']
2025-08-10 07:30:33,926 - INFO - 开始处理日期: 2025-08-09
2025-08-10 07:30:33,926 - INFO - Request Parameters - Page 1:
2025-08-10 07:30:33,926 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 07:30:33,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 07:30:42,051 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3C2CB849-13B9-72FC-B77C-4E983E478E94 Response: {'code': 'ServiceUnavailable', 'requestid': '3C2CB849-13B9-72FC-B77C-4E983E478E94', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3C2CB849-13B9-72FC-B77C-4E983E478E94)
2025-08-10 07:30:42,051 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-10 07:31:42,061 - INFO - 开始同步昨天与今天的销售数据: 2025-08-09 至 2025-08-10
2025-08-10 07:31:42,061 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-10 07:31:42,061 - INFO - 查询参数: ('2025-08-09', '2025-08-10')
2025-08-10 07:31:42,217 - INFO - MySQL查询成功，时间段: 2025-08-09 至 2025-08-10，共获取 128 条记录
2025-08-10 07:31:42,217 - INFO - 获取到 1 个日期需要处理: ['2025-08-09']
2025-08-10 07:31:42,217 - INFO - 开始处理日期: 2025-08-09
2025-08-10 07:31:42,217 - INFO - Request Parameters - Page 1:
2025-08-10 07:31:42,217 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 07:31:42,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 07:31:42,967 - INFO - Response - Page 1:
2025-08-10 07:31:42,967 - INFO - 第 1 页获取到 50 条记录
2025-08-10 07:31:43,483 - INFO - Request Parameters - Page 2:
2025-08-10 07:31:43,483 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 07:31:43,483 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 07:31:44,202 - INFO - Response - Page 2:
2025-08-10 07:31:44,202 - INFO - 第 2 页获取到 50 条记录
2025-08-10 07:31:44,717 - INFO - Request Parameters - Page 3:
2025-08-10 07:31:44,717 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 07:31:44,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 07:31:45,358 - INFO - Response - Page 3:
2025-08-10 07:31:45,358 - INFO - 第 3 页获取到 21 条记录
2025-08-10 07:31:45,874 - INFO - 查询完成，共获取到 121 条记录
2025-08-10 07:31:45,874 - INFO - 获取到 121 条表单数据
2025-08-10 07:31:45,874 - INFO - 当前日期 2025-08-09 有 127 条MySQL数据需要处理
2025-08-10 07:31:45,874 - INFO - 开始批量插入 6 条新记录
2025-08-10 07:31:46,077 - INFO - 批量插入响应状态码: 200
2025-08-10 07:31:46,077 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 09 Aug 2025 23:31:37 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D909FD35-1E9D-74B4-962A-C0B3966B5732', 'x-acs-trace-id': '169fac5abe679a061963c65a51016130', 'etag': '3dBYZb0tlPRRSBXn8YCMi8Q0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 07:31:46,077 - INFO - 批量插入响应体: {'result': ['FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMNC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMOC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMPC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMQC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMRC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMSC']}
2025-08-10 07:31:46,077 - INFO - 批量插入表单数据成功，批次 1，共 6 条记录
2025-08-10 07:31:46,077 - INFO - 成功插入的数据ID: ['FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMNC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMOC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMPC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMQC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMRC', 'FINST-OLC66Z61PVUX7WGSAGJKD4MTGR343BLM2W4EMSC']
2025-08-10 07:31:51,092 - INFO - 批量插入完成，共 6 条记录
2025-08-10 07:31:51,092 - INFO - 日期 2025-08-09 处理完成 - 更新: 0 条，插入: 6 条，错误: 0 条
2025-08-10 07:31:51,092 - INFO - 数据同步完成！更新: 0 条，插入: 6 条，错误: 0 条
2025-08-10 07:31:51,092 - INFO - 同步完成
2025-08-10 10:30:33,752 - INFO - 使用默认增量同步（当天更新数据）
2025-08-10 10:30:33,752 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-10 10:30:33,752 - INFO - 查询参数: ('2025-08-10',)
2025-08-10 10:30:33,924 - INFO - MySQL查询成功，增量数据（日期: 2025-08-10），共获取 77 条记录
2025-08-10 10:30:33,924 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 10:30:33,924 - INFO - 开始处理日期: 2025-08-09
2025-08-10 10:30:33,924 - INFO - Request Parameters - Page 1:
2025-08-10 10:30:33,924 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 10:30:33,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 10:30:42,033 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: BD9A7ED5-21F1-742A-8258-FBB063755112 Response: {'code': 'ServiceUnavailable', 'requestid': 'BD9A7ED5-21F1-742A-8258-FBB063755112', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: BD9A7ED5-21F1-742A-8258-FBB063755112)
2025-08-10 10:30:42,033 - INFO - 开始处理日期: 2025-08-10
2025-08-10 10:30:42,033 - INFO - Request Parameters - Page 1:
2025-08-10 10:30:42,033 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 10:30:42,033 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 10:30:50,174 - ERROR - 处理日期 2025-08-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 5650BEA9-113F-7830-8F82-223F1DDB3310 Response: {'code': 'ServiceUnavailable', 'requestid': '5650BEA9-113F-7830-8F82-223F1DDB3310', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 5650BEA9-113F-7830-8F82-223F1DDB3310)
2025-08-10 10:30:50,174 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-10 10:31:50,184 - INFO - 开始同步昨天与今天的销售数据: 2025-08-09 至 2025-08-10
2025-08-10 10:31:50,184 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-10 10:31:50,184 - INFO - 查询参数: ('2025-08-09', '2025-08-10')
2025-08-10 10:31:50,341 - INFO - MySQL查询成功，时间段: 2025-08-09 至 2025-08-10，共获取 269 条记录
2025-08-10 10:31:50,341 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 10:31:50,356 - INFO - 开始处理日期: 2025-08-09
2025-08-10 10:31:50,356 - INFO - Request Parameters - Page 1:
2025-08-10 10:31:50,356 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 10:31:50,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 10:31:51,387 - INFO - Response - Page 1:
2025-08-10 10:31:51,387 - INFO - 第 1 页获取到 50 条记录
2025-08-10 10:31:51,887 - INFO - Request Parameters - Page 2:
2025-08-10 10:31:51,887 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 10:31:51,887 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 10:31:52,637 - INFO - Response - Page 2:
2025-08-10 10:31:52,637 - INFO - 第 2 页获取到 50 条记录
2025-08-10 10:31:53,153 - INFO - Request Parameters - Page 3:
2025-08-10 10:31:53,153 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 10:31:53,153 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 10:31:53,856 - INFO - Response - Page 3:
2025-08-10 10:31:53,856 - INFO - 第 3 页获取到 27 条记录
2025-08-10 10:31:54,372 - INFO - 查询完成，共获取到 127 条记录
2025-08-10 10:31:54,372 - INFO - 获取到 127 条表单数据
2025-08-10 10:31:54,372 - INFO - 当前日期 2025-08-09 有 261 条MySQL数据需要处理
2025-08-10 10:31:54,372 - INFO - 开始批量插入 134 条新记录
2025-08-10 10:31:54,622 - INFO - 批量插入响应状态码: 200
2025-08-10 10:31:54,622 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 02:31:46 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B14BB51A-7655-7530-A8B3-707E52512D5F', 'x-acs-trace-id': 'a3d1e85d9b6884e6d6b85f8b163e6447', 'etag': '2ajZcQUtizPrLnfRvTh0E+A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 10:31:54,622 - INFO - 批量插入响应体: {'result': ['FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMI9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMJ9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMK9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EML9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMM9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMN9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMO9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMP9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMQ9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMR9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMS9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMT9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMU9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMV9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMW9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMX9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMY9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMZ9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM0A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM1A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM2A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM3A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM4A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM5A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM6A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM7A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM8A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM9A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMAA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMBA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMCA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMDA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMEA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMFA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMGA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMHA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMIA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMJA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMKA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMLA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMMA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMNA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMOA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMPA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMQA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMRA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMSA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMTA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMUA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMVA']}
2025-08-10 10:31:54,622 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-10 10:31:54,622 - INFO - 成功插入的数据ID: ['FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMI9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMJ9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMK9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EML9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMM9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMN9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMO9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMP9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMQ9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMR9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMS9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMT9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMU9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMV9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMW9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMX9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMY9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMZ9', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM0A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM1A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM2A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM3A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM4A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM5A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM6A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM7A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM8A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EM9A', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMAA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMBA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMCA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMDA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMEA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMFA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMGA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMHA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMIA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMJA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMKA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMLA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMMA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMNA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMOA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMPA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMQA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMRA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMSA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMTA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMUA', 'FINST-2FD66I71AVUXAF90DXHB742UHIAA34LAI25EMVA']
2025-08-10 10:31:59,856 - INFO - 批量插入响应状态码: 200
2025-08-10 10:31:59,856 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 02:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '53DD203B-A336-7E11-A53C-854A46601884', 'x-acs-trace-id': '06c68b1f292db448d21a8500ca791c28', 'etag': '26OFi1Nhu59H1rkGf255qYw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 10:31:59,856 - INFO - 批量插入响应体: {'result': ['FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMBB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMCB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMDB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMEB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMFB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMGB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMHB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMIB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMJB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMKB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMLB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMMB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMNB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMOB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMPB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMQB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMRB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMSB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMTB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMUB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMVB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMWB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMXB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMYB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMZB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM0C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM1C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM2C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM3C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM4C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM5C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM6C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM7C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM8C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM9C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMAC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMBC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMCC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMDC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMEC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMFC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMGC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMHC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMIC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMJC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMKC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMLC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMMC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMNC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMOC']}
2025-08-10 10:31:59,856 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-10 10:31:59,856 - INFO - 成功插入的数据ID: ['FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMBB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMCB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMDB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMEB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMFB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMGB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMHB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMIB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMJB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMKB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMLB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMMB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMNB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMOB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMPB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMQB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMRB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMSB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMTB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMUB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMVB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMWB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMXB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMYB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMZB', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM0C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM1C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM2C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM3C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM4C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM5C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM6C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM7C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM8C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EM9C', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMAC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMBC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMCC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMDC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMEC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMFC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMGC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMHC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMIC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMJC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMKC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMLC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMMC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMNC', 'FINST-FD966QA13VUXGUT9FINL84CHR4V13HMEI25EMOC']
2025-08-10 10:32:05,090 - INFO - 批量插入响应状态码: 200
2025-08-10 10:32:05,090 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 02:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1644', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '80E62308-C854-7EEE-AE24-F008534D78E4', 'x-acs-trace-id': 'd889ae2ba8e896d40117ba77b819e776', 'etag': '1QW3Ihj9E9Lxxa3YD/59+zg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 10:32:05,090 - INFO - 批量插入响应体: {'result': ['FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMUM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMVM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMWM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMXM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMYM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMZM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM0N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM1N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM2N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM3N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM4N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM5N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM6N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM7N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM8N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM9N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMAN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMBN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMCN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMDN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMEN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMFN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMGN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMHN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMIN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMJN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMKN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMLN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMMN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMNN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMON', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMPN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMQN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMRN']}
2025-08-10 10:32:05,090 - INFO - 批量插入表单数据成功，批次 3，共 34 条记录
2025-08-10 10:32:05,090 - INFO - 成功插入的数据ID: ['FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMUM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMVM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMWM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMXM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMYM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMZM', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM0N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM1N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM2N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM3N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM4N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM5N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM6N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM7N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM8N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EM9N', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMAN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMBN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMCN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMDN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMEN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMFN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMGN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMHN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMIN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMJN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMKN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMLN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMMN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMNN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMON', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMPN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMQN', 'FINST-BTF66DB15ZTXXZ8U50PEDAJULV3U2NNII25EMRN']
2025-08-10 10:32:10,106 - INFO - 批量插入完成，共 134 条记录
2025-08-10 10:32:10,106 - INFO - 日期 2025-08-09 处理完成 - 更新: 0 条，插入: 134 条，错误: 0 条
2025-08-10 10:32:10,106 - INFO - 开始处理日期: 2025-08-10
2025-08-10 10:32:10,106 - INFO - Request Parameters - Page 1:
2025-08-10 10:32:10,106 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 10:32:10,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 10:32:10,606 - INFO - Response - Page 1:
2025-08-10 10:32:10,606 - INFO - 查询完成，共获取到 0 条记录
2025-08-10 10:32:10,606 - INFO - 获取到 0 条表单数据
2025-08-10 10:32:10,606 - INFO - 当前日期 2025-08-10 有 1 条MySQL数据需要处理
2025-08-10 10:32:10,606 - INFO - 开始批量插入 1 条新记录
2025-08-10 10:32:10,762 - INFO - 批量插入响应状态码: 200
2025-08-10 10:32:10,762 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 02:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '73C957C8-2964-7D52-A9B3-270107841323', 'x-acs-trace-id': '092f53793d9cdfe6c04844b35cbbe93a', 'etag': '6gS7Mch5gEuMpchWtHoI4jw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 10:32:10,762 - INFO - 批量插入响应体: {'result': ['FINST-OWA66OA1VRUXL2XJDAJF164D51823F1NI25EM53']}
2025-08-10 10:32:10,762 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-10 10:32:10,762 - INFO - 成功插入的数据ID: ['FINST-OWA66OA1VRUXL2XJDAJF164D51823F1NI25EM53']
2025-08-10 10:32:15,778 - INFO - 批量插入完成，共 1 条记录
2025-08-10 10:32:15,778 - INFO - 日期 2025-08-10 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-10 10:32:15,778 - INFO - 数据同步完成！更新: 0 条，插入: 135 条，错误: 0 条
2025-08-10 10:32:15,778 - INFO - 同步完成
2025-08-10 13:30:33,531 - INFO - 使用默认增量同步（当天更新数据）
2025-08-10 13:30:33,531 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-10 13:30:33,531 - INFO - 查询参数: ('2025-08-10',)
2025-08-10 13:30:33,703 - INFO - MySQL查询成功，增量数据（日期: 2025-08-10），共获取 132 条记录
2025-08-10 13:30:33,703 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 13:30:33,703 - INFO - 开始处理日期: 2025-08-09
2025-08-10 13:30:33,703 - INFO - Request Parameters - Page 1:
2025-08-10 13:30:33,703 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:30:33,703 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:30:41,828 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F2115AB5-5A4B-76E6-9BAE-3F9B78218857 Response: {'code': 'ServiceUnavailable', 'requestid': 'F2115AB5-5A4B-76E6-9BAE-3F9B78218857', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F2115AB5-5A4B-76E6-9BAE-3F9B78218857)
2025-08-10 13:30:41,828 - INFO - 开始处理日期: 2025-08-10
2025-08-10 13:30:41,828 - INFO - Request Parameters - Page 1:
2025-08-10 13:30:41,828 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:30:41,828 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:30:49,938 - ERROR - 处理日期 2025-08-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: FE896613-63EF-71A3-9648-71812BC384B6 Response: {'code': 'ServiceUnavailable', 'requestid': 'FE896613-63EF-71A3-9648-71812BC384B6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: FE896613-63EF-71A3-9648-71812BC384B6)
2025-08-10 13:30:49,938 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-10 13:31:49,948 - INFO - 开始同步昨天与今天的销售数据: 2025-08-09 至 2025-08-10
2025-08-10 13:31:49,948 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-10 13:31:49,948 - INFO - 查询参数: ('2025-08-09', '2025-08-10')
2025-08-10 13:31:50,120 - INFO - MySQL查询成功，时间段: 2025-08-09 至 2025-08-10，共获取 448 条记录
2025-08-10 13:31:50,120 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 13:31:50,120 - INFO - 开始处理日期: 2025-08-09
2025-08-10 13:31:50,120 - INFO - Request Parameters - Page 1:
2025-08-10 13:31:50,120 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:31:50,120 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:31:50,963 - INFO - Response - Page 1:
2025-08-10 13:31:50,963 - INFO - 第 1 页获取到 50 条记录
2025-08-10 13:31:51,463 - INFO - Request Parameters - Page 2:
2025-08-10 13:31:51,463 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:31:51,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:31:52,198 - INFO - Response - Page 2:
2025-08-10 13:31:52,198 - INFO - 第 2 页获取到 50 条记录
2025-08-10 13:31:52,713 - INFO - Request Parameters - Page 3:
2025-08-10 13:31:52,713 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:31:52,713 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:31:53,495 - INFO - Response - Page 3:
2025-08-10 13:31:53,495 - INFO - 第 3 页获取到 50 条记录
2025-08-10 13:31:53,995 - INFO - Request Parameters - Page 4:
2025-08-10 13:31:53,995 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:31:53,995 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:31:54,729 - INFO - Response - Page 4:
2025-08-10 13:31:54,729 - INFO - 第 4 页获取到 50 条记录
2025-08-10 13:31:55,229 - INFO - Request Parameters - Page 5:
2025-08-10 13:31:55,229 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:31:55,229 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:31:55,948 - INFO - Response - Page 5:
2025-08-10 13:31:55,948 - INFO - 第 5 页获取到 50 条记录
2025-08-10 13:31:56,448 - INFO - Request Parameters - Page 6:
2025-08-10 13:31:56,448 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:31:56,448 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:31:57,057 - INFO - Response - Page 6:
2025-08-10 13:31:57,057 - INFO - 第 6 页获取到 11 条记录
2025-08-10 13:31:57,557 - INFO - 查询完成，共获取到 261 条记录
2025-08-10 13:31:57,557 - INFO - 获取到 261 条表单数据
2025-08-10 13:31:57,557 - INFO - 当前日期 2025-08-09 有 436 条MySQL数据需要处理
2025-08-10 13:31:57,557 - INFO - 开始批量插入 175 条新记录
2025-08-10 13:31:57,838 - INFO - 批量插入响应状态码: 200
2025-08-10 13:31:57,838 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 05:31:49 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6CA977DF-61D0-7D07-934F-172950A657D6', 'x-acs-trace-id': '2add6fe215227709455c705f6428bd88', 'etag': '2SKLjm5JBUbHu5pOeHtSjIw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 13:31:57,838 - INFO - 批量插入响应体: {'result': ['FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMZ9', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM0A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM1A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM2A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM3A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM4A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM5A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM6A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM7A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM8A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM9A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMAA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMBA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMCA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMDA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMEA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMFA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMGA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMHA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMIA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMJA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMKA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMLA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMMA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMNA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMOA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMPA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMQA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMRA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMSA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMTA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMUA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMVA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMWA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMXA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMYA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMZA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM0B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM1B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM2B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM3B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM4B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM5B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM6B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM7B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM8B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM9B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMAB', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMBB', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMCB']}
2025-08-10 13:31:57,838 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-10 13:31:57,838 - INFO - 成功插入的数据ID: ['FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMZ9', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM0A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM1A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM2A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM3A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM4A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM5A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM6A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM7A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM8A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM9A', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMAA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMBA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMCA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMDA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMEA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMFA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMGA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMHA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMIA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMJA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMKA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMLA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMMA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMNA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMOA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMPA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMQA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMRA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMSA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMTA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMUA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMVA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMWA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMXA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMYA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMZA', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM0B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM1B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM2B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM3B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM4B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM5B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM6B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM7B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM8B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EM9B', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMAB', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMBB', 'FINST-9EA669D1JVUXT6DB961WB4KCHUN22FGUX85EMCB']
2025-08-10 13:32:03,073 - INFO - 批量插入响应状态码: 200
2025-08-10 13:32:03,073 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 05:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'A858CE23-57E4-7208-86CD-EB53571D9695', 'x-acs-trace-id': '9b2b1f35322557d822aa246f23690d92', 'etag': '2TRy+5XkiJYumEbbIAEx/IA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 13:32:03,073 - INFO - 批量插入响应体: {'result': ['FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM0E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM1E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM2E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM3E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM4E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM5E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM6E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM7E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM8E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM9E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMAE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMBE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMCE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMDE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMEE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMFE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMGE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMHE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMIE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMJE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMKE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMLE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMME', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMNE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMOE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMPE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMQE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMRE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMSE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMTE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMUE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMVE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMWE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMXE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMYE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMZE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM0F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM1F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM2F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM3F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM4F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM5F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM6F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM7F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM8F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM9F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMAF', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMBF', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMCF', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMDF']}
2025-08-10 13:32:03,073 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-10 13:32:03,073 - INFO - 成功插入的数据ID: ['FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM0E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM1E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM2E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM3E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM4E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM5E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM6E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM7E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM8E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM9E', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMAE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMBE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMCE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMDE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMEE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMFE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMGE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMHE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMIE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMJE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMKE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMLE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMME', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMNE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMOE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMPE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMQE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMRE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMSE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMTE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMUE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMVE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMWE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMXE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMYE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMZE', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM0F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM1F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM2F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM3F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM4F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM5F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM6F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM7F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM8F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EM9F', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMAF', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMBF', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMCF', 'FINST-G9G669D1OTUX11JF7DXF29GBCS6M31IYX85EMDF']
2025-08-10 13:32:08,370 - INFO - 批量插入响应状态码: 200
2025-08-10 13:32:08,370 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 05:32:00 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8312AB90-6334-7856-983C-4A6B1AAC54B1', 'x-acs-trace-id': 'bd5384579f48fed6789a85fae4fe891d', 'etag': '2603h46k1n1b/gl3dJr+XoQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 13:32:08,370 - INFO - 批量插入响应体: {'result': ['FINST-F7D66UA1IVUX16JAA6SOBDT5S5B022L2Y85EM43', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B022L2Y85EM53', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B022L2Y85EM63', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B022L2Y85EM73', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM83', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM93', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMA3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMB3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMC3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMD3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EME3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMF3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMG3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMH3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMI3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMJ3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMK3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EML3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMM3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMN3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMO3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMP3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMQ3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMR3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMS3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMT3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMU3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMV3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMW3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMX3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMY3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMZ3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM04', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM14', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM24', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM34', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM44', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM54', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM64', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM74', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM84', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM94', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMA4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMB4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMC4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMD4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EME4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMF4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMG4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMH4']}
2025-08-10 13:32:08,370 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-10 13:32:08,370 - INFO - 成功插入的数据ID: ['FINST-F7D66UA1IVUX16JAA6SOBDT5S5B022L2Y85EM43', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B022L2Y85EM53', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B022L2Y85EM63', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B022L2Y85EM73', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM83', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM93', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMA3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMB3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMC3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMD3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EME3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMF3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMG3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMH3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMI3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMJ3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMK3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EML3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMM3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMN3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMO3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMP3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMQ3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMR3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMS3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMT3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMU3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMV3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMW3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMX3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMY3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMZ3', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM04', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM14', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM24', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM34', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM44', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM54', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM64', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM74', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM84', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EM94', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMA4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMB4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMC4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMD4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EME4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMF4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMG4', 'FINST-F7D66UA1IVUX16JAA6SOBDT5S5B023L2Y85EMH4']
2025-08-10 13:32:13,588 - INFO - 批量插入响应状态码: 200
2025-08-10 13:32:13,588 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 05:32:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1212', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '75F1B8FD-AFCB-724F-8E64-06D1813072C7', 'x-acs-trace-id': '9c4e9028951a2fb43a6530f9ff7a0d44', 'etag': '1vdeWAjYwjezBYKuPVk4QRQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 13:32:13,588 - INFO - 批量插入响应体: {'result': ['FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMO6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMP6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMQ6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMR6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMS6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMT6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMU6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMV6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMW6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMX6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMY6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMZ6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM07', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM17', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM27', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM37', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM47', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM57', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM67', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM77', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM87', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM97', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMA7', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMB7', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMC7']}
2025-08-10 13:32:13,588 - INFO - 批量插入表单数据成功，批次 4，共 25 条记录
2025-08-10 13:32:13,588 - INFO - 成功插入的数据ID: ['FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMO6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMP6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMQ6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMR6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMS6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMT6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMU6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMV6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMW6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMX6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMY6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMZ6', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM07', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM17', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM27', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM37', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM47', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM57', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM67', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM77', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM87', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EM97', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMA7', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMB7', 'FINST-2PF66KD1AYUXO4GBBQNSYBHNX08N25M6Y85EMC7']
2025-08-10 13:32:18,604 - INFO - 批量插入完成，共 175 条记录
2025-08-10 13:32:18,604 - INFO - 日期 2025-08-09 处理完成 - 更新: 0 条，插入: 175 条，错误: 0 条
2025-08-10 13:32:18,604 - INFO - 开始处理日期: 2025-08-10
2025-08-10 13:32:18,604 - INFO - Request Parameters - Page 1:
2025-08-10 13:32:18,604 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 13:32:18,604 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 13:32:19,119 - INFO - Response - Page 1:
2025-08-10 13:32:19,119 - INFO - 第 1 页获取到 1 条记录
2025-08-10 13:32:19,619 - INFO - 查询完成，共获取到 1 条记录
2025-08-10 13:32:19,619 - INFO - 获取到 1 条表单数据
2025-08-10 13:32:19,619 - INFO - 当前日期 2025-08-10 有 1 条MySQL数据需要处理
2025-08-10 13:32:19,619 - INFO - 日期 2025-08-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-10 13:32:19,619 - INFO - 数据同步完成！更新: 0 条，插入: 175 条，错误: 0 条
2025-08-10 13:32:19,619 - INFO - 同步完成
2025-08-10 16:30:33,878 - INFO - 使用默认增量同步（当天更新数据）
2025-08-10 16:30:33,878 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-10 16:30:33,878 - INFO - 查询参数: ('2025-08-10',)
2025-08-10 16:30:34,050 - INFO - MySQL查询成功，增量数据（日期: 2025-08-10），共获取 133 条记录
2025-08-10 16:30:34,050 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 16:30:34,050 - INFO - 开始处理日期: 2025-08-09
2025-08-10 16:30:34,065 - INFO - Request Parameters - Page 1:
2025-08-10 16:30:34,065 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 16:30:34,065 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 16:30:42,185 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1C4700C4-E6E6-7200-9BA0-34F47C3E66B4 Response: {'code': 'ServiceUnavailable', 'requestid': '1C4700C4-E6E6-7200-9BA0-34F47C3E66B4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1C4700C4-E6E6-7200-9BA0-34F47C3E66B4)
2025-08-10 16:30:42,185 - INFO - 开始处理日期: 2025-08-10
2025-08-10 16:30:42,185 - INFO - Request Parameters - Page 1:
2025-08-10 16:30:42,185 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 16:30:42,185 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 16:30:42,873 - INFO - Response - Page 1:
2025-08-10 16:30:42,873 - INFO - 第 1 页获取到 1 条记录
2025-08-10 16:30:43,389 - INFO - 查询完成，共获取到 1 条记录
2025-08-10 16:30:43,389 - INFO - 获取到 1 条表单数据
2025-08-10 16:30:43,389 - INFO - 当前日期 2025-08-10 有 1 条MySQL数据需要处理
2025-08-10 16:30:43,389 - INFO - 日期 2025-08-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-10 16:30:43,389 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-10 16:31:43,399 - INFO - 开始同步昨天与今天的销售数据: 2025-08-09 至 2025-08-10
2025-08-10 16:31:43,399 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-10 16:31:43,399 - INFO - 查询参数: ('2025-08-09', '2025-08-10')
2025-08-10 16:31:43,571 - INFO - MySQL查询成功，时间段: 2025-08-09 至 2025-08-10，共获取 450 条记录
2025-08-10 16:31:43,571 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 16:31:43,571 - INFO - 开始处理日期: 2025-08-09
2025-08-10 16:31:43,571 - INFO - Request Parameters - Page 1:
2025-08-10 16:31:43,571 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 16:31:43,571 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 16:31:51,711 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B292CFB0-31DF-7040-A760-424F159EBE0F Response: {'code': 'ServiceUnavailable', 'requestid': 'B292CFB0-31DF-7040-A760-424F159EBE0F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B292CFB0-31DF-7040-A760-424F159EBE0F)
2025-08-10 16:31:51,711 - INFO - 开始处理日期: 2025-08-10
2025-08-10 16:31:51,711 - INFO - Request Parameters - Page 1:
2025-08-10 16:31:51,711 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 16:31:51,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 16:31:57,446 - INFO - Response - Page 1:
2025-08-10 16:31:57,446 - INFO - 第 1 页获取到 1 条记录
2025-08-10 16:31:57,946 - INFO - 查询完成，共获取到 1 条记录
2025-08-10 16:31:57,946 - INFO - 获取到 1 条表单数据
2025-08-10 16:31:57,946 - INFO - 当前日期 2025-08-10 有 1 条MySQL数据需要处理
2025-08-10 16:31:57,946 - INFO - 日期 2025-08-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-10 16:31:57,946 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-10 16:31:57,946 - INFO - 同步完成
2025-08-10 19:30:33,597 - INFO - 使用默认增量同步（当天更新数据）
2025-08-10 19:30:33,597 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-10 19:30:33,597 - INFO - 查询参数: ('2025-08-10',)
2025-08-10 19:30:33,769 - INFO - MySQL查询成功，增量数据（日期: 2025-08-10），共获取 133 条记录
2025-08-10 19:30:33,769 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 19:30:33,769 - INFO - 开始处理日期: 2025-08-09
2025-08-10 19:30:33,769 - INFO - Request Parameters - Page 1:
2025-08-10 19:30:33,769 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 19:30:33,769 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 19:30:41,910 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 496CEF66-C168-7B32-8043-845CC424DE8A Response: {'code': 'ServiceUnavailable', 'requestid': '496CEF66-C168-7B32-8043-845CC424DE8A', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 496CEF66-C168-7B32-8043-845CC424DE8A)
2025-08-10 19:30:41,910 - INFO - 开始处理日期: 2025-08-10
2025-08-10 19:30:41,910 - INFO - Request Parameters - Page 1:
2025-08-10 19:30:41,910 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 19:30:41,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 19:30:47,129 - INFO - Response - Page 1:
2025-08-10 19:30:47,129 - INFO - 第 1 页获取到 1 条记录
2025-08-10 19:30:47,644 - INFO - 查询完成，共获取到 1 条记录
2025-08-10 19:30:47,644 - INFO - 获取到 1 条表单数据
2025-08-10 19:30:47,644 - INFO - 当前日期 2025-08-10 有 1 条MySQL数据需要处理
2025-08-10 19:30:47,644 - INFO - 日期 2025-08-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-10 19:30:47,644 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-10 19:31:47,655 - INFO - 开始同步昨天与今天的销售数据: 2025-08-09 至 2025-08-10
2025-08-10 19:31:47,655 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-10 19:31:47,655 - INFO - 查询参数: ('2025-08-09', '2025-08-10')
2025-08-10 19:31:47,826 - INFO - MySQL查询成功，时间段: 2025-08-09 至 2025-08-10，共获取 450 条记录
2025-08-10 19:31:47,826 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 19:31:47,826 - INFO - 开始处理日期: 2025-08-09
2025-08-10 19:31:47,826 - INFO - Request Parameters - Page 1:
2025-08-10 19:31:47,826 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 19:31:47,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 19:31:55,951 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: C11A4AC3-E566-7FC1-B2EA-E49F8D9622BD Response: {'code': 'ServiceUnavailable', 'requestid': 'C11A4AC3-E566-7FC1-B2EA-E49F8D9622BD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: C11A4AC3-E566-7FC1-B2EA-E49F8D9622BD)
2025-08-10 19:31:55,951 - INFO - 开始处理日期: 2025-08-10
2025-08-10 19:31:55,951 - INFO - Request Parameters - Page 1:
2025-08-10 19:31:55,951 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 19:31:55,951 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 19:31:56,545 - INFO - Response - Page 1:
2025-08-10 19:31:56,545 - INFO - 第 1 页获取到 1 条记录
2025-08-10 19:31:57,061 - INFO - 查询完成，共获取到 1 条记录
2025-08-10 19:31:57,061 - INFO - 获取到 1 条表单数据
2025-08-10 19:31:57,061 - INFO - 当前日期 2025-08-10 有 1 条MySQL数据需要处理
2025-08-10 19:31:57,061 - INFO - 日期 2025-08-10 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-10 19:31:57,061 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-10 19:31:57,061 - INFO - 同步完成
2025-08-10 22:30:33,069 - INFO - 使用默认增量同步（当天更新数据）
2025-08-10 22:30:33,069 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-10 22:30:33,069 - INFO - 查询参数: ('2025-08-10',)
2025-08-10 22:30:33,257 - INFO - MySQL查询成功，增量数据（日期: 2025-08-10），共获取 187 条记录
2025-08-10 22:30:33,257 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 22:30:33,257 - INFO - 开始处理日期: 2025-08-09
2025-08-10 22:30:33,257 - INFO - Request Parameters - Page 1:
2025-08-10 22:30:33,257 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:30:33,257 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:30:41,377 - ERROR - 处理日期 2025-08-09 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 88D6089E-E60E-790A-AE56-7E5C5EE5A988 Response: {'code': 'ServiceUnavailable', 'requestid': '88D6089E-E60E-790A-AE56-7E5C5EE5A988', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 88D6089E-E60E-790A-AE56-7E5C5EE5A988)
2025-08-10 22:30:41,377 - INFO - 开始处理日期: 2025-08-10
2025-08-10 22:30:41,377 - INFO - Request Parameters - Page 1:
2025-08-10 22:30:41,377 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:30:41,377 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:30:49,502 - ERROR - 处理日期 2025-08-10 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-F76B-7F34-A74C-B0B94107EF41 Response: {'code': 'ServiceUnavailable', 'requestid': '********-F76B-7F34-A74C-B0B94107EF41', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-F76B-7F34-A74C-B0B94107EF41)
2025-08-10 22:30:49,502 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-10 22:31:49,512 - INFO - 开始同步昨天与今天的销售数据: 2025-08-09 至 2025-08-10
2025-08-10 22:31:49,512 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-10 22:31:49,512 - INFO - 查询参数: ('2025-08-09', '2025-08-10')
2025-08-10 22:31:49,684 - INFO - MySQL查询成功，时间段: 2025-08-09 至 2025-08-10，共获取 504 条记录
2025-08-10 22:31:49,684 - INFO - 获取到 2 个日期需要处理: ['2025-08-09', '2025-08-10']
2025-08-10 22:31:49,684 - INFO - 开始处理日期: 2025-08-09
2025-08-10 22:31:49,684 - INFO - Request Parameters - Page 1:
2025-08-10 22:31:49,684 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:31:49,684 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:31:50,574 - INFO - Response - Page 1:
2025-08-10 22:31:50,574 - INFO - 第 1 页获取到 50 条记录
2025-08-10 22:31:51,090 - INFO - Request Parameters - Page 2:
2025-08-10 22:31:51,090 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:31:51,090 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:31:51,824 - INFO - Response - Page 2:
2025-08-10 22:31:51,824 - INFO - 第 2 页获取到 50 条记录
2025-08-10 22:31:52,340 - INFO - Request Parameters - Page 3:
2025-08-10 22:31:52,340 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:31:52,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:31:53,090 - INFO - Response - Page 3:
2025-08-10 22:31:53,090 - INFO - 第 3 页获取到 50 条记录
2025-08-10 22:31:53,590 - INFO - Request Parameters - Page 4:
2025-08-10 22:31:53,590 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:31:53,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:31:59,199 - INFO - Response - Page 4:
2025-08-10 22:31:59,199 - INFO - 第 4 页获取到 50 条记录
2025-08-10 22:31:59,715 - INFO - Request Parameters - Page 5:
2025-08-10 22:31:59,715 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:31:59,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:32:00,590 - INFO - Response - Page 5:
2025-08-10 22:32:00,590 - INFO - 第 5 页获取到 50 条记录
2025-08-10 22:32:01,106 - INFO - Request Parameters - Page 6:
2025-08-10 22:32:01,106 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:32:01,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:32:01,918 - INFO - Response - Page 6:
2025-08-10 22:32:01,918 - INFO - 第 6 页获取到 50 条记录
2025-08-10 22:32:02,434 - INFO - Request Parameters - Page 7:
2025-08-10 22:32:02,434 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:32:02,434 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:32:03,199 - INFO - Response - Page 7:
2025-08-10 22:32:03,199 - INFO - 第 7 页获取到 50 条记录
2025-08-10 22:32:03,715 - INFO - Request Parameters - Page 8:
2025-08-10 22:32:03,715 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:32:03,715 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:32:04,465 - INFO - Response - Page 8:
2025-08-10 22:32:04,465 - INFO - 第 8 页获取到 50 条记录
2025-08-10 22:32:04,965 - INFO - Request Parameters - Page 9:
2025-08-10 22:32:04,965 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:32:04,965 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754668800000, 1754755199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:32:05,652 - INFO - Response - Page 9:
2025-08-10 22:32:05,652 - INFO - 第 9 页获取到 36 条记录
2025-08-10 22:32:06,152 - INFO - 查询完成，共获取到 436 条记录
2025-08-10 22:32:06,152 - INFO - 获取到 436 条表单数据
2025-08-10 22:32:06,152 - INFO - 当前日期 2025-08-09 有 439 条MySQL数据需要处理
2025-08-10 22:32:06,168 - INFO - 开始批量插入 3 条新记录
2025-08-10 22:32:06,340 - INFO - 批量插入响应状态码: 200
2025-08-10 22:32:06,340 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 14:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '54175012-8FE4-7D51-8ADC-77F719B48FA8', 'x-acs-trace-id': 'd1bb5e361ecdb332eddbf1d1e7c0c426', 'etag': '1xa2FQVN8EwyXBXOk/wjRRg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 22:32:06,340 - INFO - 批量插入响应体: {'result': ['FINST-74766M71MWUXVT2VD80ZC7HICN292CAN8S5EMDM', 'FINST-74766M71MWUXVT2VD80ZC7HICN292CAN8S5EMEM', 'FINST-74766M71MWUXVT2VD80ZC7HICN292CAN8S5EMFM']}
2025-08-10 22:32:06,340 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-10 22:32:06,340 - INFO - 成功插入的数据ID: ['FINST-74766M71MWUXVT2VD80ZC7HICN292CAN8S5EMDM', 'FINST-74766M71MWUXVT2VD80ZC7HICN292CAN8S5EMEM', 'FINST-74766M71MWUXVT2VD80ZC7HICN292CAN8S5EMFM']
2025-08-10 22:32:11,356 - INFO - 批量插入完成，共 3 条记录
2025-08-10 22:32:11,356 - INFO - 日期 2025-08-09 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-08-10 22:32:11,356 - INFO - 开始处理日期: 2025-08-10
2025-08-10 22:32:11,356 - INFO - Request Parameters - Page 1:
2025-08-10 22:32:11,356 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-10 22:32:11,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754755200000, 1754841599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-10 22:32:11,887 - INFO - Response - Page 1:
2025-08-10 22:32:11,887 - INFO - 第 1 页获取到 1 条记录
2025-08-10 22:32:12,387 - INFO - 查询完成，共获取到 1 条记录
2025-08-10 22:32:12,387 - INFO - 获取到 1 条表单数据
2025-08-10 22:32:12,387 - INFO - 当前日期 2025-08-10 有 50 条MySQL数据需要处理
2025-08-10 22:32:12,387 - INFO - 开始批量插入 49 条新记录
2025-08-10 22:32:12,652 - INFO - 批量插入响应状态码: 200
2025-08-10 22:32:12,652 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 10 Aug 2025 14:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2364', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5F1E4B2A-881F-74DC-8D7B-6C3F453703AF', 'x-acs-trace-id': '9d925ea1c50dd1d6d846b5b7578118d4', 'etag': '2PnDXL/ne8fAvif4gr6dDLg4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-10 22:32:12,652 - INFO - 批量插入响应体: {'result': ['FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMYE', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMZE', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM0F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM1F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM2F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM3F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM4F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM5F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM6F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM7F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM8F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM9F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMAF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMBF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMCF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMDF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMEF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMFF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMGF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMHF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMIF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMJF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMKF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMLF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMMF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMNF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMOF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMPF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMQF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMRF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMSF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMTF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMUF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMVF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMWF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMXF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMYF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMZF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM0G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM1G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM2G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM3G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM4G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM5G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM6G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM7G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM8G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM9G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMAG']}
2025-08-10 22:32:12,652 - INFO - 批量插入表单数据成功，批次 1，共 49 条记录
2025-08-10 22:32:12,652 - INFO - 成功插入的数据ID: ['FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMYE', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMZE', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM0F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM1F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM2F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM3F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM4F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM5F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM6F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM7F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM8F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM9F', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMAF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMBF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMCF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMDF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMEF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMFF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMGF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMHF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMIF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMJF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMKF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMLF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMMF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMNF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMOF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMPF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMQF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMRF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMSF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMTF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMUF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMVF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMWF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMXF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMYF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMZF', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM0G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM1G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM2G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM3G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM4G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM5G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM6G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM7G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM8G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EM9G', 'FINST-GK866F817SUXV70HAMJLK6K3U9843Q5S8S5EMAG']
2025-08-10 22:32:17,668 - INFO - 批量插入完成，共 49 条记录
2025-08-10 22:32:17,668 - INFO - 日期 2025-08-10 处理完成 - 更新: 0 条，插入: 49 条，错误: 0 条
2025-08-10 22:32:17,668 - INFO - 数据同步完成！更新: 0 条，插入: 52 条，错误: 0 条
2025-08-10 22:32:17,668 - INFO - 同步完成
