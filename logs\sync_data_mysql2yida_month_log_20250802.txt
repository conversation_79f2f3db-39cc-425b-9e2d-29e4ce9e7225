2025-08-02 00:00:02,624 - INFO - =================使用默认全量同步=============
2025-08-02 00:00:04,905 - INFO - MySQL查询成功，共获取 4748 条记录
2025-08-02 00:00:04,905 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-02 00:00:04,936 - INFO - 开始处理日期: 2025-01
2025-08-02 00:00:04,936 - INFO - Request Parameters - Page 1:
2025-08-02 00:00:04,936 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:04,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:06,577 - INFO - Response - Page 1:
2025-08-02 00:00:06,780 - INFO - 第 1 页获取到 100 条记录
2025-08-02 00:00:06,780 - INFO - Request Parameters - Page 2:
2025-08-02 00:00:06,780 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:06,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:07,311 - INFO - Response - Page 2:
2025-08-02 00:00:07,514 - INFO - 第 2 页获取到 100 条记录
2025-08-02 00:00:07,514 - INFO - Request Parameters - Page 3:
2025-08-02 00:00:07,514 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:07,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:08,014 - INFO - Response - Page 3:
2025-08-02 00:00:08,217 - INFO - 第 3 页获取到 100 条记录
2025-08-02 00:00:08,217 - INFO - Request Parameters - Page 4:
2025-08-02 00:00:08,217 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:08,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:08,764 - INFO - Response - Page 4:
2025-08-02 00:00:08,967 - INFO - 第 4 页获取到 100 条记录
2025-08-02 00:00:08,967 - INFO - Request Parameters - Page 5:
2025-08-02 00:00:08,967 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:08,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:09,514 - INFO - Response - Page 5:
2025-08-02 00:00:09,717 - INFO - 第 5 页获取到 100 条记录
2025-08-02 00:00:09,717 - INFO - Request Parameters - Page 6:
2025-08-02 00:00:09,717 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:09,717 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:10,233 - INFO - Response - Page 6:
2025-08-02 00:00:10,436 - INFO - 第 6 页获取到 100 条记录
2025-08-02 00:00:10,436 - INFO - Request Parameters - Page 7:
2025-08-02 00:00:10,436 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:10,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:10,905 - INFO - Response - Page 7:
2025-08-02 00:00:11,108 - INFO - 第 7 页获取到 82 条记录
2025-08-02 00:00:11,108 - INFO - 查询完成，共获取到 682 条记录
2025-08-02 00:00:11,108 - INFO - 获取到 682 条表单数据
2025-08-02 00:00:11,108 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-02 00:00:11,124 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 00:00:11,124 - INFO - 开始处理日期: 2025-02
2025-08-02 00:00:11,124 - INFO - Request Parameters - Page 1:
2025-08-02 00:00:11,124 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:11,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:11,639 - INFO - Response - Page 1:
2025-08-02 00:00:11,842 - INFO - 第 1 页获取到 100 条记录
2025-08-02 00:00:11,842 - INFO - Request Parameters - Page 2:
2025-08-02 00:00:11,842 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:11,842 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:12,499 - INFO - Response - Page 2:
2025-08-02 00:00:12,702 - INFO - 第 2 页获取到 100 条记录
2025-08-02 00:00:12,702 - INFO - Request Parameters - Page 3:
2025-08-02 00:00:12,702 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:12,702 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:13,217 - INFO - Response - Page 3:
2025-08-02 00:00:13,421 - INFO - 第 3 页获取到 100 条记录
2025-08-02 00:00:13,421 - INFO - Request Parameters - Page 4:
2025-08-02 00:00:13,421 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:13,421 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:14,014 - INFO - Response - Page 4:
2025-08-02 00:00:14,217 - INFO - 第 4 页获取到 100 条记录
2025-08-02 00:00:14,217 - INFO - Request Parameters - Page 5:
2025-08-02 00:00:14,217 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:14,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:14,671 - INFO - Response - Page 5:
2025-08-02 00:00:14,874 - INFO - 第 5 页获取到 100 条记录
2025-08-02 00:00:14,874 - INFO - Request Parameters - Page 6:
2025-08-02 00:00:14,874 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:14,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:15,467 - INFO - Response - Page 6:
2025-08-02 00:00:15,671 - INFO - 第 6 页获取到 100 条记录
2025-08-02 00:00:15,671 - INFO - Request Parameters - Page 7:
2025-08-02 00:00:15,671 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:15,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:16,217 - INFO - Response - Page 7:
2025-08-02 00:00:16,421 - INFO - 第 7 页获取到 70 条记录
2025-08-02 00:00:16,421 - INFO - 查询完成，共获取到 670 条记录
2025-08-02 00:00:16,421 - INFO - 获取到 670 条表单数据
2025-08-02 00:00:16,421 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-02 00:00:16,436 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 00:00:16,436 - INFO - 开始处理日期: 2025-03
2025-08-02 00:00:16,436 - INFO - Request Parameters - Page 1:
2025-08-02 00:00:16,436 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:16,436 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:16,999 - INFO - Response - Page 1:
2025-08-02 00:00:17,202 - INFO - 第 1 页获取到 100 条记录
2025-08-02 00:00:17,202 - INFO - Request Parameters - Page 2:
2025-08-02 00:00:17,202 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:17,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:17,733 - INFO - Response - Page 2:
2025-08-02 00:00:17,936 - INFO - 第 2 页获取到 100 条记录
2025-08-02 00:00:17,936 - INFO - Request Parameters - Page 3:
2025-08-02 00:00:17,936 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:17,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:18,467 - INFO - Response - Page 3:
2025-08-02 00:00:18,671 - INFO - 第 3 页获取到 100 条记录
2025-08-02 00:00:18,671 - INFO - Request Parameters - Page 4:
2025-08-02 00:00:18,671 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:18,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:19,124 - INFO - Response - Page 4:
2025-08-02 00:00:19,327 - INFO - 第 4 页获取到 100 条记录
2025-08-02 00:00:19,327 - INFO - Request Parameters - Page 5:
2025-08-02 00:00:19,327 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:19,327 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:19,796 - INFO - Response - Page 5:
2025-08-02 00:00:19,999 - INFO - 第 5 页获取到 100 条记录
2025-08-02 00:00:19,999 - INFO - Request Parameters - Page 6:
2025-08-02 00:00:19,999 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:19,999 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:20,592 - INFO - Response - Page 6:
2025-08-02 00:00:20,796 - INFO - 第 6 页获取到 100 条记录
2025-08-02 00:00:20,796 - INFO - Request Parameters - Page 7:
2025-08-02 00:00:20,796 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:20,796 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:21,296 - INFO - Response - Page 7:
2025-08-02 00:00:21,499 - INFO - 第 7 页获取到 61 条记录
2025-08-02 00:00:21,499 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 00:00:21,499 - INFO - 获取到 661 条表单数据
2025-08-02 00:00:21,499 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-02 00:00:21,514 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 00:00:21,514 - INFO - 开始处理日期: 2025-04
2025-08-02 00:00:21,514 - INFO - Request Parameters - Page 1:
2025-08-02 00:00:21,514 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:21,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:21,999 - INFO - Response - Page 1:
2025-08-02 00:00:22,202 - INFO - 第 1 页获取到 100 条记录
2025-08-02 00:00:22,202 - INFO - Request Parameters - Page 2:
2025-08-02 00:00:22,202 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:22,202 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:22,733 - INFO - Response - Page 2:
2025-08-02 00:00:22,936 - INFO - 第 2 页获取到 100 条记录
2025-08-02 00:00:22,936 - INFO - Request Parameters - Page 3:
2025-08-02 00:00:22,936 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:22,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:23,468 - INFO - Response - Page 3:
2025-08-02 00:00:23,671 - INFO - 第 3 页获取到 100 条记录
2025-08-02 00:00:23,671 - INFO - Request Parameters - Page 4:
2025-08-02 00:00:23,671 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:23,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:24,139 - INFO - Response - Page 4:
2025-08-02 00:00:24,343 - INFO - 第 4 页获取到 100 条记录
2025-08-02 00:00:24,343 - INFO - Request Parameters - Page 5:
2025-08-02 00:00:24,343 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:24,343 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:24,889 - INFO - Response - Page 5:
2025-08-02 00:00:25,093 - INFO - 第 5 页获取到 100 条记录
2025-08-02 00:00:25,093 - INFO - Request Parameters - Page 6:
2025-08-02 00:00:25,093 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:25,093 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:25,577 - INFO - Response - Page 6:
2025-08-02 00:00:25,780 - INFO - 第 6 页获取到 100 条记录
2025-08-02 00:00:25,780 - INFO - Request Parameters - Page 7:
2025-08-02 00:00:25,780 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:25,780 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:26,311 - INFO - Response - Page 7:
2025-08-02 00:00:26,514 - INFO - 第 7 页获取到 56 条记录
2025-08-02 00:00:26,514 - INFO - 查询完成，共获取到 656 条记录
2025-08-02 00:00:26,514 - INFO - 获取到 656 条表单数据
2025-08-02 00:00:26,514 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-02 00:00:26,530 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 00:00:26,530 - INFO - 开始处理日期: 2025-05
2025-08-02 00:00:26,530 - INFO - Request Parameters - Page 1:
2025-08-02 00:00:26,530 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:26,530 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:27,046 - INFO - Response - Page 1:
2025-08-02 00:00:27,249 - INFO - 第 1 页获取到 100 条记录
2025-08-02 00:00:27,249 - INFO - Request Parameters - Page 2:
2025-08-02 00:00:27,249 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:27,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:27,749 - INFO - Response - Page 2:
2025-08-02 00:00:27,952 - INFO - 第 2 页获取到 100 条记录
2025-08-02 00:00:27,952 - INFO - Request Parameters - Page 3:
2025-08-02 00:00:27,952 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:27,952 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:28,530 - INFO - Response - Page 3:
2025-08-02 00:00:28,733 - INFO - 第 3 页获取到 100 条记录
2025-08-02 00:00:28,733 - INFO - Request Parameters - Page 4:
2025-08-02 00:00:28,733 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:28,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:29,171 - INFO - Response - Page 4:
2025-08-02 00:00:29,374 - INFO - 第 4 页获取到 100 条记录
2025-08-02 00:00:29,374 - INFO - Request Parameters - Page 5:
2025-08-02 00:00:29,374 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:29,374 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:29,843 - INFO - Response - Page 5:
2025-08-02 00:00:30,046 - INFO - 第 5 页获取到 100 条记录
2025-08-02 00:00:30,046 - INFO - Request Parameters - Page 6:
2025-08-02 00:00:30,046 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:30,046 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:30,655 - INFO - Response - Page 6:
2025-08-02 00:00:30,858 - INFO - 第 6 页获取到 100 条记录
2025-08-02 00:00:30,858 - INFO - Request Parameters - Page 7:
2025-08-02 00:00:30,858 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:30,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:31,296 - INFO - Response - Page 7:
2025-08-02 00:00:31,499 - INFO - 第 7 页获取到 65 条记录
2025-08-02 00:00:31,499 - INFO - 查询完成，共获取到 665 条记录
2025-08-02 00:00:31,499 - INFO - 获取到 665 条表单数据
2025-08-02 00:00:31,499 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-02 00:00:31,514 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 00:00:31,514 - INFO - 开始处理日期: 2025-06
2025-08-02 00:00:31,514 - INFO - Request Parameters - Page 1:
2025-08-02 00:00:31,514 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:31,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:32,108 - INFO - Response - Page 1:
2025-08-02 00:00:32,311 - INFO - 第 1 页获取到 100 条记录
2025-08-02 00:00:32,311 - INFO - Request Parameters - Page 2:
2025-08-02 00:00:32,311 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:32,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:32,921 - INFO - Response - Page 2:
2025-08-02 00:00:33,124 - INFO - 第 2 页获取到 100 条记录
2025-08-02 00:00:33,124 - INFO - Request Parameters - Page 3:
2025-08-02 00:00:33,124 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:33,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:33,686 - INFO - Response - Page 3:
2025-08-02 00:00:33,889 - INFO - 第 3 页获取到 100 条记录
2025-08-02 00:00:33,889 - INFO - Request Parameters - Page 4:
2025-08-02 00:00:33,889 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:33,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:34,561 - INFO - Response - Page 4:
2025-08-02 00:00:34,765 - INFO - 第 4 页获取到 100 条记录
2025-08-02 00:00:34,765 - INFO - Request Parameters - Page 5:
2025-08-02 00:00:34,765 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:34,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:35,296 - INFO - Response - Page 5:
2025-08-02 00:00:35,499 - INFO - 第 5 页获取到 100 条记录
2025-08-02 00:00:35,499 - INFO - Request Parameters - Page 6:
2025-08-02 00:00:35,499 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:35,499 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:36,046 - INFO - Response - Page 6:
2025-08-02 00:00:36,249 - INFO - 第 6 页获取到 100 条记录
2025-08-02 00:00:36,249 - INFO - Request Parameters - Page 7:
2025-08-02 00:00:36,249 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:36,249 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:36,765 - INFO - Response - Page 7:
2025-08-02 00:00:36,968 - INFO - 第 7 页获取到 61 条记录
2025-08-02 00:00:36,968 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 00:00:36,968 - INFO - 获取到 661 条表单数据
2025-08-02 00:00:36,968 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-02 00:00:36,983 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 00:00:36,983 - INFO - 开始处理日期: 2025-07
2025-08-02 00:00:36,983 - INFO - Request Parameters - Page 1:
2025-08-02 00:00:36,983 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:36,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:37,515 - INFO - Response - Page 1:
2025-08-02 00:00:37,718 - INFO - 第 1 页获取到 100 条记录
2025-08-02 00:00:37,718 - INFO - Request Parameters - Page 2:
2025-08-02 00:00:37,718 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:37,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:38,265 - INFO - Response - Page 2:
2025-08-02 00:00:38,468 - INFO - 第 2 页获取到 100 条记录
2025-08-02 00:00:38,468 - INFO - Request Parameters - Page 3:
2025-08-02 00:00:38,468 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:38,468 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:38,968 - INFO - Response - Page 3:
2025-08-02 00:00:39,171 - INFO - 第 3 页获取到 100 条记录
2025-08-02 00:00:39,171 - INFO - Request Parameters - Page 4:
2025-08-02 00:00:39,171 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:39,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:39,671 - INFO - Response - Page 4:
2025-08-02 00:00:39,874 - INFO - 第 4 页获取到 100 条记录
2025-08-02 00:00:39,874 - INFO - Request Parameters - Page 5:
2025-08-02 00:00:39,874 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:39,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:40,374 - INFO - Response - Page 5:
2025-08-02 00:00:40,577 - INFO - 第 5 页获取到 100 条记录
2025-08-02 00:00:40,577 - INFO - Request Parameters - Page 6:
2025-08-02 00:00:40,577 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:40,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:41,108 - INFO - Response - Page 6:
2025-08-02 00:00:41,311 - INFO - 第 6 页获取到 100 条记录
2025-08-02 00:00:41,311 - INFO - Request Parameters - Page 7:
2025-08-02 00:00:41,311 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:41,311 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:41,655 - INFO - Response - Page 7:
2025-08-02 00:00:41,858 - INFO - 第 7 页获取到 19 条记录
2025-08-02 00:00:41,858 - INFO - 查询完成，共获取到 619 条记录
2025-08-02 00:00:41,858 - INFO - 获取到 619 条表单数据
2025-08-02 00:00:41,858 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-02 00:00:41,874 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 00:00:41,874 - INFO - 开始处理日期: 2025-08
2025-08-02 00:00:41,874 - INFO - Request Parameters - Page 1:
2025-08-02 00:00:41,874 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 00:00:41,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 00:00:42,108 - INFO - Response - Page 1:
2025-08-02 00:00:42,327 - INFO - 第 1 页获取到 4 条记录
2025-08-02 00:00:42,327 - INFO - 查询完成，共获取到 4 条记录
2025-08-02 00:00:42,327 - INFO - 获取到 4 条表单数据
2025-08-02 00:00:42,327 - INFO - 当前日期 2025-08 有 134 条MySQL数据需要处理
2025-08-02 00:00:42,327 - INFO - 开始批量插入 130 条新记录
2025-08-02 00:00:42,608 - INFO - 批量插入响应状态码: 200
2025-08-02 00:00:42,608 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 16:00:42 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1B35DAAA-ABAA-7E5C-970A-044307B4D32F', 'x-acs-trace-id': 'ffbb36e26772e9e8d36640f69062d7f9', 'etag': '4M8uI9c+lgVy9X4hCHgh8OA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 00:00:42,608 - INFO - 批量插入响应体: {'result': ['FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMD3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDME3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMF3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMG3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMH3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMI3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMJ3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMK3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDML3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMM3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMN3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMO3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMP3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMQ3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMR3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMS3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMT3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMU3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMV3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMW3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMX3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMY3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMZ3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM04', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM14', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM24', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM34', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM44', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM54', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM64', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM74', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM84', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM94', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMA4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMB4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMC4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMD4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDME4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMF4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMG4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMH4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMI4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMJ4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMK4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDML4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMM4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMN4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMO4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMP4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMQ4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMR4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMS4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMT4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMU4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMV4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMW4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMX4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMY4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMZ4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM05', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM15', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM25', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM35', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM45', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM55', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM65', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM75', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM85', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM95', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMA5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMB5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMC5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMD5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDME5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMF5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMG5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMH5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMI5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMJ5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMK5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDML5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMM5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMN5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMO5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMP5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMQ5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMR5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMS5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMT5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMU5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMV5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMW5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMX5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMY5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMZ5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM06', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM16', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM26', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM36', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM46']}
2025-08-02 00:00:42,608 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-08-02 00:00:42,608 - INFO - 成功插入的数据ID: ['FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMD3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDME3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMF3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMG3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMH3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMI3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMJ3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMK3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDML3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMM3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMN3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMO3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMP3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMQ3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMR3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMS3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMT3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMU3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMV3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMW3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMX3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMY3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMZ3', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM04', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM14', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM24', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM34', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM44', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM54', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM64', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM74', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM84', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM94', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMA4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMB4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMC4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMD4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDME4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMF4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMG4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMH4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMI4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMJ4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMK4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDML4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMM4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMN4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMO4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMP4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMQ4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMR4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMS4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMT4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMU4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMV4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMW4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMX4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMY4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMZ4', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM05', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM15', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM25', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM35', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM45', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM55', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM65', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM75', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM85', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM95', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMA5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMB5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMC5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMD5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDME5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMF5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMG5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMH5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMI5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMJ5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMK5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDML5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMM5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMN5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMO5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMP5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMQ5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMR5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMS5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMT5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMU5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMV5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMW5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMX5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMY5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDMZ5', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM06', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM16', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM26', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM36', 'FINST-3PF66X617MMXXMXD8BMJY8UFJO163O9XF0TDM46']
2025-08-02 00:00:45,827 - INFO - 批量插入响应状态码: 200
2025-08-02 00:00:45,827 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 16:00:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1452', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E362F4F2-FC69-74AA-827C-1A7D7989CAD3', 'x-acs-trace-id': 'bd5e7c85eaf10e76256adbf45d9d2a92', 'etag': '1AeaMKuTlpv41CRvgQRxVjQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 00:00:45,827 - INFO - 批量插入响应体: {'result': ['FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMY8', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMZ8', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM09', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM19', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM29', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM39', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM49', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM59', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM69', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM79', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM89', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM99', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMA9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMB9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMC9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMD9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDME9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMF9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMG9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMH9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMI9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMJ9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMK9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDML9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMM9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMN9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMO9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMP9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMQ9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMR9']}
2025-08-02 00:00:45,827 - INFO - 批量插入表单数据成功，批次 2，共 30 条记录
2025-08-02 00:00:45,827 - INFO - 成功插入的数据ID: ['FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMY8', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMZ8', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM09', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM19', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM29', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM39', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM49', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM59', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM69', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM79', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM89', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDM99', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMA9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMB9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMC9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMD9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDME9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMF9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMG9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMH9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMI9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMJ9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMK9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDML9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMM9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMN9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMO9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMP9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMQ9', 'FINST-OPC666D1DTLX9M5LF7INDCIB1DIA21RZF0TDMR9']
2025-08-02 00:00:48,843 - INFO - 批量插入完成，共 130 条记录
2025-08-02 00:00:48,843 - INFO - 日期 2025-08 处理完成 - 更新: 0 条，插入: 130 条，错误: 0 条
2025-08-02 00:00:48,843 - INFO - 数据同步完成！更新: 0 条，插入: 130 条，错误: 0 条
2025-08-02 00:00:48,843 - INFO - =================同步完成====================
2025-08-02 03:00:02,550 - INFO - =================使用默认全量同步=============
2025-08-02 03:00:04,769 - INFO - MySQL查询成功，共获取 4761 条记录
2025-08-02 03:00:04,769 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-02 03:00:04,816 - INFO - 开始处理日期: 2025-01
2025-08-02 03:00:04,816 - INFO - Request Parameters - Page 1:
2025-08-02 03:00:04,816 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:04,816 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:06,347 - INFO - Response - Page 1:
2025-08-02 03:00:06,550 - INFO - 第 1 页获取到 100 条记录
2025-08-02 03:00:06,550 - INFO - Request Parameters - Page 2:
2025-08-02 03:00:06,550 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:06,550 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:07,534 - INFO - Response - Page 2:
2025-08-02 03:00:07,737 - INFO - 第 2 页获取到 100 条记录
2025-08-02 03:00:07,737 - INFO - Request Parameters - Page 3:
2025-08-02 03:00:07,737 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:07,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:08,237 - INFO - Response - Page 3:
2025-08-02 03:00:08,441 - INFO - 第 3 页获取到 100 条记录
2025-08-02 03:00:08,441 - INFO - Request Parameters - Page 4:
2025-08-02 03:00:08,441 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:08,441 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:08,987 - INFO - Response - Page 4:
2025-08-02 03:00:09,191 - INFO - 第 4 页获取到 100 条记录
2025-08-02 03:00:09,191 - INFO - Request Parameters - Page 5:
2025-08-02 03:00:09,191 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:09,191 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:09,722 - INFO - Response - Page 5:
2025-08-02 03:00:09,925 - INFO - 第 5 页获取到 100 条记录
2025-08-02 03:00:09,925 - INFO - Request Parameters - Page 6:
2025-08-02 03:00:09,925 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:09,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:10,487 - INFO - Response - Page 6:
2025-08-02 03:00:10,691 - INFO - 第 6 页获取到 100 条记录
2025-08-02 03:00:10,691 - INFO - Request Parameters - Page 7:
2025-08-02 03:00:10,691 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:10,691 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:11,175 - INFO - Response - Page 7:
2025-08-02 03:00:11,378 - INFO - 第 7 页获取到 82 条记录
2025-08-02 03:00:11,378 - INFO - 查询完成，共获取到 682 条记录
2025-08-02 03:00:11,378 - INFO - 获取到 682 条表单数据
2025-08-02 03:00:11,378 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-02 03:00:11,394 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 03:00:11,394 - INFO - 开始处理日期: 2025-02
2025-08-02 03:00:11,394 - INFO - Request Parameters - Page 1:
2025-08-02 03:00:11,394 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:11,394 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:11,894 - INFO - Response - Page 1:
2025-08-02 03:00:12,097 - INFO - 第 1 页获取到 100 条记录
2025-08-02 03:00:12,097 - INFO - Request Parameters - Page 2:
2025-08-02 03:00:12,097 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:12,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:12,659 - INFO - Response - Page 2:
2025-08-02 03:00:12,863 - INFO - 第 2 页获取到 100 条记录
2025-08-02 03:00:12,863 - INFO - Request Parameters - Page 3:
2025-08-02 03:00:12,863 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:12,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:13,441 - INFO - Response - Page 3:
2025-08-02 03:00:13,644 - INFO - 第 3 页获取到 100 条记录
2025-08-02 03:00:13,644 - INFO - Request Parameters - Page 4:
2025-08-02 03:00:13,644 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:13,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:14,128 - INFO - Response - Page 4:
2025-08-02 03:00:14,331 - INFO - 第 4 页获取到 100 条记录
2025-08-02 03:00:14,331 - INFO - Request Parameters - Page 5:
2025-08-02 03:00:14,331 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:14,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:14,863 - INFO - Response - Page 5:
2025-08-02 03:00:15,066 - INFO - 第 5 页获取到 100 条记录
2025-08-02 03:00:15,066 - INFO - Request Parameters - Page 6:
2025-08-02 03:00:15,066 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:15,066 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:15,628 - INFO - Response - Page 6:
2025-08-02 03:00:15,831 - INFO - 第 6 页获取到 100 条记录
2025-08-02 03:00:15,831 - INFO - Request Parameters - Page 7:
2025-08-02 03:00:15,831 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:15,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:16,316 - INFO - Response - Page 7:
2025-08-02 03:00:16,519 - INFO - 第 7 页获取到 70 条记录
2025-08-02 03:00:16,519 - INFO - 查询完成，共获取到 670 条记录
2025-08-02 03:00:16,519 - INFO - 获取到 670 条表单数据
2025-08-02 03:00:16,519 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-02 03:00:16,534 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 03:00:16,534 - INFO - 开始处理日期: 2025-03
2025-08-02 03:00:16,534 - INFO - Request Parameters - Page 1:
2025-08-02 03:00:16,534 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:16,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:17,097 - INFO - Response - Page 1:
2025-08-02 03:00:17,300 - INFO - 第 1 页获取到 100 条记录
2025-08-02 03:00:17,300 - INFO - Request Parameters - Page 2:
2025-08-02 03:00:17,300 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:17,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:17,894 - INFO - Response - Page 2:
2025-08-02 03:00:18,097 - INFO - 第 2 页获取到 100 条记录
2025-08-02 03:00:18,097 - INFO - Request Parameters - Page 3:
2025-08-02 03:00:18,097 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:18,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:18,691 - INFO - Response - Page 3:
2025-08-02 03:00:18,894 - INFO - 第 3 页获取到 100 条记录
2025-08-02 03:00:18,894 - INFO - Request Parameters - Page 4:
2025-08-02 03:00:18,894 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:18,894 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:19,425 - INFO - Response - Page 4:
2025-08-02 03:00:19,628 - INFO - 第 4 页获取到 100 条记录
2025-08-02 03:00:19,628 - INFO - Request Parameters - Page 5:
2025-08-02 03:00:19,628 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:19,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:20,113 - INFO - Response - Page 5:
2025-08-02 03:00:20,316 - INFO - 第 5 页获取到 100 条记录
2025-08-02 03:00:20,316 - INFO - Request Parameters - Page 6:
2025-08-02 03:00:20,316 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:20,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:20,816 - INFO - Response - Page 6:
2025-08-02 03:00:21,019 - INFO - 第 6 页获取到 100 条记录
2025-08-02 03:00:21,019 - INFO - Request Parameters - Page 7:
2025-08-02 03:00:21,019 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:21,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:21,519 - INFO - Response - Page 7:
2025-08-02 03:00:21,722 - INFO - 第 7 页获取到 61 条记录
2025-08-02 03:00:21,722 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 03:00:21,722 - INFO - 获取到 661 条表单数据
2025-08-02 03:00:21,722 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-02 03:00:21,738 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 03:00:21,738 - INFO - 开始处理日期: 2025-04
2025-08-02 03:00:21,738 - INFO - Request Parameters - Page 1:
2025-08-02 03:00:21,738 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:21,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:22,331 - INFO - Response - Page 1:
2025-08-02 03:00:22,534 - INFO - 第 1 页获取到 100 条记录
2025-08-02 03:00:22,534 - INFO - Request Parameters - Page 2:
2025-08-02 03:00:22,534 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:22,534 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:23,066 - INFO - Response - Page 2:
2025-08-02 03:00:23,269 - INFO - 第 2 页获取到 100 条记录
2025-08-02 03:00:23,269 - INFO - Request Parameters - Page 3:
2025-08-02 03:00:23,269 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:23,269 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:23,706 - INFO - Response - Page 3:
2025-08-02 03:00:23,910 - INFO - 第 3 页获取到 100 条记录
2025-08-02 03:00:23,910 - INFO - Request Parameters - Page 4:
2025-08-02 03:00:23,910 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:23,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:24,410 - INFO - Response - Page 4:
2025-08-02 03:00:24,613 - INFO - 第 4 页获取到 100 条记录
2025-08-02 03:00:24,613 - INFO - Request Parameters - Page 5:
2025-08-02 03:00:24,613 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:24,613 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:25,160 - INFO - Response - Page 5:
2025-08-02 03:00:25,363 - INFO - 第 5 页获取到 100 条记录
2025-08-02 03:00:25,363 - INFO - Request Parameters - Page 6:
2025-08-02 03:00:25,363 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:25,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:25,878 - INFO - Response - Page 6:
2025-08-02 03:00:26,097 - INFO - 第 6 页获取到 100 条记录
2025-08-02 03:00:26,097 - INFO - Request Parameters - Page 7:
2025-08-02 03:00:26,097 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:26,097 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:26,613 - INFO - Response - Page 7:
2025-08-02 03:00:26,816 - INFO - 第 7 页获取到 56 条记录
2025-08-02 03:00:26,816 - INFO - 查询完成，共获取到 656 条记录
2025-08-02 03:00:26,816 - INFO - 获取到 656 条表单数据
2025-08-02 03:00:26,816 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-02 03:00:26,831 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 03:00:26,831 - INFO - 开始处理日期: 2025-05
2025-08-02 03:00:26,831 - INFO - Request Parameters - Page 1:
2025-08-02 03:00:26,831 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:26,831 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:27,316 - INFO - Response - Page 1:
2025-08-02 03:00:27,519 - INFO - 第 1 页获取到 100 条记录
2025-08-02 03:00:27,519 - INFO - Request Parameters - Page 2:
2025-08-02 03:00:27,519 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:27,519 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:28,050 - INFO - Response - Page 2:
2025-08-02 03:00:28,253 - INFO - 第 2 页获取到 100 条记录
2025-08-02 03:00:28,253 - INFO - Request Parameters - Page 3:
2025-08-02 03:00:28,253 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:28,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:28,769 - INFO - Response - Page 3:
2025-08-02 03:00:28,972 - INFO - 第 3 页获取到 100 条记录
2025-08-02 03:00:28,972 - INFO - Request Parameters - Page 4:
2025-08-02 03:00:28,972 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:28,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:29,519 - INFO - Response - Page 4:
2025-08-02 03:00:29,722 - INFO - 第 4 页获取到 100 条记录
2025-08-02 03:00:29,722 - INFO - Request Parameters - Page 5:
2025-08-02 03:00:29,722 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:29,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:30,253 - INFO - Response - Page 5:
2025-08-02 03:00:30,456 - INFO - 第 5 页获取到 100 条记录
2025-08-02 03:00:30,456 - INFO - Request Parameters - Page 6:
2025-08-02 03:00:30,456 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:30,456 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:31,050 - INFO - Response - Page 6:
2025-08-02 03:00:31,253 - INFO - 第 6 页获取到 100 条记录
2025-08-02 03:00:31,253 - INFO - Request Parameters - Page 7:
2025-08-02 03:00:31,253 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:31,253 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:31,738 - INFO - Response - Page 7:
2025-08-02 03:00:31,941 - INFO - 第 7 页获取到 65 条记录
2025-08-02 03:00:31,941 - INFO - 查询完成，共获取到 665 条记录
2025-08-02 03:00:31,941 - INFO - 获取到 665 条表单数据
2025-08-02 03:00:31,941 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-02 03:00:31,956 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 03:00:31,956 - INFO - 开始处理日期: 2025-06
2025-08-02 03:00:31,956 - INFO - Request Parameters - Page 1:
2025-08-02 03:00:31,956 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:31,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:32,581 - INFO - Response - Page 1:
2025-08-02 03:00:32,785 - INFO - 第 1 页获取到 100 条记录
2025-08-02 03:00:32,785 - INFO - Request Parameters - Page 2:
2025-08-02 03:00:32,785 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:32,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:33,222 - INFO - Response - Page 2:
2025-08-02 03:00:33,425 - INFO - 第 2 页获取到 100 条记录
2025-08-02 03:00:33,425 - INFO - Request Parameters - Page 3:
2025-08-02 03:00:33,425 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:33,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:33,972 - INFO - Response - Page 3:
2025-08-02 03:00:34,175 - INFO - 第 3 页获取到 100 条记录
2025-08-02 03:00:34,175 - INFO - Request Parameters - Page 4:
2025-08-02 03:00:34,175 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:34,175 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:34,769 - INFO - Response - Page 4:
2025-08-02 03:00:34,972 - INFO - 第 4 页获取到 100 条记录
2025-08-02 03:00:34,972 - INFO - Request Parameters - Page 5:
2025-08-02 03:00:34,972 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:34,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:35,550 - INFO - Response - Page 5:
2025-08-02 03:00:35,753 - INFO - 第 5 页获取到 100 条记录
2025-08-02 03:00:35,753 - INFO - Request Parameters - Page 6:
2025-08-02 03:00:35,753 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:35,753 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:36,222 - INFO - Response - Page 6:
2025-08-02 03:00:36,425 - INFO - 第 6 页获取到 100 条记录
2025-08-02 03:00:36,425 - INFO - Request Parameters - Page 7:
2025-08-02 03:00:36,425 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:36,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:36,910 - INFO - Response - Page 7:
2025-08-02 03:00:37,113 - INFO - 第 7 页获取到 61 条记录
2025-08-02 03:00:37,113 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 03:00:37,113 - INFO - 获取到 661 条表单数据
2025-08-02 03:00:37,128 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-02 03:00:37,128 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 03:00:37,128 - INFO - 开始处理日期: 2025-07
2025-08-02 03:00:37,128 - INFO - Request Parameters - Page 1:
2025-08-02 03:00:37,128 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:37,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:37,707 - INFO - Response - Page 1:
2025-08-02 03:00:37,910 - INFO - 第 1 页获取到 100 条记录
2025-08-02 03:00:37,910 - INFO - Request Parameters - Page 2:
2025-08-02 03:00:37,910 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:37,910 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:38,425 - INFO - Response - Page 2:
2025-08-02 03:00:38,628 - INFO - 第 2 页获取到 100 条记录
2025-08-02 03:00:38,628 - INFO - Request Parameters - Page 3:
2025-08-02 03:00:38,628 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:38,628 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:39,082 - INFO - Response - Page 3:
2025-08-02 03:00:39,285 - INFO - 第 3 页获取到 100 条记录
2025-08-02 03:00:39,285 - INFO - Request Parameters - Page 4:
2025-08-02 03:00:39,285 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:39,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:39,816 - INFO - Response - Page 4:
2025-08-02 03:00:40,019 - INFO - 第 4 页获取到 100 条记录
2025-08-02 03:00:40,019 - INFO - Request Parameters - Page 5:
2025-08-02 03:00:40,019 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:40,019 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:40,519 - INFO - Response - Page 5:
2025-08-02 03:00:40,722 - INFO - 第 5 页获取到 100 条记录
2025-08-02 03:00:40,722 - INFO - Request Parameters - Page 6:
2025-08-02 03:00:40,722 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:40,722 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:41,222 - INFO - Response - Page 6:
2025-08-02 03:00:41,425 - INFO - 第 6 页获取到 100 条记录
2025-08-02 03:00:41,425 - INFO - Request Parameters - Page 7:
2025-08-02 03:00:41,425 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:41,425 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:41,738 - INFO - Response - Page 7:
2025-08-02 03:00:41,941 - INFO - 第 7 页获取到 19 条记录
2025-08-02 03:00:41,941 - INFO - 查询完成，共获取到 619 条记录
2025-08-02 03:00:41,941 - INFO - 获取到 619 条表单数据
2025-08-02 03:00:41,957 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-02 03:00:41,972 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 03:00:41,972 - INFO - 开始处理日期: 2025-08
2025-08-02 03:00:41,972 - INFO - Request Parameters - Page 1:
2025-08-02 03:00:41,972 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:41,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:42,503 - INFO - Response - Page 1:
2025-08-02 03:00:42,707 - INFO - 第 1 页获取到 100 条记录
2025-08-02 03:00:42,707 - INFO - Request Parameters - Page 2:
2025-08-02 03:00:42,707 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 03:00:42,707 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 03:00:43,113 - INFO - Response - Page 2:
2025-08-02 03:00:43,316 - INFO - 第 2 页获取到 34 条记录
2025-08-02 03:00:43,316 - INFO - 查询完成，共获取到 134 条记录
2025-08-02 03:00:43,316 - INFO - 获取到 134 条表单数据
2025-08-02 03:00:43,316 - INFO - 当前日期 2025-08 有 147 条MySQL数据需要处理
2025-08-02 03:00:43,316 - INFO - 开始批量插入 13 条新记录
2025-08-02 03:00:43,488 - INFO - 批量插入响应状态码: 200
2025-08-02 03:00:43,488 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 19:00:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '636', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '4D3CA0C8-7FE2-72C4-9574-D0454EB49F49', 'x-acs-trace-id': '7a58dffdcc67845a3770684f5733f982', 'etag': '6NBnZniYh5QEjDk8lB0/ziw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 03:00:43,488 - INFO - 批量插入响应体: {'result': ['FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDM81', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDM91', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMA1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMB1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMC1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMD1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDME1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMF1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMG1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMH1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMI1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMJ1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMK1']}
2025-08-02 03:00:43,488 - INFO - 批量插入表单数据成功，批次 1，共 13 条记录
2025-08-02 03:00:43,488 - INFO - 成功插入的数据ID: ['FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDM81', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDM91', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMA1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMB1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMC1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMD1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDME1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMF1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMG1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMH1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMI1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMJ1', 'FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMK1']
2025-08-02 03:00:46,504 - INFO - 批量插入完成，共 13 条记录
2025-08-02 03:00:46,504 - INFO - 日期 2025-08 处理完成 - 更新: 0 条，插入: 13 条，错误: 0 条
2025-08-02 03:00:46,504 - INFO - 数据同步完成！更新: 0 条，插入: 13 条，错误: 0 条
2025-08-02 03:00:46,504 - INFO - =================同步完成====================
2025-08-02 06:00:02,562 - INFO - =================使用默认全量同步=============
2025-08-02 06:00:04,760 - INFO - MySQL查询成功，共获取 4781 条记录
2025-08-02 06:00:04,760 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-02 06:00:04,807 - INFO - 开始处理日期: 2025-01
2025-08-02 06:00:04,807 - INFO - Request Parameters - Page 1:
2025-08-02 06:00:04,807 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:04,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:06,478 - INFO - Response - Page 1:
2025-08-02 06:00:06,682 - INFO - 第 1 页获取到 100 条记录
2025-08-02 06:00:06,682 - INFO - Request Parameters - Page 2:
2025-08-02 06:00:06,682 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:06,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:07,213 - INFO - Response - Page 2:
2025-08-02 06:00:07,416 - INFO - 第 2 页获取到 100 条记录
2025-08-02 06:00:07,416 - INFO - Request Parameters - Page 3:
2025-08-02 06:00:07,416 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:07,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:07,932 - INFO - Response - Page 3:
2025-08-02 06:00:08,135 - INFO - 第 3 页获取到 100 条记录
2025-08-02 06:00:08,135 - INFO - Request Parameters - Page 4:
2025-08-02 06:00:08,135 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:08,135 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:08,619 - INFO - Response - Page 4:
2025-08-02 06:00:08,822 - INFO - 第 4 页获取到 100 条记录
2025-08-02 06:00:08,822 - INFO - Request Parameters - Page 5:
2025-08-02 06:00:08,822 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:08,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:09,369 - INFO - Response - Page 5:
2025-08-02 06:00:09,572 - INFO - 第 5 页获取到 100 条记录
2025-08-02 06:00:09,572 - INFO - Request Parameters - Page 6:
2025-08-02 06:00:09,572 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:09,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:10,119 - INFO - Response - Page 6:
2025-08-02 06:00:10,322 - INFO - 第 6 页获取到 100 条记录
2025-08-02 06:00:10,322 - INFO - Request Parameters - Page 7:
2025-08-02 06:00:10,322 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:10,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:10,822 - INFO - Response - Page 7:
2025-08-02 06:00:11,025 - INFO - 第 7 页获取到 82 条记录
2025-08-02 06:00:11,025 - INFO - 查询完成，共获取到 682 条记录
2025-08-02 06:00:11,025 - INFO - 获取到 682 条表单数据
2025-08-02 06:00:11,025 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-02 06:00:11,041 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 06:00:11,041 - INFO - 开始处理日期: 2025-02
2025-08-02 06:00:11,041 - INFO - Request Parameters - Page 1:
2025-08-02 06:00:11,041 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:11,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:11,603 - INFO - Response - Page 1:
2025-08-02 06:00:11,807 - INFO - 第 1 页获取到 100 条记录
2025-08-02 06:00:11,807 - INFO - Request Parameters - Page 2:
2025-08-02 06:00:11,807 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:11,807 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:12,338 - INFO - Response - Page 2:
2025-08-02 06:00:12,541 - INFO - 第 2 页获取到 100 条记录
2025-08-02 06:00:12,541 - INFO - Request Parameters - Page 3:
2025-08-02 06:00:12,541 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:12,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:13,041 - INFO - Response - Page 3:
2025-08-02 06:00:13,244 - INFO - 第 3 页获取到 100 条记录
2025-08-02 06:00:13,244 - INFO - Request Parameters - Page 4:
2025-08-02 06:00:13,244 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:13,244 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:13,744 - INFO - Response - Page 4:
2025-08-02 06:00:13,947 - INFO - 第 4 页获取到 100 条记录
2025-08-02 06:00:13,947 - INFO - Request Parameters - Page 5:
2025-08-02 06:00:13,947 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:13,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:14,478 - INFO - Response - Page 5:
2025-08-02 06:00:14,682 - INFO - 第 5 页获取到 100 条记录
2025-08-02 06:00:14,682 - INFO - Request Parameters - Page 6:
2025-08-02 06:00:14,682 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:14,682 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:15,182 - INFO - Response - Page 6:
2025-08-02 06:00:15,385 - INFO - 第 6 页获取到 100 条记录
2025-08-02 06:00:15,385 - INFO - Request Parameters - Page 7:
2025-08-02 06:00:15,385 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:15,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:15,885 - INFO - Response - Page 7:
2025-08-02 06:00:16,088 - INFO - 第 7 页获取到 70 条记录
2025-08-02 06:00:16,088 - INFO - 查询完成，共获取到 670 条记录
2025-08-02 06:00:16,088 - INFO - 获取到 670 条表单数据
2025-08-02 06:00:16,088 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-02 06:00:16,103 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 06:00:16,103 - INFO - 开始处理日期: 2025-03
2025-08-02 06:00:16,103 - INFO - Request Parameters - Page 1:
2025-08-02 06:00:16,103 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:16,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:16,697 - INFO - Response - Page 1:
2025-08-02 06:00:16,900 - INFO - 第 1 页获取到 100 条记录
2025-08-02 06:00:16,900 - INFO - Request Parameters - Page 2:
2025-08-02 06:00:16,900 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:16,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:17,432 - INFO - Response - Page 2:
2025-08-02 06:00:17,635 - INFO - 第 2 页获取到 100 条记录
2025-08-02 06:00:17,635 - INFO - Request Parameters - Page 3:
2025-08-02 06:00:17,635 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:17,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:18,166 - INFO - Response - Page 3:
2025-08-02 06:00:18,369 - INFO - 第 3 页获取到 100 条记录
2025-08-02 06:00:18,369 - INFO - Request Parameters - Page 4:
2025-08-02 06:00:18,369 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:18,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:18,853 - INFO - Response - Page 4:
2025-08-02 06:00:19,057 - INFO - 第 4 页获取到 100 条记录
2025-08-02 06:00:19,057 - INFO - Request Parameters - Page 5:
2025-08-02 06:00:19,057 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:19,057 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:19,666 - INFO - Response - Page 5:
2025-08-02 06:00:19,869 - INFO - 第 5 页获取到 100 条记录
2025-08-02 06:00:19,869 - INFO - Request Parameters - Page 6:
2025-08-02 06:00:19,869 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:19,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:20,307 - INFO - Response - Page 6:
2025-08-02 06:00:20,510 - INFO - 第 6 页获取到 100 条记录
2025-08-02 06:00:20,510 - INFO - Request Parameters - Page 7:
2025-08-02 06:00:20,510 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:20,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:20,963 - INFO - Response - Page 7:
2025-08-02 06:00:21,166 - INFO - 第 7 页获取到 61 条记录
2025-08-02 06:00:21,166 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 06:00:21,166 - INFO - 获取到 661 条表单数据
2025-08-02 06:00:21,166 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-02 06:00:21,181 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 06:00:21,181 - INFO - 开始处理日期: 2025-04
2025-08-02 06:00:21,181 - INFO - Request Parameters - Page 1:
2025-08-02 06:00:21,181 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:21,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:21,760 - INFO - Response - Page 1:
2025-08-02 06:00:21,963 - INFO - 第 1 页获取到 100 条记录
2025-08-02 06:00:21,963 - INFO - Request Parameters - Page 2:
2025-08-02 06:00:21,963 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:21,963 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:22,463 - INFO - Response - Page 2:
2025-08-02 06:00:22,666 - INFO - 第 2 页获取到 100 条记录
2025-08-02 06:00:22,666 - INFO - Request Parameters - Page 3:
2025-08-02 06:00:22,666 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:22,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:23,119 - INFO - Response - Page 3:
2025-08-02 06:00:23,322 - INFO - 第 3 页获取到 100 条记录
2025-08-02 06:00:23,322 - INFO - Request Parameters - Page 4:
2025-08-02 06:00:23,322 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:23,322 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:23,791 - INFO - Response - Page 4:
2025-08-02 06:00:23,994 - INFO - 第 4 页获取到 100 条记录
2025-08-02 06:00:23,994 - INFO - Request Parameters - Page 5:
2025-08-02 06:00:23,994 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:23,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:24,478 - INFO - Response - Page 5:
2025-08-02 06:00:24,681 - INFO - 第 5 页获取到 100 条记录
2025-08-02 06:00:24,681 - INFO - Request Parameters - Page 6:
2025-08-02 06:00:24,681 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:24,681 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:25,197 - INFO - Response - Page 6:
2025-08-02 06:00:25,400 - INFO - 第 6 页获取到 100 条记录
2025-08-02 06:00:25,400 - INFO - Request Parameters - Page 7:
2025-08-02 06:00:25,400 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:25,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:25,869 - INFO - Response - Page 7:
2025-08-02 06:00:26,072 - INFO - 第 7 页获取到 56 条记录
2025-08-02 06:00:26,072 - INFO - 查询完成，共获取到 656 条记录
2025-08-02 06:00:26,072 - INFO - 获取到 656 条表单数据
2025-08-02 06:00:26,072 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-02 06:00:26,088 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 06:00:26,088 - INFO - 开始处理日期: 2025-05
2025-08-02 06:00:26,088 - INFO - Request Parameters - Page 1:
2025-08-02 06:00:26,088 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:26,088 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:26,650 - INFO - Response - Page 1:
2025-08-02 06:00:26,853 - INFO - 第 1 页获取到 100 条记录
2025-08-02 06:00:26,853 - INFO - Request Parameters - Page 2:
2025-08-02 06:00:26,853 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:26,853 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:27,385 - INFO - Response - Page 2:
2025-08-02 06:00:27,588 - INFO - 第 2 页获取到 100 条记录
2025-08-02 06:00:27,588 - INFO - Request Parameters - Page 3:
2025-08-02 06:00:27,588 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:27,588 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:28,010 - INFO - Response - Page 3:
2025-08-02 06:00:28,213 - INFO - 第 3 页获取到 100 条记录
2025-08-02 06:00:28,213 - INFO - Request Parameters - Page 4:
2025-08-02 06:00:28,213 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:28,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:28,822 - INFO - Response - Page 4:
2025-08-02 06:00:29,025 - INFO - 第 4 页获取到 100 条记录
2025-08-02 06:00:29,025 - INFO - Request Parameters - Page 5:
2025-08-02 06:00:29,025 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:29,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:29,525 - INFO - Response - Page 5:
2025-08-02 06:00:29,728 - INFO - 第 5 页获取到 100 条记录
2025-08-02 06:00:29,728 - INFO - Request Parameters - Page 6:
2025-08-02 06:00:29,728 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:29,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:30,197 - INFO - Response - Page 6:
2025-08-02 06:00:30,400 - INFO - 第 6 页获取到 100 条记录
2025-08-02 06:00:30,400 - INFO - Request Parameters - Page 7:
2025-08-02 06:00:30,400 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:30,400 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:30,838 - INFO - Response - Page 7:
2025-08-02 06:00:31,041 - INFO - 第 7 页获取到 65 条记录
2025-08-02 06:00:31,041 - INFO - 查询完成，共获取到 665 条记录
2025-08-02 06:00:31,041 - INFO - 获取到 665 条表单数据
2025-08-02 06:00:31,041 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-02 06:00:31,056 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 06:00:31,056 - INFO - 开始处理日期: 2025-06
2025-08-02 06:00:31,056 - INFO - Request Parameters - Page 1:
2025-08-02 06:00:31,056 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:31,056 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:31,666 - INFO - Response - Page 1:
2025-08-02 06:00:31,869 - INFO - 第 1 页获取到 100 条记录
2025-08-02 06:00:31,869 - INFO - Request Parameters - Page 2:
2025-08-02 06:00:31,869 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:31,869 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:32,416 - INFO - Response - Page 2:
2025-08-02 06:00:32,619 - INFO - 第 2 页获取到 100 条记录
2025-08-02 06:00:32,619 - INFO - Request Parameters - Page 3:
2025-08-02 06:00:32,619 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:32,619 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:33,181 - INFO - Response - Page 3:
2025-08-02 06:00:33,385 - INFO - 第 3 页获取到 100 条记录
2025-08-02 06:00:33,385 - INFO - Request Parameters - Page 4:
2025-08-02 06:00:33,385 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:33,385 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:33,822 - INFO - Response - Page 4:
2025-08-02 06:00:34,025 - INFO - 第 4 页获取到 100 条记录
2025-08-02 06:00:34,025 - INFO - Request Parameters - Page 5:
2025-08-02 06:00:34,025 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:34,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:34,525 - INFO - Response - Page 5:
2025-08-02 06:00:34,728 - INFO - 第 5 页获取到 100 条记录
2025-08-02 06:00:34,728 - INFO - Request Parameters - Page 6:
2025-08-02 06:00:34,728 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:34,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:35,260 - INFO - Response - Page 6:
2025-08-02 06:00:35,463 - INFO - 第 6 页获取到 100 条记录
2025-08-02 06:00:35,463 - INFO - Request Parameters - Page 7:
2025-08-02 06:00:35,463 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:35,463 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:35,978 - INFO - Response - Page 7:
2025-08-02 06:00:36,181 - INFO - 第 7 页获取到 61 条记录
2025-08-02 06:00:36,181 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 06:00:36,181 - INFO - 获取到 661 条表单数据
2025-08-02 06:00:36,181 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-02 06:00:36,197 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 06:00:36,197 - INFO - 开始处理日期: 2025-07
2025-08-02 06:00:36,197 - INFO - Request Parameters - Page 1:
2025-08-02 06:00:36,197 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:36,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:36,744 - INFO - Response - Page 1:
2025-08-02 06:00:36,947 - INFO - 第 1 页获取到 100 条记录
2025-08-02 06:00:36,947 - INFO - Request Parameters - Page 2:
2025-08-02 06:00:36,947 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:36,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:37,525 - INFO - Response - Page 2:
2025-08-02 06:00:37,728 - INFO - 第 2 页获取到 100 条记录
2025-08-02 06:00:37,728 - INFO - Request Parameters - Page 3:
2025-08-02 06:00:37,728 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:37,728 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:38,213 - INFO - Response - Page 3:
2025-08-02 06:00:38,416 - INFO - 第 3 页获取到 100 条记录
2025-08-02 06:00:38,416 - INFO - Request Parameters - Page 4:
2025-08-02 06:00:38,416 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:38,416 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:39,010 - INFO - Response - Page 4:
2025-08-02 06:00:39,213 - INFO - 第 4 页获取到 100 条记录
2025-08-02 06:00:39,213 - INFO - Request Parameters - Page 5:
2025-08-02 06:00:39,213 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:39,213 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:39,728 - INFO - Response - Page 5:
2025-08-02 06:00:39,931 - INFO - 第 5 页获取到 100 条记录
2025-08-02 06:00:39,931 - INFO - Request Parameters - Page 6:
2025-08-02 06:00:39,931 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:39,931 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:40,463 - INFO - Response - Page 6:
2025-08-02 06:00:40,666 - INFO - 第 6 页获取到 100 条记录
2025-08-02 06:00:40,666 - INFO - Request Parameters - Page 7:
2025-08-02 06:00:40,666 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:40,666 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:40,978 - INFO - Response - Page 7:
2025-08-02 06:00:41,181 - INFO - 第 7 页获取到 19 条记录
2025-08-02 06:00:41,181 - INFO - 查询完成，共获取到 619 条记录
2025-08-02 06:00:41,181 - INFO - 获取到 619 条表单数据
2025-08-02 06:00:41,181 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-02 06:00:41,197 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 06:00:41,197 - INFO - 开始处理日期: 2025-08
2025-08-02 06:00:41,197 - INFO - Request Parameters - Page 1:
2025-08-02 06:00:41,197 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:41,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:41,744 - INFO - Response - Page 1:
2025-08-02 06:00:41,947 - INFO - 第 1 页获取到 100 条记录
2025-08-02 06:00:41,947 - INFO - Request Parameters - Page 2:
2025-08-02 06:00:41,947 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 06:00:41,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 06:00:42,369 - INFO - Response - Page 2:
2025-08-02 06:00:42,572 - INFO - 第 2 页获取到 47 条记录
2025-08-02 06:00:42,572 - INFO - 查询完成，共获取到 147 条记录
2025-08-02 06:00:42,572 - INFO - 获取到 147 条表单数据
2025-08-02 06:00:42,572 - INFO - 当前日期 2025-08 有 167 条MySQL数据需要处理
2025-08-02 06:00:42,572 - INFO - 开始批量插入 20 条新记录
2025-08-02 06:00:42,744 - INFO - 批量插入响应状态码: 200
2025-08-02 06:00:42,744 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 22:00:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '972', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'FD709DCA-B0BC-70E0-A972-4768164861B2', 'x-acs-trace-id': '971b095f2b6837239bb5955f3440ac37', 'etag': '9ihCeVMNfwdARqS+Srg+iQQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 06:00:42,744 - INFO - 批量插入响应体: {'result': ['FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMII', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMJI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMKI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMLI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMMI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMNI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMOI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMPI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMQI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMRI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMSI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMTI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMUI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMVI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDMWI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDMXI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDMYI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDMZI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDM0J', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDM1J']}
2025-08-02 06:00:42,744 - INFO - 批量插入表单数据成功，批次 1，共 20 条记录
2025-08-02 06:00:42,744 - INFO - 成功插入的数据ID: ['FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMII', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMJI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMKI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMLI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMMI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMNI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMOI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMPI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMQI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMRI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMSI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMTI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMUI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3I8YADTDMVI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDMWI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDMXI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDMYI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDMZI', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDM0J', 'FINST-MUC66Q818AMXSZPZA14NKBPNUEVA3J8YADTDM1J']
2025-08-02 06:00:45,759 - INFO - 批量插入完成，共 20 条记录
2025-08-02 06:00:45,759 - INFO - 日期 2025-08 处理完成 - 更新: 0 条，插入: 20 条，错误: 0 条
2025-08-02 06:00:45,759 - INFO - 数据同步完成！更新: 0 条，插入: 20 条，错误: 0 条
2025-08-02 06:00:45,759 - INFO - =================同步完成====================
2025-08-02 09:00:02,575 - INFO - =================使用默认全量同步=============
2025-08-02 09:00:04,982 - INFO - MySQL查询成功，共获取 4789 条记录
2025-08-02 09:00:04,982 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-02 09:00:05,013 - INFO - 开始处理日期: 2025-01
2025-08-02 09:00:05,028 - INFO - Request Parameters - Page 1:
2025-08-02 09:00:05,028 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:05,028 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:06,200 - INFO - Response - Page 1:
2025-08-02 09:00:06,403 - INFO - 第 1 页获取到 100 条记录
2025-08-02 09:00:06,403 - INFO - Request Parameters - Page 2:
2025-08-02 09:00:06,403 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:06,403 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:07,560 - INFO - Response - Page 2:
2025-08-02 09:00:07,763 - INFO - 第 2 页获取到 100 条记录
2025-08-02 09:00:07,763 - INFO - Request Parameters - Page 3:
2025-08-02 09:00:07,763 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:07,763 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:08,388 - INFO - Response - Page 3:
2025-08-02 09:00:08,591 - INFO - 第 3 页获取到 100 条记录
2025-08-02 09:00:08,591 - INFO - Request Parameters - Page 4:
2025-08-02 09:00:08,591 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:08,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:09,148 - INFO - Response - Page 4:
2025-08-02 09:00:09,352 - INFO - 第 4 页获取到 100 条记录
2025-08-02 09:00:09,352 - INFO - Request Parameters - Page 5:
2025-08-02 09:00:09,352 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:09,352 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:09,867 - INFO - Response - Page 5:
2025-08-02 09:00:10,070 - INFO - 第 5 页获取到 100 条记录
2025-08-02 09:00:10,070 - INFO - Request Parameters - Page 6:
2025-08-02 09:00:10,070 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:10,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:10,617 - INFO - Response - Page 6:
2025-08-02 09:00:10,820 - INFO - 第 6 页获取到 100 条记录
2025-08-02 09:00:10,820 - INFO - Request Parameters - Page 7:
2025-08-02 09:00:10,820 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:10,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:11,289 - INFO - Response - Page 7:
2025-08-02 09:00:11,492 - INFO - 第 7 页获取到 82 条记录
2025-08-02 09:00:11,492 - INFO - 查询完成，共获取到 682 条记录
2025-08-02 09:00:11,492 - INFO - 获取到 682 条表单数据
2025-08-02 09:00:11,492 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-02 09:00:11,508 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 09:00:11,508 - INFO - 开始处理日期: 2025-02
2025-08-02 09:00:11,508 - INFO - Request Parameters - Page 1:
2025-08-02 09:00:11,508 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:11,508 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:12,039 - INFO - Response - Page 1:
2025-08-02 09:00:12,242 - INFO - 第 1 页获取到 100 条记录
2025-08-02 09:00:12,242 - INFO - Request Parameters - Page 2:
2025-08-02 09:00:12,242 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:12,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:12,805 - INFO - Response - Page 2:
2025-08-02 09:00:13,008 - INFO - 第 2 页获取到 100 条记录
2025-08-02 09:00:13,008 - INFO - Request Parameters - Page 3:
2025-08-02 09:00:13,008 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:13,008 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:13,586 - INFO - Response - Page 3:
2025-08-02 09:00:13,789 - INFO - 第 3 页获取到 100 条记录
2025-08-02 09:00:13,789 - INFO - Request Parameters - Page 4:
2025-08-02 09:00:13,789 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:13,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:14,273 - INFO - Response - Page 4:
2025-08-02 09:00:14,477 - INFO - 第 4 页获取到 100 条记录
2025-08-02 09:00:14,477 - INFO - Request Parameters - Page 5:
2025-08-02 09:00:14,477 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:14,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:14,977 - INFO - Response - Page 5:
2025-08-02 09:00:15,180 - INFO - 第 5 页获取到 100 条记录
2025-08-02 09:00:15,180 - INFO - Request Parameters - Page 6:
2025-08-02 09:00:15,180 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:15,180 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:15,820 - INFO - Response - Page 6:
2025-08-02 09:00:16,023 - INFO - 第 6 页获取到 100 条记录
2025-08-02 09:00:16,023 - INFO - Request Parameters - Page 7:
2025-08-02 09:00:16,023 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:16,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:16,492 - INFO - Response - Page 7:
2025-08-02 09:00:16,695 - INFO - 第 7 页获取到 70 条记录
2025-08-02 09:00:16,695 - INFO - 查询完成，共获取到 670 条记录
2025-08-02 09:00:16,695 - INFO - 获取到 670 条表单数据
2025-08-02 09:00:16,695 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-02 09:00:16,711 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 09:00:16,711 - INFO - 开始处理日期: 2025-03
2025-08-02 09:00:16,711 - INFO - Request Parameters - Page 1:
2025-08-02 09:00:16,711 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:16,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:17,211 - INFO - Response - Page 1:
2025-08-02 09:00:17,414 - INFO - 第 1 页获取到 100 条记录
2025-08-02 09:00:17,414 - INFO - Request Parameters - Page 2:
2025-08-02 09:00:17,414 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:17,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:17,961 - INFO - Response - Page 2:
2025-08-02 09:00:18,164 - INFO - 第 2 页获取到 100 条记录
2025-08-02 09:00:18,164 - INFO - Request Parameters - Page 3:
2025-08-02 09:00:18,164 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:18,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:18,727 - INFO - Response - Page 3:
2025-08-02 09:00:18,930 - INFO - 第 3 页获取到 100 条记录
2025-08-02 09:00:18,930 - INFO - Request Parameters - Page 4:
2025-08-02 09:00:18,930 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:18,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:19,586 - INFO - Response - Page 4:
2025-08-02 09:00:19,789 - INFO - 第 4 页获取到 100 条记录
2025-08-02 09:00:19,789 - INFO - Request Parameters - Page 5:
2025-08-02 09:00:19,789 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:19,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:20,258 - INFO - Response - Page 5:
2025-08-02 09:00:20,461 - INFO - 第 5 页获取到 100 条记录
2025-08-02 09:00:20,461 - INFO - Request Parameters - Page 6:
2025-08-02 09:00:20,461 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:20,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:21,039 - INFO - Response - Page 6:
2025-08-02 09:00:21,242 - INFO - 第 6 页获取到 100 条记录
2025-08-02 09:00:21,242 - INFO - Request Parameters - Page 7:
2025-08-02 09:00:21,242 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:21,242 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:21,711 - INFO - Response - Page 7:
2025-08-02 09:00:21,914 - INFO - 第 7 页获取到 61 条记录
2025-08-02 09:00:21,914 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 09:00:21,914 - INFO - 获取到 661 条表单数据
2025-08-02 09:00:21,914 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-02 09:00:21,930 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 09:00:21,930 - INFO - 开始处理日期: 2025-04
2025-08-02 09:00:21,930 - INFO - Request Parameters - Page 1:
2025-08-02 09:00:21,930 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:21,930 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:22,492 - INFO - Response - Page 1:
2025-08-02 09:00:22,695 - INFO - 第 1 页获取到 100 条记录
2025-08-02 09:00:22,695 - INFO - Request Parameters - Page 2:
2025-08-02 09:00:22,695 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:22,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:23,211 - INFO - Response - Page 2:
2025-08-02 09:00:23,414 - INFO - 第 2 页获取到 100 条记录
2025-08-02 09:00:23,414 - INFO - Request Parameters - Page 3:
2025-08-02 09:00:23,414 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:23,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:23,930 - INFO - Response - Page 3:
2025-08-02 09:00:24,133 - INFO - 第 3 页获取到 100 条记录
2025-08-02 09:00:24,133 - INFO - Request Parameters - Page 4:
2025-08-02 09:00:24,133 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:24,133 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:24,664 - INFO - Response - Page 4:
2025-08-02 09:00:24,867 - INFO - 第 4 页获取到 100 条记录
2025-08-02 09:00:24,867 - INFO - Request Parameters - Page 5:
2025-08-02 09:00:24,867 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:24,867 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:25,445 - INFO - Response - Page 5:
2025-08-02 09:00:25,648 - INFO - 第 5 页获取到 100 条记录
2025-08-02 09:00:25,648 - INFO - Request Parameters - Page 6:
2025-08-02 09:00:25,648 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:25,648 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:26,164 - INFO - Response - Page 6:
2025-08-02 09:00:26,367 - INFO - 第 6 页获取到 100 条记录
2025-08-02 09:00:26,367 - INFO - Request Parameters - Page 7:
2025-08-02 09:00:26,367 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:26,367 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:26,867 - INFO - Response - Page 7:
2025-08-02 09:00:27,070 - INFO - 第 7 页获取到 56 条记录
2025-08-02 09:00:27,070 - INFO - 查询完成，共获取到 656 条记录
2025-08-02 09:00:27,070 - INFO - 获取到 656 条表单数据
2025-08-02 09:00:27,070 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-02 09:00:27,086 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 09:00:27,086 - INFO - 开始处理日期: 2025-05
2025-08-02 09:00:27,086 - INFO - Request Parameters - Page 1:
2025-08-02 09:00:27,086 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:27,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:27,570 - INFO - Response - Page 1:
2025-08-02 09:00:27,773 - INFO - 第 1 页获取到 100 条记录
2025-08-02 09:00:27,773 - INFO - Request Parameters - Page 2:
2025-08-02 09:00:27,773 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:27,773 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:28,289 - INFO - Response - Page 2:
2025-08-02 09:00:28,492 - INFO - 第 2 页获取到 100 条记录
2025-08-02 09:00:28,492 - INFO - Request Parameters - Page 3:
2025-08-02 09:00:28,492 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:28,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:28,961 - INFO - Response - Page 3:
2025-08-02 09:00:29,164 - INFO - 第 3 页获取到 100 条记录
2025-08-02 09:00:29,164 - INFO - Request Parameters - Page 4:
2025-08-02 09:00:29,164 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:29,164 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:29,789 - INFO - Response - Page 4:
2025-08-02 09:00:29,992 - INFO - 第 4 页获取到 100 条记录
2025-08-02 09:00:29,992 - INFO - Request Parameters - Page 5:
2025-08-02 09:00:29,992 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:29,992 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:30,476 - INFO - Response - Page 5:
2025-08-02 09:00:30,680 - INFO - 第 5 页获取到 100 条记录
2025-08-02 09:00:30,680 - INFO - Request Parameters - Page 6:
2025-08-02 09:00:30,680 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:30,680 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:31,226 - INFO - Response - Page 6:
2025-08-02 09:00:31,430 - INFO - 第 6 页获取到 100 条记录
2025-08-02 09:00:31,430 - INFO - Request Parameters - Page 7:
2025-08-02 09:00:31,430 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:31,430 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:32,039 - INFO - Response - Page 7:
2025-08-02 09:00:32,242 - INFO - 第 7 页获取到 65 条记录
2025-08-02 09:00:32,242 - INFO - 查询完成，共获取到 665 条记录
2025-08-02 09:00:32,242 - INFO - 获取到 665 条表单数据
2025-08-02 09:00:32,242 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-02 09:00:32,258 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 09:00:32,258 - INFO - 开始处理日期: 2025-06
2025-08-02 09:00:32,258 - INFO - Request Parameters - Page 1:
2025-08-02 09:00:32,258 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:32,258 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:32,898 - INFO - Response - Page 1:
2025-08-02 09:00:33,101 - INFO - 第 1 页获取到 100 条记录
2025-08-02 09:00:33,101 - INFO - Request Parameters - Page 2:
2025-08-02 09:00:33,101 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:33,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:33,617 - INFO - Response - Page 2:
2025-08-02 09:00:33,820 - INFO - 第 2 页获取到 100 条记录
2025-08-02 09:00:33,820 - INFO - Request Parameters - Page 3:
2025-08-02 09:00:33,820 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:33,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:34,430 - INFO - Response - Page 3:
2025-08-02 09:00:34,633 - INFO - 第 3 页获取到 100 条记录
2025-08-02 09:00:34,633 - INFO - Request Parameters - Page 4:
2025-08-02 09:00:34,633 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:34,633 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:35,211 - INFO - Response - Page 4:
2025-08-02 09:00:35,414 - INFO - 第 4 页获取到 100 条记录
2025-08-02 09:00:35,414 - INFO - Request Parameters - Page 5:
2025-08-02 09:00:35,414 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:35,414 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:35,992 - INFO - Response - Page 5:
2025-08-02 09:00:36,195 - INFO - 第 5 页获取到 100 条记录
2025-08-02 09:00:36,195 - INFO - Request Parameters - Page 6:
2025-08-02 09:00:36,195 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:36,195 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:36,742 - INFO - Response - Page 6:
2025-08-02 09:00:36,945 - INFO - 第 6 页获取到 100 条记录
2025-08-02 09:00:36,945 - INFO - Request Parameters - Page 7:
2025-08-02 09:00:36,945 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:36,945 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:37,398 - INFO - Response - Page 7:
2025-08-02 09:00:37,601 - INFO - 第 7 页获取到 61 条记录
2025-08-02 09:00:37,601 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 09:00:37,601 - INFO - 获取到 661 条表单数据
2025-08-02 09:00:37,601 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-02 09:00:37,617 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 09:00:37,617 - INFO - 开始处理日期: 2025-07
2025-08-02 09:00:37,617 - INFO - Request Parameters - Page 1:
2025-08-02 09:00:37,617 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:37,617 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:38,195 - INFO - Response - Page 1:
2025-08-02 09:00:38,398 - INFO - 第 1 页获取到 100 条记录
2025-08-02 09:00:38,398 - INFO - Request Parameters - Page 2:
2025-08-02 09:00:38,398 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:38,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:38,945 - INFO - Response - Page 2:
2025-08-02 09:00:39,148 - INFO - 第 2 页获取到 100 条记录
2025-08-02 09:00:39,148 - INFO - Request Parameters - Page 3:
2025-08-02 09:00:39,148 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:39,148 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:39,726 - INFO - Response - Page 3:
2025-08-02 09:00:39,929 - INFO - 第 3 页获取到 100 条记录
2025-08-02 09:00:39,929 - INFO - Request Parameters - Page 4:
2025-08-02 09:00:39,929 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:39,929 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:40,492 - INFO - Response - Page 4:
2025-08-02 09:00:40,695 - INFO - 第 4 页获取到 100 条记录
2025-08-02 09:00:40,695 - INFO - Request Parameters - Page 5:
2025-08-02 09:00:40,695 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:40,695 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:41,179 - INFO - Response - Page 5:
2025-08-02 09:00:41,383 - INFO - 第 5 页获取到 100 条记录
2025-08-02 09:00:41,383 - INFO - Request Parameters - Page 6:
2025-08-02 09:00:41,383 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:41,383 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:41,883 - INFO - Response - Page 6:
2025-08-02 09:00:42,086 - INFO - 第 6 页获取到 100 条记录
2025-08-02 09:00:42,086 - INFO - Request Parameters - Page 7:
2025-08-02 09:00:42,086 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:42,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:42,523 - INFO - Response - Page 7:
2025-08-02 09:00:42,726 - INFO - 第 7 页获取到 19 条记录
2025-08-02 09:00:42,726 - INFO - 查询完成，共获取到 619 条记录
2025-08-02 09:00:42,726 - INFO - 获取到 619 条表单数据
2025-08-02 09:00:42,726 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-02 09:00:42,742 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 09:00:42,742 - INFO - 开始处理日期: 2025-08
2025-08-02 09:00:42,742 - INFO - Request Parameters - Page 1:
2025-08-02 09:00:42,742 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:42,742 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:43,258 - INFO - Response - Page 1:
2025-08-02 09:00:43,461 - INFO - 第 1 页获取到 100 条记录
2025-08-02 09:00:43,461 - INFO - Request Parameters - Page 2:
2025-08-02 09:00:43,461 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 09:00:43,461 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 09:00:44,023 - INFO - Response - Page 2:
2025-08-02 09:00:44,226 - INFO - 第 2 页获取到 67 条记录
2025-08-02 09:00:44,226 - INFO - 查询完成，共获取到 167 条记录
2025-08-02 09:00:44,226 - INFO - 获取到 167 条表单数据
2025-08-02 09:00:44,226 - INFO - 当前日期 2025-08 有 175 条MySQL数据需要处理
2025-08-02 09:00:44,226 - INFO - 开始批量插入 8 条新记录
2025-08-02 09:00:44,398 - INFO - 批量插入响应状态码: 200
2025-08-02 09:00:44,398 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 01:00:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '396', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7D8D7B97-163E-77F3-B883-7B246BC2BB9C', 'x-acs-trace-id': 'a0e7a784926c2480548e9d2ca14add8d', 'etag': '32D8D5ioKw7kg2pGTcUsTwQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 09:00:44,398 - INFO - 批量插入响应体: {'result': ['FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM5E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM6E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM7E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM8E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM9E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDMAE', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDMBE', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDMCE']}
2025-08-02 09:00:44,398 - INFO - 批量插入表单数据成功，批次 1，共 8 条记录
2025-08-02 09:00:44,398 - INFO - 成功插入的数据ID: ['FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM5E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM6E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM7E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM8E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDM9E', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDMAE', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDMBE', 'FINST-LOG66Q61MMMX4QSGB2D605224CE53KWGQJTDMCE']
2025-08-02 09:00:47,414 - INFO - 批量插入完成，共 8 条记录
2025-08-02 09:00:47,414 - INFO - 日期 2025-08 处理完成 - 更新: 0 条，插入: 8 条，错误: 0 条
2025-08-02 09:00:47,414 - INFO - 数据同步完成！更新: 0 条，插入: 8 条，错误: 0 条
2025-08-02 09:00:47,414 - INFO - =================同步完成====================
2025-08-02 12:00:01,714 - INFO - =================使用默认全量同步=============
2025-08-02 12:00:03,964 - INFO - MySQL查询成功，共获取 5104 条记录
2025-08-02 12:00:03,964 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-02 12:00:04,011 - INFO - 开始处理日期: 2025-01
2025-08-02 12:00:04,011 - INFO - Request Parameters - Page 1:
2025-08-02 12:00:04,011 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:04,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:05,511 - INFO - Response - Page 1:
2025-08-02 12:00:05,714 - INFO - 第 1 页获取到 100 条记录
2025-08-02 12:00:05,714 - INFO - Request Parameters - Page 2:
2025-08-02 12:00:05,714 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:05,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:06,683 - INFO - Response - Page 2:
2025-08-02 12:00:06,886 - INFO - 第 2 页获取到 100 条记录
2025-08-02 12:00:06,886 - INFO - Request Parameters - Page 3:
2025-08-02 12:00:06,886 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:06,886 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:07,527 - INFO - Response - Page 3:
2025-08-02 12:00:07,730 - INFO - 第 3 页获取到 100 条记录
2025-08-02 12:00:07,730 - INFO - Request Parameters - Page 4:
2025-08-02 12:00:07,730 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:07,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:08,230 - INFO - Response - Page 4:
2025-08-02 12:00:08,433 - INFO - 第 4 页获取到 100 条记录
2025-08-02 12:00:08,433 - INFO - Request Parameters - Page 5:
2025-08-02 12:00:08,433 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:08,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:08,980 - INFO - Response - Page 5:
2025-08-02 12:00:09,183 - INFO - 第 5 页获取到 100 条记录
2025-08-02 12:00:09,183 - INFO - Request Parameters - Page 6:
2025-08-02 12:00:09,183 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:09,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:09,761 - INFO - Response - Page 6:
2025-08-02 12:00:09,964 - INFO - 第 6 页获取到 100 条记录
2025-08-02 12:00:09,964 - INFO - Request Parameters - Page 7:
2025-08-02 12:00:09,964 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:09,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:10,480 - INFO - Response - Page 7:
2025-08-02 12:00:10,683 - INFO - 第 7 页获取到 82 条记录
2025-08-02 12:00:10,683 - INFO - 查询完成，共获取到 682 条记录
2025-08-02 12:00:10,683 - INFO - 获取到 682 条表单数据
2025-08-02 12:00:10,698 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-02 12:00:10,714 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 12:00:10,714 - INFO - 开始处理日期: 2025-02
2025-08-02 12:00:10,714 - INFO - Request Parameters - Page 1:
2025-08-02 12:00:10,714 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:10,714 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:11,261 - INFO - Response - Page 1:
2025-08-02 12:00:11,464 - INFO - 第 1 页获取到 100 条记录
2025-08-02 12:00:11,464 - INFO - Request Parameters - Page 2:
2025-08-02 12:00:11,464 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:11,464 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:12,120 - INFO - Response - Page 2:
2025-08-02 12:00:12,323 - INFO - 第 2 页获取到 100 条记录
2025-08-02 12:00:12,323 - INFO - Request Parameters - Page 3:
2025-08-02 12:00:12,323 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:12,323 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:12,808 - INFO - Response - Page 3:
2025-08-02 12:00:13,011 - INFO - 第 3 页获取到 100 条记录
2025-08-02 12:00:13,011 - INFO - Request Parameters - Page 4:
2025-08-02 12:00:13,011 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:13,011 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:13,537 - INFO - Response - Page 4:
2025-08-02 12:00:13,740 - INFO - 第 4 页获取到 100 条记录
2025-08-02 12:00:13,740 - INFO - Request Parameters - Page 5:
2025-08-02 12:00:13,740 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:13,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:14,334 - INFO - Response - Page 5:
2025-08-02 12:00:14,553 - INFO - 第 5 页获取到 100 条记录
2025-08-02 12:00:14,553 - INFO - Request Parameters - Page 6:
2025-08-02 12:00:14,553 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:14,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:15,115 - INFO - Response - Page 6:
2025-08-02 12:00:15,318 - INFO - 第 6 页获取到 100 条记录
2025-08-02 12:00:15,318 - INFO - Request Parameters - Page 7:
2025-08-02 12:00:15,318 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:15,318 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:15,865 - INFO - Response - Page 7:
2025-08-02 12:00:16,068 - INFO - 第 7 页获取到 70 条记录
2025-08-02 12:00:16,068 - INFO - 查询完成，共获取到 670 条记录
2025-08-02 12:00:16,068 - INFO - 获取到 670 条表单数据
2025-08-02 12:00:16,068 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-02 12:00:16,084 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 12:00:16,084 - INFO - 开始处理日期: 2025-03
2025-08-02 12:00:16,084 - INFO - Request Parameters - Page 1:
2025-08-02 12:00:16,084 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:16,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:16,584 - INFO - Response - Page 1:
2025-08-02 12:00:16,787 - INFO - 第 1 页获取到 100 条记录
2025-08-02 12:00:16,787 - INFO - Request Parameters - Page 2:
2025-08-02 12:00:16,787 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:16,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:17,334 - INFO - Response - Page 2:
2025-08-02 12:00:17,537 - INFO - 第 2 页获取到 100 条记录
2025-08-02 12:00:17,537 - INFO - Request Parameters - Page 3:
2025-08-02 12:00:17,537 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:17,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:18,037 - INFO - Response - Page 3:
2025-08-02 12:00:18,240 - INFO - 第 3 页获取到 100 条记录
2025-08-02 12:00:18,240 - INFO - Request Parameters - Page 4:
2025-08-02 12:00:18,240 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:18,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:18,803 - INFO - Response - Page 4:
2025-08-02 12:00:19,006 - INFO - 第 4 页获取到 100 条记录
2025-08-02 12:00:19,006 - INFO - Request Parameters - Page 5:
2025-08-02 12:00:19,006 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:19,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:19,568 - INFO - Response - Page 5:
2025-08-02 12:00:19,772 - INFO - 第 5 页获取到 100 条记录
2025-08-02 12:00:19,772 - INFO - Request Parameters - Page 6:
2025-08-02 12:00:19,772 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:19,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:20,397 - INFO - Response - Page 6:
2025-08-02 12:00:20,600 - INFO - 第 6 页获取到 100 条记录
2025-08-02 12:00:20,600 - INFO - Request Parameters - Page 7:
2025-08-02 12:00:20,600 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:20,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:21,068 - INFO - Response - Page 7:
2025-08-02 12:00:21,272 - INFO - 第 7 页获取到 61 条记录
2025-08-02 12:00:21,272 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 12:00:21,272 - INFO - 获取到 661 条表单数据
2025-08-02 12:00:21,272 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-02 12:00:21,287 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 12:00:21,287 - INFO - 开始处理日期: 2025-04
2025-08-02 12:00:21,287 - INFO - Request Parameters - Page 1:
2025-08-02 12:00:21,287 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:21,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:21,818 - INFO - Response - Page 1:
2025-08-02 12:00:22,022 - INFO - 第 1 页获取到 100 条记录
2025-08-02 12:00:22,022 - INFO - Request Parameters - Page 2:
2025-08-02 12:00:22,022 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:22,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:22,584 - INFO - Response - Page 2:
2025-08-02 12:00:22,787 - INFO - 第 2 页获取到 100 条记录
2025-08-02 12:00:22,787 - INFO - Request Parameters - Page 3:
2025-08-02 12:00:22,787 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:22,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:23,350 - INFO - Response - Page 3:
2025-08-02 12:00:23,553 - INFO - 第 3 页获取到 100 条记录
2025-08-02 12:00:23,553 - INFO - Request Parameters - Page 4:
2025-08-02 12:00:23,553 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:23,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:24,084 - INFO - Response - Page 4:
2025-08-02 12:00:24,287 - INFO - 第 4 页获取到 100 条记录
2025-08-02 12:00:24,287 - INFO - Request Parameters - Page 5:
2025-08-02 12:00:24,287 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:24,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:24,756 - INFO - Response - Page 5:
2025-08-02 12:00:24,959 - INFO - 第 5 页获取到 100 条记录
2025-08-02 12:00:24,959 - INFO - Request Parameters - Page 6:
2025-08-02 12:00:24,959 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:24,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:25,506 - INFO - Response - Page 6:
2025-08-02 12:00:25,709 - INFO - 第 6 页获取到 100 条记录
2025-08-02 12:00:25,709 - INFO - Request Parameters - Page 7:
2025-08-02 12:00:25,709 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:25,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:26,193 - INFO - Response - Page 7:
2025-08-02 12:00:26,396 - INFO - 第 7 页获取到 56 条记录
2025-08-02 12:00:26,396 - INFO - 查询完成，共获取到 656 条记录
2025-08-02 12:00:26,396 - INFO - 获取到 656 条表单数据
2025-08-02 12:00:26,396 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-02 12:00:26,412 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 12:00:26,412 - INFO - 开始处理日期: 2025-05
2025-08-02 12:00:26,412 - INFO - Request Parameters - Page 1:
2025-08-02 12:00:26,412 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:26,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:26,943 - INFO - Response - Page 1:
2025-08-02 12:00:27,147 - INFO - 第 1 页获取到 100 条记录
2025-08-02 12:00:27,147 - INFO - Request Parameters - Page 2:
2025-08-02 12:00:27,147 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:27,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:27,725 - INFO - Response - Page 2:
2025-08-02 12:00:27,928 - INFO - 第 2 页获取到 100 条记录
2025-08-02 12:00:27,928 - INFO - Request Parameters - Page 3:
2025-08-02 12:00:27,928 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:27,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:28,428 - INFO - Response - Page 3:
2025-08-02 12:00:28,631 - INFO - 第 3 页获取到 100 条记录
2025-08-02 12:00:28,631 - INFO - Request Parameters - Page 4:
2025-08-02 12:00:28,631 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:28,631 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:29,146 - INFO - Response - Page 4:
2025-08-02 12:00:29,350 - INFO - 第 4 页获取到 100 条记录
2025-08-02 12:00:29,350 - INFO - Request Parameters - Page 5:
2025-08-02 12:00:29,350 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:29,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:29,912 - INFO - Response - Page 5:
2025-08-02 12:00:30,115 - INFO - 第 5 页获取到 100 条记录
2025-08-02 12:00:30,115 - INFO - Request Parameters - Page 6:
2025-08-02 12:00:30,115 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:30,115 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:30,646 - INFO - Response - Page 6:
2025-08-02 12:00:30,850 - INFO - 第 6 页获取到 100 条记录
2025-08-02 12:00:30,850 - INFO - Request Parameters - Page 7:
2025-08-02 12:00:30,850 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:30,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:31,334 - INFO - Response - Page 7:
2025-08-02 12:00:31,537 - INFO - 第 7 页获取到 65 条记录
2025-08-02 12:00:31,537 - INFO - 查询完成，共获取到 665 条记录
2025-08-02 12:00:31,537 - INFO - 获取到 665 条表单数据
2025-08-02 12:00:31,537 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-02 12:00:31,553 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 12:00:31,553 - INFO - 开始处理日期: 2025-06
2025-08-02 12:00:31,553 - INFO - Request Parameters - Page 1:
2025-08-02 12:00:31,553 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:31,553 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:32,053 - INFO - Response - Page 1:
2025-08-02 12:00:32,256 - INFO - 第 1 页获取到 100 条记录
2025-08-02 12:00:32,256 - INFO - Request Parameters - Page 2:
2025-08-02 12:00:32,256 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:32,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:32,756 - INFO - Response - Page 2:
2025-08-02 12:00:32,959 - INFO - 第 2 页获取到 100 条记录
2025-08-02 12:00:32,959 - INFO - Request Parameters - Page 3:
2025-08-02 12:00:32,959 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:32,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:33,459 - INFO - Response - Page 3:
2025-08-02 12:00:33,662 - INFO - 第 3 页获取到 100 条记录
2025-08-02 12:00:33,662 - INFO - Request Parameters - Page 4:
2025-08-02 12:00:33,662 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:33,662 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:34,146 - INFO - Response - Page 4:
2025-08-02 12:00:34,350 - INFO - 第 4 页获取到 100 条记录
2025-08-02 12:00:34,350 - INFO - Request Parameters - Page 5:
2025-08-02 12:00:34,350 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:34,350 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:34,975 - INFO - Response - Page 5:
2025-08-02 12:00:35,178 - INFO - 第 5 页获取到 100 条记录
2025-08-02 12:00:35,178 - INFO - Request Parameters - Page 6:
2025-08-02 12:00:35,178 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:35,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:35,725 - INFO - Response - Page 6:
2025-08-02 12:00:35,928 - INFO - 第 6 页获取到 100 条记录
2025-08-02 12:00:35,928 - INFO - Request Parameters - Page 7:
2025-08-02 12:00:35,928 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:35,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:36,459 - INFO - Response - Page 7:
2025-08-02 12:00:36,662 - INFO - 第 7 页获取到 61 条记录
2025-08-02 12:00:36,662 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 12:00:36,662 - INFO - 获取到 661 条表单数据
2025-08-02 12:00:36,662 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-02 12:00:36,678 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 12:00:36,678 - INFO - 开始处理日期: 2025-07
2025-08-02 12:00:36,678 - INFO - Request Parameters - Page 1:
2025-08-02 12:00:36,678 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:36,678 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:37,240 - INFO - Response - Page 1:
2025-08-02 12:00:37,443 - INFO - 第 1 页获取到 100 条记录
2025-08-02 12:00:37,443 - INFO - Request Parameters - Page 2:
2025-08-02 12:00:37,443 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:37,443 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:37,959 - INFO - Response - Page 2:
2025-08-02 12:00:38,162 - INFO - 第 2 页获取到 100 条记录
2025-08-02 12:00:38,162 - INFO - Request Parameters - Page 3:
2025-08-02 12:00:38,162 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:38,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:38,646 - INFO - Response - Page 3:
2025-08-02 12:00:38,850 - INFO - 第 3 页获取到 100 条记录
2025-08-02 12:00:38,850 - INFO - Request Parameters - Page 4:
2025-08-02 12:00:38,850 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:38,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:39,381 - INFO - Response - Page 4:
2025-08-02 12:00:39,584 - INFO - 第 4 页获取到 100 条记录
2025-08-02 12:00:39,584 - INFO - Request Parameters - Page 5:
2025-08-02 12:00:39,584 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:39,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:40,084 - INFO - Response - Page 5:
2025-08-02 12:00:40,287 - INFO - 第 5 页获取到 100 条记录
2025-08-02 12:00:40,287 - INFO - Request Parameters - Page 6:
2025-08-02 12:00:40,287 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:40,287 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:40,771 - INFO - Response - Page 6:
2025-08-02 12:00:40,975 - INFO - 第 6 页获取到 100 条记录
2025-08-02 12:00:40,975 - INFO - Request Parameters - Page 7:
2025-08-02 12:00:40,975 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:40,975 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:41,334 - INFO - Response - Page 7:
2025-08-02 12:00:41,537 - INFO - 第 7 页获取到 19 条记录
2025-08-02 12:00:41,537 - INFO - 查询完成，共获取到 619 条记录
2025-08-02 12:00:41,537 - INFO - 获取到 619 条表单数据
2025-08-02 12:00:41,537 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-02 12:00:41,537 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCM84
2025-08-02 12:00:42,068 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCM84
2025-08-02 12:00:42,068 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 48888.5, 'new_value': 50713.5}, {'field': 'total_amount', 'old_value': 48888.5, 'new_value': 50713.5}, {'field': 'order_count', 'old_value': 10213, 'new_value': 10603}]
2025-08-02 12:00:42,068 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCM94
2025-08-02 12:00:42,537 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCM94
2025-08-02 12:00:42,537 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 65207.11, 'new_value': 67726.51}, {'field': 'total_amount', 'old_value': 66499.86, 'new_value': 69019.26}, {'field': 'order_count', 'old_value': 7783, 'new_value': 8084}]
2025-08-02 12:00:42,537 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMA4
2025-08-02 12:00:42,990 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMA4
2025-08-02 12:00:42,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 39802.18, 'new_value': 41007.2}, {'field': 'offline_amount', 'old_value': 134543.6, 'new_value': 138367.0}, {'field': 'total_amount', 'old_value': 174345.78, 'new_value': 179374.2}, {'field': 'order_count', 'old_value': 2477, 'new_value': 2548}]
2025-08-02 12:00:42,990 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMB4
2025-08-02 12:00:43,506 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMB4
2025-08-02 12:00:43,506 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 2474.0, 'new_value': 2582.0}, {'field': 'total_amount', 'old_value': 2474.0, 'new_value': 2582.0}, {'field': 'order_count', 'old_value': 171, 'new_value': 180}]
2025-08-02 12:00:43,506 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMC4
2025-08-02 12:00:43,990 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMC4
2025-08-02 12:00:43,990 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 4443.67, 'new_value': 4682.17}, {'field': 'offline_amount', 'old_value': 174490.89, 'new_value': 181008.57}, {'field': 'total_amount', 'old_value': 178934.56, 'new_value': 185690.74}, {'field': 'order_count', 'old_value': 2843, 'new_value': 2923}]
2025-08-02 12:00:43,990 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMD4
2025-08-02 12:00:44,459 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMD4
2025-08-02 12:00:44,459 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 84057.0, 'new_value': 88153.0}, {'field': 'total_amount', 'old_value': 84057.0, 'new_value': 88153.0}, {'field': 'order_count', 'old_value': 265, 'new_value': 275}]
2025-08-02 12:00:44,459 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCME4
2025-08-02 12:00:44,912 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCME4
2025-08-02 12:00:44,912 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 38068.05, 'new_value': 39532.05}, {'field': 'offline_amount', 'old_value': 28363.58, 'new_value': 28937.11}, {'field': 'total_amount', 'old_value': 66431.63, 'new_value': 68469.16}, {'field': 'order_count', 'old_value': 4458, 'new_value': 4611}]
2025-08-02 12:00:44,912 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMF4
2025-08-02 12:00:45,474 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMF4
2025-08-02 12:00:45,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 136504.56, 'new_value': 141676.61}, {'field': 'total_amount', 'old_value': 136504.56, 'new_value': 141676.61}, {'field': 'order_count', 'old_value': 670, 'new_value': 692}]
2025-08-02 12:00:45,474 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMG4
2025-08-02 12:00:45,974 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMG4
2025-08-02 12:00:45,974 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 83202.0, 'new_value': 86625.0}, {'field': 'offline_amount', 'old_value': 256901.0, 'new_value': 263107.0}, {'field': 'total_amount', 'old_value': 340103.0, 'new_value': 349732.0}, {'field': 'order_count', 'old_value': 2653, 'new_value': 2736}]
2025-08-02 12:00:45,974 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMH4
2025-08-02 12:00:46,474 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMH4
2025-08-02 12:00:46,474 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 55529.1, 'new_value': 57350.5}, {'field': 'total_amount', 'old_value': 55529.1, 'new_value': 57350.5}, {'field': 'order_count', 'old_value': 351, 'new_value': 364}]
2025-08-02 12:00:46,474 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMI4
2025-08-02 12:00:46,928 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMI4
2025-08-02 12:00:46,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 131373.15, 'new_value': 135476.15}, {'field': 'total_amount', 'old_value': 136601.15, 'new_value': 140704.15}, {'field': 'order_count', 'old_value': 8628, 'new_value': 8869}]
2025-08-02 12:00:46,928 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMJ4
2025-08-02 12:00:47,474 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMJ4
2025-08-02 12:00:47,474 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 14421.08, 'new_value': 14960.23}, {'field': 'offline_amount', 'old_value': 82703.08, 'new_value': 85184.98}, {'field': 'total_amount', 'old_value': 97124.16, 'new_value': 100145.21}, {'field': 'order_count', 'old_value': 5074, 'new_value': 5237}]
2025-08-02 12:00:47,474 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMK4
2025-08-02 12:00:47,959 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMK4
2025-08-02 12:00:47,959 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 8430.9, 'new_value': 9229.3}, {'field': 'offline_amount', 'old_value': 40356.5, 'new_value': 41305.5}, {'field': 'total_amount', 'old_value': 48787.4, 'new_value': 50534.8}, {'field': 'order_count', 'old_value': 89, 'new_value': 92}]
2025-08-02 12:00:47,959 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCML4
2025-08-02 12:00:48,428 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCML4
2025-08-02 12:00:48,428 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 19303.26, 'new_value': 19889.55}, {'field': 'offline_amount', 'old_value': 73237.17, 'new_value': 75483.07}, {'field': 'total_amount', 'old_value': 92540.43, 'new_value': 95372.62}, {'field': 'order_count', 'old_value': 1834, 'new_value': 1892}]
2025-08-02 12:00:48,428 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMM4
2025-08-02 12:00:48,928 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMM4
2025-08-02 12:00:48,928 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 101144.0, 'new_value': 108700.0}, {'field': 'total_amount', 'old_value': 101144.0, 'new_value': 108700.0}, {'field': 'order_count', 'old_value': 23610, 'new_value': 24510}]
2025-08-02 12:00:48,928 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMN4
2025-08-02 12:00:49,349 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMN4
2025-08-02 12:00:49,349 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 151721.0, 'new_value': 163056.0}, {'field': 'total_amount', 'old_value': 151721.0, 'new_value': 163056.0}, {'field': 'order_count', 'old_value': 23796, 'new_value': 24696}]
2025-08-02 12:00:49,349 - INFO - 开始更新记录 - 表单实例ID: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMO4
2025-08-02 12:00:49,787 - INFO - 更新表单数据成功: FINST-0O9664D1F2SWDRD2DW72H7NKUBPI3O1MDSLCMO4
2025-08-02 12:00:49,787 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 2673.9, 'new_value': 2702.2}, {'field': 'offline_amount', 'old_value': 92995.9, 'new_value': 95141.9}, {'field': 'total_amount', 'old_value': 95669.8, 'new_value': 97844.1}, {'field': 'order_count', 'old_value': 670, 'new_value': 690}]
2025-08-02 12:00:49,787 - INFO - 日期 2025-07 处理完成 - 更新: 17 条，插入: 0 条，错误: 0 条
2025-08-02 12:00:49,787 - INFO - 开始处理日期: 2025-08
2025-08-02 12:00:49,787 - INFO - Request Parameters - Page 1:
2025-08-02 12:00:49,787 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:49,787 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:50,334 - INFO - Response - Page 1:
2025-08-02 12:00:50,537 - INFO - 第 1 页获取到 100 条记录
2025-08-02 12:00:50,537 - INFO - Request Parameters - Page 2:
2025-08-02 12:00:50,537 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 12:00:50,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 12:00:51,021 - INFO - Response - Page 2:
2025-08-02 12:00:51,224 - INFO - 第 2 页获取到 75 条记录
2025-08-02 12:00:51,224 - INFO - 查询完成，共获取到 175 条记录
2025-08-02 12:00:51,224 - INFO - 获取到 175 条表单数据
2025-08-02 12:00:51,224 - INFO - 当前日期 2025-08 有 490 条MySQL数据需要处理
2025-08-02 12:00:51,224 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMHT
2025-08-02 12:00:51,693 - INFO - 更新表单数据成功: FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMHT
2025-08-02 12:00:51,693 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10394.19}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10394.19}, {'field': 'order_count', 'old_value': 0, 'new_value': 1129}]
2025-08-02 12:00:51,693 - INFO - 开始更新记录 - 表单实例ID: FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMIT
2025-08-02 12:00:52,224 - INFO - 更新表单数据成功: FINST-EZD66RB1AALXCT7C91OW1BSSYTWQ3SO7TASDMIT
2025-08-02 12:00:52,224 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 12:00:52,224 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDM91
2025-08-02 12:00:52,599 - INFO - 更新表单数据成功: FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDM91
2025-08-02 12:00:52,599 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 150000.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 150000.0}]
2025-08-02 12:00:52,599 - INFO - 开始更新记录 - 表单实例ID: FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMD1
2025-08-02 12:00:53,021 - INFO - 更新表单数据成功: FINST-AEF66BC18KNX2R147XJ7T93BRI3J2B6FV6TDMD1
2025-08-02 12:00:53,021 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 50000.0}]
2025-08-02 12:00:53,021 - INFO - 开始批量插入 315 条新记录
2025-08-02 12:00:53,318 - INFO - 批量插入响应状态码: 200
2025-08-02 12:00:53,318 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 04:00:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4912', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C493C736-21B7-7B87-9F20-4859CB4DC77A', 'x-acs-trace-id': 'cfc069cd6dc22043c934d7e996636386', 'etag': '4EXvvR5llvsIQ7eIynvz0GQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 12:00:53,318 - INFO - 批量插入响应体: {'result': ['FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMAB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMBB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMCB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMDB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMEB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMFB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMGB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMHB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMIB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMJB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMKB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMLB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMMB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMNB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMOB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMPB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMQB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMRB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMSB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMTB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMUB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMVB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMWB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMXB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMYB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMZB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDM0C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDM1C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM2C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM3C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM4C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM5C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM6C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM7C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM8C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM9C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMAC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMBC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMCC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMDC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMEC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMFC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMGC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMHC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMIC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMJC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMKC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMLC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMMC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMNC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMOC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMPC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMQC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMRC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMSC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMTC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMUC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMVC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMWC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMXC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMYC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMZC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM0D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM1D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM2D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM3D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM4D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM5D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM6D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM7D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM8D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM9D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMAD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMBD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMCD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMDD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMED1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMFD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMGD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMHD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMID1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMJD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMKD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMLD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMMD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMND1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMOD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMPD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMQD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMRD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMSD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMTD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMUD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMVD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMWD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMXD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMYD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMZD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM0E1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM1E1']}
2025-08-02 12:00:53,318 - INFO - 批量插入表单数据成功，批次 1，共 100 条记录
2025-08-02 12:00:53,318 - INFO - 成功插入的数据ID: ['FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMAB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMBB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMCB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMDB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMEB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMFB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMGB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMHB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMIB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMJB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMKB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMLB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMMB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMNB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMOB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMPB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMQB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMRB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMSB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMTB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMUB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMVB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMWB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMXB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMYB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDMZB1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDM0C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2B656QTDM1C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM2C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM3C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM4C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM5C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM6C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM7C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM8C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM9C1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMAC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMBC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMCC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMDC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMEC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMFC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMGC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMHC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMIC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMJC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMKC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMLC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMMC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMNC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMOC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMPC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMQC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMRC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMSC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMTC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMUC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMVC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMWC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMXC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMYC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMZC1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM0D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM1D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM2D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM3D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM4D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM5D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM6D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM7D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM8D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM9D1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMAD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMBD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMCD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMDD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMED1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMFD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMGD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMHD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMID1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMJD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMKD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMLD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMMD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMND1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMOD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMPD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMQD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMRD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMSD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMTD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMUD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMVD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMWD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMXD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMYD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDMZD1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM0E1', 'FINST-G1F66CA1APFXBZANDGA06BUP39UR2C656QTDM1E1']
2025-08-02 12:00:56,662 - INFO - 批量插入响应状态码: 200
2025-08-02 12:00:56,662 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 04:00:59 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4807', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6A137318-8E8D-7A53-8A07-7B70EDB1F007', 'x-acs-trace-id': '2ce9aafc769a425f22d447bd7148e0da', 'etag': '4FJ5DtgFp1GgH0qGKUxk0Dg7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 12:00:56,662 - INFO - 批量插入响应体: {'result': ['FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMV', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMW', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMX', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMY', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMZ', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM01', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM11', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM21', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM31', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM41', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM51', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM61', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM71', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM81', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM91', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMA1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMB1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMC1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMD1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDME1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMF1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMG1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMH1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMI1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMJ1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMK1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDML1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMM1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMN1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMO1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMP1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMQ1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMR1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMS1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMT1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMU1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMV1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMW1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMX1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMY1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMZ1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM02', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM12', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM22', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM32', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM42', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM52', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM62', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM72', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM82', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM92', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMA2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMB2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMC2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMD2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDME2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMF2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMG2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMH2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMI2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMJ2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMK2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDML2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMM2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMN2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMO2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMP2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMQ2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMR2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMS2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMT2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMU2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMV2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMW2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMX2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMY2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMZ2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM03', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM13', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM23', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM33', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM43', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM53', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM63', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM73', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM83', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM93', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMA3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMB3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMC3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMD3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDME3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMF3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMG3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMH3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMI3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMJ3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMK3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDML3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMM3']}
2025-08-02 12:00:56,662 - INFO - 批量插入表单数据成功，批次 2，共 100 条记录
2025-08-02 12:00:56,662 - INFO - 成功插入的数据ID: ['FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMV', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMW', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMX', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMY', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMZ', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM01', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM11', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM21', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM31', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM41', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM51', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM61', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM71', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM81', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDM91', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMA1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMB1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMC1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMD1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDME1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMF1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMG1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMH1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMI1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMJ1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMK1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDML1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMM1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMN1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMO1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMP1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMQ1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMR1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM21R76QTDMS1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMT1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMU1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMV1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMW1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMX1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMY1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMZ1', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM02', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM12', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM22', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM32', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM42', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM52', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM62', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM72', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM82', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM92', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMA2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMB2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMC2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMD2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDME2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMF2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMG2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMH2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMI2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMJ2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMK2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDML2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMM2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMN2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMO2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMP2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMQ2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMR2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMS2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMT2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMU2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMV2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMW2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMX2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMY2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMZ2', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM03', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM13', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM23', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM33', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM43', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM53', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM63', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM73', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM83', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDM93', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMA3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMB3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMC3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMD3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDME3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMF3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMG3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMH3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMI3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMJ3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMK3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDML3', 'FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMM3']
2025-08-02 12:00:59,959 - INFO - 批量插入响应状态码: 200
2025-08-02 12:00:59,959 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 04:01:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '4812', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '2A4A20CA-F47D-7262-836F-0F9B47075FAF', 'x-acs-trace-id': '5b3988f1c0ae54c730e5ce241dc8a857', 'etag': '4vZzo9z40uFjoKnampJGI4g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 12:00:59,959 - INFO - 批量插入响应体: {'result': ['FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMU5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMV5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMW5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMX5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMY5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMZ5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM06', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM16', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM26', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM36', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM46', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM56', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM66', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM76', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM86', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM96', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMA6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMB6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMC6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMD6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDME6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMF6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMG6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMH6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMI6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMJ6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMK6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDML6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMM6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMN6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMO6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMP6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMQ6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMR6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMS6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMT6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMU6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMV6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMW6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMX6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMY6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMZ6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM07', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM17', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM27', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM37', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM47', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM57', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM67', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM77', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM87', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM97', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMA7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMB7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMC7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMD7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDME7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMF7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMG7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMH7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMI7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMJ7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMK7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDML7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMM7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMN7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMO7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMP7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMQ7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMR7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMS7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMT7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMU7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMV7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMW7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMX7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMY7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMZ7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM08', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM18', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM28', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM38', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM48', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM58', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM68', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM78', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM88', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM98', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMA8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMB8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMC8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMD8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDME8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMF8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMG8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMH8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMI8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMJ8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMK8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDML8']}
2025-08-02 12:00:59,959 - INFO - 批量插入表单数据成功，批次 3，共 100 条记录
2025-08-02 12:00:59,959 - INFO - 成功插入的数据ID: ['FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMU5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMV5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMW5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMX5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMY5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMZ5', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM06', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM16', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM26', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM36', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM46', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM56', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM66', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM76', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM86', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM96', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMA6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMB6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMC6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMD6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDME6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMF6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMG6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMH6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMI6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMJ6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMK6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDML6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMM6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMN6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMO6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMP6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMQ6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMR6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMS6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMT6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMU6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMV6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMW6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMX6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMY6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMZ6', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM07', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM17', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM27', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM37', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM47', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM57', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM67', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM77', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM87', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM97', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMA7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMB7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMC7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMD7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDME7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMF7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMG7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMH7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMI7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMJ7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMK7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDML7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMM7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMN7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMO7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMP7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMQ7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMR7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMS7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMT7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMU7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMV7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMW7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMX7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMY7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMZ7', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM08', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM18', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM28', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM38', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM48', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM58', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM68', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM78', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM88', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM98', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMA8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMB8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMC8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMD8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDME8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMF8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMG8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMH8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMI8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMJ8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMK8', 'FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDML8']
2025-08-02 12:01:03,131 - INFO - 批量插入响应状态码: 200
2025-08-02 12:01:03,131 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 04:01:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '717', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E9282727-E5FD-702A-95C5-CF26073DD092', 'x-acs-trace-id': 'd28d355afa727f748f6ec0a0177d36bc', 'etag': '7kYD6AjkObFlV0gq5GWg57g7', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 12:01:03,131 - INFO - 批量插入响应体: {'result': ['FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM3', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM4', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM5', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM6', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM7', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM8', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM9', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMA', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMB', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMC', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMD', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDME', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMF', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMG', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMH']}
2025-08-02 12:01:03,131 - INFO - 批量插入表单数据成功，批次 4，共 15 条记录
2025-08-02 12:01:03,131 - INFO - 成功插入的数据ID: ['FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM3', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM4', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM5', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM6', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM7', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM8', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDM9', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMA', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMB', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMC', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMD', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDME', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMF', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMG', 'FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMH']
2025-08-02 12:01:06,146 - INFO - 批量插入完成，共 315 条记录
2025-08-02 12:01:06,146 - INFO - 日期 2025-08 处理完成 - 更新: 4 条，插入: 315 条，错误: 0 条
2025-08-02 12:01:06,146 - INFO - 数据同步完成！更新: 21 条，插入: 315 条，错误: 0 条
2025-08-02 12:01:06,146 - INFO - =================同步完成====================
2025-08-02 15:00:02,556 - INFO - =================使用默认全量同步=============
2025-08-02 15:00:04,790 - INFO - MySQL查询成功，共获取 5107 条记录
2025-08-02 15:00:04,790 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-02 15:00:04,837 - INFO - 开始处理日期: 2025-01
2025-08-02 15:00:04,837 - INFO - Request Parameters - Page 1:
2025-08-02 15:00:04,837 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:04,837 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:06,603 - INFO - Response - Page 1:
2025-08-02 15:00:06,806 - INFO - 第 1 页获取到 100 条记录
2025-08-02 15:00:06,806 - INFO - Request Parameters - Page 2:
2025-08-02 15:00:06,806 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:06,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:07,369 - INFO - Response - Page 2:
2025-08-02 15:00:07,572 - INFO - 第 2 页获取到 100 条记录
2025-08-02 15:00:07,572 - INFO - Request Parameters - Page 3:
2025-08-02 15:00:07,572 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:07,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:08,103 - INFO - Response - Page 3:
2025-08-02 15:00:08,306 - INFO - 第 3 页获取到 100 条记录
2025-08-02 15:00:08,306 - INFO - Request Parameters - Page 4:
2025-08-02 15:00:08,306 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:08,306 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:08,837 - INFO - Response - Page 4:
2025-08-02 15:00:09,040 - INFO - 第 4 页获取到 100 条记录
2025-08-02 15:00:09,040 - INFO - Request Parameters - Page 5:
2025-08-02 15:00:09,040 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:09,040 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:09,587 - INFO - Response - Page 5:
2025-08-02 15:00:09,790 - INFO - 第 5 页获取到 100 条记录
2025-08-02 15:00:09,790 - INFO - Request Parameters - Page 6:
2025-08-02 15:00:09,790 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:09,790 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:10,275 - INFO - Response - Page 6:
2025-08-02 15:00:10,478 - INFO - 第 6 页获取到 100 条记录
2025-08-02 15:00:10,478 - INFO - Request Parameters - Page 7:
2025-08-02 15:00:10,478 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:10,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:10,978 - INFO - Response - Page 7:
2025-08-02 15:00:11,181 - INFO - 第 7 页获取到 82 条记录
2025-08-02 15:00:11,181 - INFO - 查询完成，共获取到 682 条记录
2025-08-02 15:00:11,181 - INFO - 获取到 682 条表单数据
2025-08-02 15:00:11,181 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-02 15:00:11,197 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 15:00:11,197 - INFO - 开始处理日期: 2025-02
2025-08-02 15:00:11,197 - INFO - Request Parameters - Page 1:
2025-08-02 15:00:11,197 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:11,197 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:11,697 - INFO - Response - Page 1:
2025-08-02 15:00:11,900 - INFO - 第 1 页获取到 100 条记录
2025-08-02 15:00:11,900 - INFO - Request Parameters - Page 2:
2025-08-02 15:00:11,900 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:11,900 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:12,368 - INFO - Response - Page 2:
2025-08-02 15:00:12,572 - INFO - 第 2 页获取到 100 条记录
2025-08-02 15:00:12,572 - INFO - Request Parameters - Page 3:
2025-08-02 15:00:12,572 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:12,572 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:13,087 - INFO - Response - Page 3:
2025-08-02 15:00:13,290 - INFO - 第 3 页获取到 100 条记录
2025-08-02 15:00:13,290 - INFO - Request Parameters - Page 4:
2025-08-02 15:00:13,290 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:13,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:13,900 - INFO - Response - Page 4:
2025-08-02 15:00:14,103 - INFO - 第 4 页获取到 100 条记录
2025-08-02 15:00:14,103 - INFO - Request Parameters - Page 5:
2025-08-02 15:00:14,103 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:14,103 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:14,603 - INFO - Response - Page 5:
2025-08-02 15:00:14,806 - INFO - 第 5 页获取到 100 条记录
2025-08-02 15:00:14,806 - INFO - Request Parameters - Page 6:
2025-08-02 15:00:14,806 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:14,806 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:15,384 - INFO - Response - Page 6:
2025-08-02 15:00:15,587 - INFO - 第 6 页获取到 100 条记录
2025-08-02 15:00:15,587 - INFO - Request Parameters - Page 7:
2025-08-02 15:00:15,587 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:15,587 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:16,072 - INFO - Response - Page 7:
2025-08-02 15:00:16,275 - INFO - 第 7 页获取到 70 条记录
2025-08-02 15:00:16,275 - INFO - 查询完成，共获取到 670 条记录
2025-08-02 15:00:16,275 - INFO - 获取到 670 条表单数据
2025-08-02 15:00:16,275 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-02 15:00:16,290 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 15:00:16,290 - INFO - 开始处理日期: 2025-03
2025-08-02 15:00:16,290 - INFO - Request Parameters - Page 1:
2025-08-02 15:00:16,290 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:16,290 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:16,822 - INFO - Response - Page 1:
2025-08-02 15:00:17,025 - INFO - 第 1 页获取到 100 条记录
2025-08-02 15:00:17,025 - INFO - Request Parameters - Page 2:
2025-08-02 15:00:17,025 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:17,025 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:17,540 - INFO - Response - Page 2:
2025-08-02 15:00:17,743 - INFO - 第 2 页获取到 100 条记录
2025-08-02 15:00:17,743 - INFO - Request Parameters - Page 3:
2025-08-02 15:00:17,743 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:17,743 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:18,317 - INFO - Response - Page 3:
2025-08-02 15:00:18,520 - INFO - 第 3 页获取到 100 条记录
2025-08-02 15:00:18,520 - INFO - Request Parameters - Page 4:
2025-08-02 15:00:18,520 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:18,520 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:19,082 - INFO - Response - Page 4:
2025-08-02 15:00:19,285 - INFO - 第 4 页获取到 100 条记录
2025-08-02 15:00:19,285 - INFO - Request Parameters - Page 5:
2025-08-02 15:00:19,285 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:19,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:19,848 - INFO - Response - Page 5:
2025-08-02 15:00:20,051 - INFO - 第 5 页获取到 100 条记录
2025-08-02 15:00:20,051 - INFO - Request Parameters - Page 6:
2025-08-02 15:00:20,051 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:20,051 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:20,660 - INFO - Response - Page 6:
2025-08-02 15:00:20,864 - INFO - 第 6 页获取到 100 条记录
2025-08-02 15:00:20,864 - INFO - Request Parameters - Page 7:
2025-08-02 15:00:20,864 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:20,864 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:21,332 - INFO - Response - Page 7:
2025-08-02 15:00:21,535 - INFO - 第 7 页获取到 61 条记录
2025-08-02 15:00:21,535 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 15:00:21,535 - INFO - 获取到 661 条表单数据
2025-08-02 15:00:21,535 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-02 15:00:21,551 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 15:00:21,551 - INFO - 开始处理日期: 2025-04
2025-08-02 15:00:21,551 - INFO - Request Parameters - Page 1:
2025-08-02 15:00:21,551 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:21,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:22,082 - INFO - Response - Page 1:
2025-08-02 15:00:22,285 - INFO - 第 1 页获取到 100 条记录
2025-08-02 15:00:22,285 - INFO - Request Parameters - Page 2:
2025-08-02 15:00:22,285 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:22,285 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:22,785 - INFO - Response - Page 2:
2025-08-02 15:00:22,989 - INFO - 第 2 页获取到 100 条记录
2025-08-02 15:00:22,989 - INFO - Request Parameters - Page 3:
2025-08-02 15:00:22,989 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:22,989 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:23,614 - INFO - Response - Page 3:
2025-08-02 15:00:23,817 - INFO - 第 3 页获取到 100 条记录
2025-08-02 15:00:23,817 - INFO - Request Parameters - Page 4:
2025-08-02 15:00:23,817 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:23,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:24,379 - INFO - Response - Page 4:
2025-08-02 15:00:24,582 - INFO - 第 4 页获取到 100 条记录
2025-08-02 15:00:24,582 - INFO - Request Parameters - Page 5:
2025-08-02 15:00:24,582 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:24,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:25,098 - INFO - Response - Page 5:
2025-08-02 15:00:25,301 - INFO - 第 5 页获取到 100 条记录
2025-08-02 15:00:25,301 - INFO - Request Parameters - Page 6:
2025-08-02 15:00:25,301 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:25,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:25,926 - INFO - Response - Page 6:
2025-08-02 15:00:26,129 - INFO - 第 6 页获取到 100 条记录
2025-08-02 15:00:26,129 - INFO - Request Parameters - Page 7:
2025-08-02 15:00:26,129 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:26,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:26,614 - INFO - Response - Page 7:
2025-08-02 15:00:26,817 - INFO - 第 7 页获取到 56 条记录
2025-08-02 15:00:26,817 - INFO - 查询完成，共获取到 656 条记录
2025-08-02 15:00:26,817 - INFO - 获取到 656 条表单数据
2025-08-02 15:00:26,817 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-02 15:00:26,832 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 15:00:26,832 - INFO - 开始处理日期: 2025-05
2025-08-02 15:00:26,832 - INFO - Request Parameters - Page 1:
2025-08-02 15:00:26,832 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:26,832 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:27,363 - INFO - Response - Page 1:
2025-08-02 15:00:27,567 - INFO - 第 1 页获取到 100 条记录
2025-08-02 15:00:27,567 - INFO - Request Parameters - Page 2:
2025-08-02 15:00:27,567 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:27,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:28,098 - INFO - Response - Page 2:
2025-08-02 15:00:28,301 - INFO - 第 2 页获取到 100 条记录
2025-08-02 15:00:28,301 - INFO - Request Parameters - Page 3:
2025-08-02 15:00:28,301 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:28,301 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:28,817 - INFO - Response - Page 3:
2025-08-02 15:00:29,020 - INFO - 第 3 页获取到 100 条记录
2025-08-02 15:00:29,020 - INFO - Request Parameters - Page 4:
2025-08-02 15:00:29,020 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:29,020 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:29,582 - INFO - Response - Page 4:
2025-08-02 15:00:29,785 - INFO - 第 4 页获取到 100 条记录
2025-08-02 15:00:29,785 - INFO - Request Parameters - Page 5:
2025-08-02 15:00:29,785 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:29,785 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:30,332 - INFO - Response - Page 5:
2025-08-02 15:00:30,535 - INFO - 第 5 页获取到 100 条记录
2025-08-02 15:00:30,535 - INFO - Request Parameters - Page 6:
2025-08-02 15:00:30,535 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:30,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:31,035 - INFO - Response - Page 6:
2025-08-02 15:00:31,238 - INFO - 第 6 页获取到 100 条记录
2025-08-02 15:00:31,238 - INFO - Request Parameters - Page 7:
2025-08-02 15:00:31,238 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:31,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:31,707 - INFO - Response - Page 7:
2025-08-02 15:00:31,910 - INFO - 第 7 页获取到 65 条记录
2025-08-02 15:00:31,910 - INFO - 查询完成，共获取到 665 条记录
2025-08-02 15:00:31,910 - INFO - 获取到 665 条表单数据
2025-08-02 15:00:31,910 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-02 15:00:31,926 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 15:00:31,926 - INFO - 开始处理日期: 2025-06
2025-08-02 15:00:31,926 - INFO - Request Parameters - Page 1:
2025-08-02 15:00:31,926 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:31,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:32,379 - INFO - Response - Page 1:
2025-08-02 15:00:32,582 - INFO - 第 1 页获取到 100 条记录
2025-08-02 15:00:32,582 - INFO - Request Parameters - Page 2:
2025-08-02 15:00:32,582 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:32,582 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:33,238 - INFO - Response - Page 2:
2025-08-02 15:00:33,442 - INFO - 第 2 页获取到 100 条记录
2025-08-02 15:00:33,442 - INFO - Request Parameters - Page 3:
2025-08-02 15:00:33,442 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:33,442 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:33,973 - INFO - Response - Page 3:
2025-08-02 15:00:34,176 - INFO - 第 3 页获取到 100 条记录
2025-08-02 15:00:34,176 - INFO - Request Parameters - Page 4:
2025-08-02 15:00:34,176 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:34,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:34,660 - INFO - Response - Page 4:
2025-08-02 15:00:34,863 - INFO - 第 4 页获取到 100 条记录
2025-08-02 15:00:34,863 - INFO - Request Parameters - Page 5:
2025-08-02 15:00:34,863 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:34,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:35,442 - INFO - Response - Page 5:
2025-08-02 15:00:35,645 - INFO - 第 5 页获取到 100 条记录
2025-08-02 15:00:35,645 - INFO - Request Parameters - Page 6:
2025-08-02 15:00:35,645 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:35,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:36,176 - INFO - Response - Page 6:
2025-08-02 15:00:36,379 - INFO - 第 6 页获取到 100 条记录
2025-08-02 15:00:36,379 - INFO - Request Parameters - Page 7:
2025-08-02 15:00:36,379 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:36,379 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:36,926 - INFO - Response - Page 7:
2025-08-02 15:00:37,129 - INFO - 第 7 页获取到 61 条记录
2025-08-02 15:00:37,129 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 15:00:37,129 - INFO - 获取到 661 条表单数据
2025-08-02 15:00:37,129 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-02 15:00:37,145 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 15:00:37,145 - INFO - 开始处理日期: 2025-07
2025-08-02 15:00:37,145 - INFO - Request Parameters - Page 1:
2025-08-02 15:00:37,145 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:37,145 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:37,676 - INFO - Response - Page 1:
2025-08-02 15:00:37,879 - INFO - 第 1 页获取到 100 条记录
2025-08-02 15:00:37,879 - INFO - Request Parameters - Page 2:
2025-08-02 15:00:37,879 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:37,879 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:38,348 - INFO - Response - Page 2:
2025-08-02 15:00:38,551 - INFO - 第 2 页获取到 100 条记录
2025-08-02 15:00:38,551 - INFO - Request Parameters - Page 3:
2025-08-02 15:00:38,551 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:38,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:39,129 - INFO - Response - Page 3:
2025-08-02 15:00:39,348 - INFO - 第 3 页获取到 100 条记录
2025-08-02 15:00:39,348 - INFO - Request Parameters - Page 4:
2025-08-02 15:00:39,348 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:39,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:39,832 - INFO - Response - Page 4:
2025-08-02 15:00:40,035 - INFO - 第 4 页获取到 100 条记录
2025-08-02 15:00:40,035 - INFO - Request Parameters - Page 5:
2025-08-02 15:00:40,035 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:40,035 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:40,598 - INFO - Response - Page 5:
2025-08-02 15:00:40,801 - INFO - 第 5 页获取到 100 条记录
2025-08-02 15:00:40,801 - INFO - Request Parameters - Page 6:
2025-08-02 15:00:40,801 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:40,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:41,363 - INFO - Response - Page 6:
2025-08-02 15:00:41,567 - INFO - 第 6 页获取到 100 条记录
2025-08-02 15:00:41,567 - INFO - Request Parameters - Page 7:
2025-08-02 15:00:41,567 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:41,567 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:41,895 - INFO - Response - Page 7:
2025-08-02 15:00:42,098 - INFO - 第 7 页获取到 19 条记录
2025-08-02 15:00:42,098 - INFO - 查询完成，共获取到 619 条记录
2025-08-02 15:00:42,098 - INFO - 获取到 619 条表单数据
2025-08-02 15:00:42,098 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-02 15:00:42,113 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 15:00:42,113 - INFO - 开始处理日期: 2025-08
2025-08-02 15:00:42,113 - INFO - Request Parameters - Page 1:
2025-08-02 15:00:42,113 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:42,113 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:42,660 - INFO - Response - Page 1:
2025-08-02 15:00:42,863 - INFO - 第 1 页获取到 100 条记录
2025-08-02 15:00:42,863 - INFO - Request Parameters - Page 2:
2025-08-02 15:00:42,863 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:42,863 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:43,348 - INFO - Response - Page 2:
2025-08-02 15:00:43,551 - INFO - 第 2 页获取到 100 条记录
2025-08-02 15:00:43,551 - INFO - Request Parameters - Page 3:
2025-08-02 15:00:43,551 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:43,551 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:44,066 - INFO - Response - Page 3:
2025-08-02 15:00:44,270 - INFO - 第 3 页获取到 100 条记录
2025-08-02 15:00:44,270 - INFO - Request Parameters - Page 4:
2025-08-02 15:00:44,270 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:44,270 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:44,926 - INFO - Response - Page 4:
2025-08-02 15:00:45,129 - INFO - 第 4 页获取到 100 条记录
2025-08-02 15:00:45,129 - INFO - Request Parameters - Page 5:
2025-08-02 15:00:45,129 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 15:00:45,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 15:00:45,582 - INFO - Response - Page 5:
2025-08-02 15:00:45,785 - INFO - 第 5 页获取到 90 条记录
2025-08-02 15:00:45,785 - INFO - 查询完成，共获取到 490 条记录
2025-08-02 15:00:45,785 - INFO - 获取到 490 条表单数据
2025-08-02 15:00:45,785 - INFO - 当前日期 2025-08 有 493 条MySQL数据需要处理
2025-08-02 15:00:45,785 - INFO - 开始更新记录 - 表单实例ID: FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMK2
2025-08-02 15:00:46,379 - INFO - 更新表单数据成功: FINST-SWC66P917NNX4JUX5LS1XA0KZQOM22R76QTDMK2
2025-08-02 15:00:46,379 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 11100.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 11100.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-08-02 15:00:46,395 - INFO - 开始更新记录 - 表单实例ID: FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMF
2025-08-02 15:00:46,863 - INFO - 更新表单数据成功: FINST-WBF66B81IRNXGPLL8JFTCBES5LTB3XQC6QTDMF
2025-08-02 15:00:46,863 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 54464.0, 'new_value': 19585.4}, {'field': 'total_amount', 'old_value': 54464.0, 'new_value': 19585.4}, {'field': 'order_count', 'old_value': 92, 'new_value': 392}]
2025-08-02 15:00:46,863 - INFO - 开始批量插入 3 条新记录
2025-08-02 15:00:47,020 - INFO - 批量插入响应状态码: 200
2025-08-02 15:00:47,020 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 07:00:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '6AF0E602-4D45-7645-8544-9CF5BC3B9C96', 'x-acs-trace-id': 'c85ddda0bdee8522bc0c769545cf594e', 'etag': '1CYnzZwReSkb6QO6Rg9lJOA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 15:00:47,020 - INFO - 批量插入响应体: {'result': ['FINST-L5766E71JNNXXYLLDASEE4AHSVPU3MPHLWTDMJ3', 'FINST-L5766E71JNNXXYLLDASEE4AHSVPU3NPHLWTDMK3', 'FINST-L5766E71JNNXXYLLDASEE4AHSVPU3NPHLWTDML3']}
2025-08-02 15:00:47,020 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-02 15:00:47,020 - INFO - 成功插入的数据ID: ['FINST-L5766E71JNNXXYLLDASEE4AHSVPU3MPHLWTDMJ3', 'FINST-L5766E71JNNXXYLLDASEE4AHSVPU3NPHLWTDMK3', 'FINST-L5766E71JNNXXYLLDASEE4AHSVPU3NPHLWTDML3']
2025-08-02 15:00:50,035 - INFO - 批量插入完成，共 3 条记录
2025-08-02 15:00:50,035 - INFO - 日期 2025-08 处理完成 - 更新: 2 条，插入: 3 条，错误: 0 条
2025-08-02 15:00:50,035 - INFO - 数据同步完成！更新: 2 条，插入: 3 条，错误: 0 条
2025-08-02 15:00:50,035 - INFO - =================同步完成====================
2025-08-02 18:00:02,617 - INFO - =================使用默认全量同步=============
2025-08-02 18:00:04,757 - INFO - MySQL查询成功，共获取 5107 条记录
2025-08-02 18:00:04,757 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-02 18:00:04,804 - INFO - 开始处理日期: 2025-01
2025-08-02 18:00:04,804 - INFO - Request Parameters - Page 1:
2025-08-02 18:00:04,804 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:04,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:06,757 - INFO - Response - Page 1:
2025-08-02 18:00:06,960 - INFO - 第 1 页获取到 100 条记录
2025-08-02 18:00:06,960 - INFO - Request Parameters - Page 2:
2025-08-02 18:00:06,960 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:06,960 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:07,585 - INFO - Response - Page 2:
2025-08-02 18:00:07,789 - INFO - 第 2 页获取到 100 条记录
2025-08-02 18:00:07,789 - INFO - Request Parameters - Page 3:
2025-08-02 18:00:07,789 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:07,789 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:08,335 - INFO - Response - Page 3:
2025-08-02 18:00:08,539 - INFO - 第 3 页获取到 100 条记录
2025-08-02 18:00:08,539 - INFO - Request Parameters - Page 4:
2025-08-02 18:00:08,539 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:08,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:09,101 - INFO - Response - Page 4:
2025-08-02 18:00:09,304 - INFO - 第 4 页获取到 100 条记录
2025-08-02 18:00:09,304 - INFO - Request Parameters - Page 5:
2025-08-02 18:00:09,304 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:09,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:09,914 - INFO - Response - Page 5:
2025-08-02 18:00:10,117 - INFO - 第 5 页获取到 100 条记录
2025-08-02 18:00:10,117 - INFO - Request Parameters - Page 6:
2025-08-02 18:00:10,117 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:10,117 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:10,601 - INFO - Response - Page 6:
2025-08-02 18:00:10,804 - INFO - 第 6 页获取到 100 条记录
2025-08-02 18:00:10,804 - INFO - Request Parameters - Page 7:
2025-08-02 18:00:10,804 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:10,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:11,320 - INFO - Response - Page 7:
2025-08-02 18:00:11,523 - INFO - 第 7 页获取到 82 条记录
2025-08-02 18:00:11,523 - INFO - 查询完成，共获取到 682 条记录
2025-08-02 18:00:11,523 - INFO - 获取到 682 条表单数据
2025-08-02 18:00:11,523 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-02 18:00:11,539 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:11,539 - INFO - 开始处理日期: 2025-02
2025-08-02 18:00:11,539 - INFO - Request Parameters - Page 1:
2025-08-02 18:00:11,539 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:11,539 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:12,101 - INFO - Response - Page 1:
2025-08-02 18:00:12,304 - INFO - 第 1 页获取到 100 条记录
2025-08-02 18:00:12,304 - INFO - Request Parameters - Page 2:
2025-08-02 18:00:12,304 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:12,304 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:12,882 - INFO - Response - Page 2:
2025-08-02 18:00:13,085 - INFO - 第 2 页获取到 100 条记录
2025-08-02 18:00:13,085 - INFO - Request Parameters - Page 3:
2025-08-02 18:00:13,085 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:13,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:13,601 - INFO - Response - Page 3:
2025-08-02 18:00:13,804 - INFO - 第 3 页获取到 100 条记录
2025-08-02 18:00:13,804 - INFO - Request Parameters - Page 4:
2025-08-02 18:00:13,804 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:13,804 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:14,367 - INFO - Response - Page 4:
2025-08-02 18:00:14,570 - INFO - 第 4 页获取到 100 条记录
2025-08-02 18:00:14,570 - INFO - Request Parameters - Page 5:
2025-08-02 18:00:14,570 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:14,570 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:15,117 - INFO - Response - Page 5:
2025-08-02 18:00:15,320 - INFO - 第 5 页获取到 100 条记录
2025-08-02 18:00:15,320 - INFO - Request Parameters - Page 6:
2025-08-02 18:00:15,320 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:15,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:15,867 - INFO - Response - Page 6:
2025-08-02 18:00:16,070 - INFO - 第 6 页获取到 100 条记录
2025-08-02 18:00:16,070 - INFO - Request Parameters - Page 7:
2025-08-02 18:00:16,070 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:16,070 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:16,601 - INFO - Response - Page 7:
2025-08-02 18:00:16,804 - INFO - 第 7 页获取到 70 条记录
2025-08-02 18:00:16,804 - INFO - 查询完成，共获取到 670 条记录
2025-08-02 18:00:16,804 - INFO - 获取到 670 条表单数据
2025-08-02 18:00:16,804 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-02 18:00:16,820 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:16,820 - INFO - 开始处理日期: 2025-03
2025-08-02 18:00:16,820 - INFO - Request Parameters - Page 1:
2025-08-02 18:00:16,820 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:16,820 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:17,382 - INFO - Response - Page 1:
2025-08-02 18:00:17,585 - INFO - 第 1 页获取到 100 条记录
2025-08-02 18:00:17,585 - INFO - Request Parameters - Page 2:
2025-08-02 18:00:17,585 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:17,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:18,148 - INFO - Response - Page 2:
2025-08-02 18:00:18,351 - INFO - 第 2 页获取到 100 条记录
2025-08-02 18:00:18,351 - INFO - Request Parameters - Page 3:
2025-08-02 18:00:18,351 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:18,351 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:18,898 - INFO - Response - Page 3:
2025-08-02 18:00:19,101 - INFO - 第 3 页获取到 100 条记录
2025-08-02 18:00:19,101 - INFO - Request Parameters - Page 4:
2025-08-02 18:00:19,101 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:19,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:19,648 - INFO - Response - Page 4:
2025-08-02 18:00:19,851 - INFO - 第 4 页获取到 100 条记录
2025-08-02 18:00:19,851 - INFO - Request Parameters - Page 5:
2025-08-02 18:00:19,851 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:19,851 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:20,351 - INFO - Response - Page 5:
2025-08-02 18:00:20,554 - INFO - 第 5 页获取到 100 条记录
2025-08-02 18:00:20,554 - INFO - Request Parameters - Page 6:
2025-08-02 18:00:20,554 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:20,554 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:21,195 - INFO - Response - Page 6:
2025-08-02 18:00:21,398 - INFO - 第 6 页获取到 100 条记录
2025-08-02 18:00:21,398 - INFO - Request Parameters - Page 7:
2025-08-02 18:00:21,398 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:21,398 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:21,882 - INFO - Response - Page 7:
2025-08-02 18:00:22,085 - INFO - 第 7 页获取到 61 条记录
2025-08-02 18:00:22,085 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 18:00:22,085 - INFO - 获取到 661 条表单数据
2025-08-02 18:00:22,085 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-02 18:00:22,101 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:22,101 - INFO - 开始处理日期: 2025-04
2025-08-02 18:00:22,101 - INFO - Request Parameters - Page 1:
2025-08-02 18:00:22,101 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:22,101 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:22,669 - INFO - Response - Page 1:
2025-08-02 18:00:22,877 - INFO - 第 1 页获取到 100 条记录
2025-08-02 18:00:22,877 - INFO - Request Parameters - Page 2:
2025-08-02 18:00:22,877 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:22,877 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:23,362 - INFO - Response - Page 2:
2025-08-02 18:00:23,565 - INFO - 第 2 页获取到 100 条记录
2025-08-02 18:00:23,565 - INFO - Request Parameters - Page 3:
2025-08-02 18:00:23,565 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:23,565 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:24,127 - INFO - Response - Page 3:
2025-08-02 18:00:24,330 - INFO - 第 3 页获取到 100 条记录
2025-08-02 18:00:24,330 - INFO - Request Parameters - Page 4:
2025-08-02 18:00:24,330 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:24,330 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:24,830 - INFO - Response - Page 4:
2025-08-02 18:00:25,034 - INFO - 第 4 页获取到 100 条记录
2025-08-02 18:00:25,034 - INFO - Request Parameters - Page 5:
2025-08-02 18:00:25,034 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:25,034 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:25,534 - INFO - Response - Page 5:
2025-08-02 18:00:25,737 - INFO - 第 5 页获取到 100 条记录
2025-08-02 18:00:25,737 - INFO - Request Parameters - Page 6:
2025-08-02 18:00:25,737 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:25,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:26,252 - INFO - Response - Page 6:
2025-08-02 18:00:26,455 - INFO - 第 6 页获取到 100 条记录
2025-08-02 18:00:26,455 - INFO - Request Parameters - Page 7:
2025-08-02 18:00:26,455 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:26,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:26,955 - INFO - Response - Page 7:
2025-08-02 18:00:27,159 - INFO - 第 7 页获取到 56 条记录
2025-08-02 18:00:27,159 - INFO - 查询完成，共获取到 656 条记录
2025-08-02 18:00:27,159 - INFO - 获取到 656 条表单数据
2025-08-02 18:00:27,159 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-02 18:00:27,174 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:27,174 - INFO - 开始处理日期: 2025-05
2025-08-02 18:00:27,174 - INFO - Request Parameters - Page 1:
2025-08-02 18:00:27,174 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:27,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:27,721 - INFO - Response - Page 1:
2025-08-02 18:00:27,924 - INFO - 第 1 页获取到 100 条记录
2025-08-02 18:00:27,924 - INFO - Request Parameters - Page 2:
2025-08-02 18:00:27,924 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:27,924 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:28,487 - INFO - Response - Page 2:
2025-08-02 18:00:28,690 - INFO - 第 2 页获取到 100 条记录
2025-08-02 18:00:28,690 - INFO - Request Parameters - Page 3:
2025-08-02 18:00:28,690 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:28,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:29,221 - INFO - Response - Page 3:
2025-08-02 18:00:29,424 - INFO - 第 3 页获取到 100 条记录
2025-08-02 18:00:29,424 - INFO - Request Parameters - Page 4:
2025-08-02 18:00:29,424 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:29,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:30,018 - INFO - Response - Page 4:
2025-08-02 18:00:30,221 - INFO - 第 4 页获取到 100 条记录
2025-08-02 18:00:30,221 - INFO - Request Parameters - Page 5:
2025-08-02 18:00:30,221 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:30,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:30,768 - INFO - Response - Page 5:
2025-08-02 18:00:30,971 - INFO - 第 5 页获取到 100 条记录
2025-08-02 18:00:30,971 - INFO - Request Parameters - Page 6:
2025-08-02 18:00:30,971 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:30,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:31,534 - INFO - Response - Page 6:
2025-08-02 18:00:31,737 - INFO - 第 6 页获取到 100 条记录
2025-08-02 18:00:31,737 - INFO - Request Parameters - Page 7:
2025-08-02 18:00:31,737 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:31,737 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:32,221 - INFO - Response - Page 7:
2025-08-02 18:00:32,424 - INFO - 第 7 页获取到 65 条记录
2025-08-02 18:00:32,424 - INFO - 查询完成，共获取到 665 条记录
2025-08-02 18:00:32,424 - INFO - 获取到 665 条表单数据
2025-08-02 18:00:32,424 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-02 18:00:32,440 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:32,440 - INFO - 开始处理日期: 2025-06
2025-08-02 18:00:32,440 - INFO - Request Parameters - Page 1:
2025-08-02 18:00:32,440 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:32,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:32,987 - INFO - Response - Page 1:
2025-08-02 18:00:33,190 - INFO - 第 1 页获取到 100 条记录
2025-08-02 18:00:33,190 - INFO - Request Parameters - Page 2:
2025-08-02 18:00:33,190 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:33,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:33,737 - INFO - Response - Page 2:
2025-08-02 18:00:33,940 - INFO - 第 2 页获取到 100 条记录
2025-08-02 18:00:33,940 - INFO - Request Parameters - Page 3:
2025-08-02 18:00:33,940 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:33,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:34,487 - INFO - Response - Page 3:
2025-08-02 18:00:34,690 - INFO - 第 3 页获取到 100 条记录
2025-08-02 18:00:34,690 - INFO - Request Parameters - Page 4:
2025-08-02 18:00:34,690 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:34,690 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:35,190 - INFO - Response - Page 4:
2025-08-02 18:00:35,393 - INFO - 第 4 页获取到 100 条记录
2025-08-02 18:00:35,393 - INFO - Request Parameters - Page 5:
2025-08-02 18:00:35,393 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:35,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:35,893 - INFO - Response - Page 5:
2025-08-02 18:00:36,096 - INFO - 第 5 页获取到 100 条记录
2025-08-02 18:00:36,096 - INFO - Request Parameters - Page 6:
2025-08-02 18:00:36,096 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:36,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:36,705 - INFO - Response - Page 6:
2025-08-02 18:00:36,908 - INFO - 第 6 页获取到 100 条记录
2025-08-02 18:00:36,908 - INFO - Request Parameters - Page 7:
2025-08-02 18:00:36,908 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:36,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:37,393 - INFO - Response - Page 7:
2025-08-02 18:00:37,596 - INFO - 第 7 页获取到 61 条记录
2025-08-02 18:00:37,596 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 18:00:37,596 - INFO - 获取到 661 条表单数据
2025-08-02 18:00:37,596 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-02 18:00:37,612 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:37,612 - INFO - 开始处理日期: 2025-07
2025-08-02 18:00:37,612 - INFO - Request Parameters - Page 1:
2025-08-02 18:00:37,612 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:37,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:38,143 - INFO - Response - Page 1:
2025-08-02 18:00:38,346 - INFO - 第 1 页获取到 100 条记录
2025-08-02 18:00:38,346 - INFO - Request Parameters - Page 2:
2025-08-02 18:00:38,346 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:38,346 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:38,877 - INFO - Response - Page 2:
2025-08-02 18:00:39,080 - INFO - 第 2 页获取到 100 条记录
2025-08-02 18:00:39,080 - INFO - Request Parameters - Page 3:
2025-08-02 18:00:39,080 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:39,080 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:39,643 - INFO - Response - Page 3:
2025-08-02 18:00:39,846 - INFO - 第 3 页获取到 100 条记录
2025-08-02 18:00:39,846 - INFO - Request Parameters - Page 4:
2025-08-02 18:00:39,846 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:39,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:40,393 - INFO - Response - Page 4:
2025-08-02 18:00:40,596 - INFO - 第 4 页获取到 100 条记录
2025-08-02 18:00:40,596 - INFO - Request Parameters - Page 5:
2025-08-02 18:00:40,596 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:40,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:41,221 - INFO - Response - Page 5:
2025-08-02 18:00:41,424 - INFO - 第 5 页获取到 100 条记录
2025-08-02 18:00:41,424 - INFO - Request Parameters - Page 6:
2025-08-02 18:00:41,424 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:41,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:41,971 - INFO - Response - Page 6:
2025-08-02 18:00:42,174 - INFO - 第 6 页获取到 100 条记录
2025-08-02 18:00:42,174 - INFO - Request Parameters - Page 7:
2025-08-02 18:00:42,174 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:42,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:42,502 - INFO - Response - Page 7:
2025-08-02 18:00:42,705 - INFO - 第 7 页获取到 19 条记录
2025-08-02 18:00:42,705 - INFO - 查询完成，共获取到 619 条记录
2025-08-02 18:00:42,705 - INFO - 获取到 619 条表单数据
2025-08-02 18:00:42,705 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-02 18:00:42,721 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:42,721 - INFO - 开始处理日期: 2025-08
2025-08-02 18:00:42,721 - INFO - Request Parameters - Page 1:
2025-08-02 18:00:42,721 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:42,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:43,252 - INFO - Response - Page 1:
2025-08-02 18:00:43,455 - INFO - 第 1 页获取到 100 条记录
2025-08-02 18:00:43,455 - INFO - Request Parameters - Page 2:
2025-08-02 18:00:43,455 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:43,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:43,955 - INFO - Response - Page 2:
2025-08-02 18:00:44,158 - INFO - 第 2 页获取到 100 条记录
2025-08-02 18:00:44,158 - INFO - Request Parameters - Page 3:
2025-08-02 18:00:44,158 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:44,158 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:44,705 - INFO - Response - Page 3:
2025-08-02 18:00:44,908 - INFO - 第 3 页获取到 100 条记录
2025-08-02 18:00:44,908 - INFO - Request Parameters - Page 4:
2025-08-02 18:00:44,908 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:44,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:45,455 - INFO - Response - Page 4:
2025-08-02 18:00:45,658 - INFO - 第 4 页获取到 100 条记录
2025-08-02 18:00:45,658 - INFO - Request Parameters - Page 5:
2025-08-02 18:00:45,658 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 18:00:45,658 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 18:00:46,143 - INFO - Response - Page 5:
2025-08-02 18:00:46,346 - INFO - 第 5 页获取到 93 条记录
2025-08-02 18:00:46,346 - INFO - 查询完成，共获取到 493 条记录
2025-08-02 18:00:46,346 - INFO - 获取到 493 条表单数据
2025-08-02 18:00:46,346 - INFO - 当前日期 2025-08 有 493 条MySQL数据需要处理
2025-08-02 18:00:46,362 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM97
2025-08-02 18:00:46,799 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDM97
2025-08-02 18:00:46,799 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 8000.0, 'new_value': 9711.89}, {'field': 'total_amount', 'old_value': 8000.0, 'new_value': 9711.89}, {'field': 'order_count', 'old_value': 440, 'new_value': 432}]
2025-08-02 18:00:46,799 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMB7
2025-08-02 18:00:47,283 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMB7
2025-08-02 18:00:47,283 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 6000.0, 'new_value': 1945.23}, {'field': 'total_amount', 'old_value': 6000.0, 'new_value': 1945.23}, {'field': 'order_count', 'old_value': 90, 'new_value': 289}]
2025-08-02 18:00:47,283 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMC7
2025-08-02 18:00:47,752 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMC7
2025-08-02 18:00:47,752 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 12683.34}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 12683.34}, {'field': 'order_count', 'old_value': 0, 'new_value': 40}]
2025-08-02 18:00:47,752 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMD7
2025-08-02 18:00:48,158 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMD7
2025-08-02 18:00:48,158 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 328342.73}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 328342.73}, {'field': 'order_count', 'old_value': 0, 'new_value': 81}]
2025-08-02 18:00:48,158 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDME7
2025-08-02 18:00:48,690 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDME7
2025-08-02 18:00:48,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 14703.63}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 14703.63}, {'field': 'order_count', 'old_value': 0, 'new_value': 139}]
2025-08-02 18:00:48,690 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMF7
2025-08-02 18:00:49,237 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMF7
2025-08-02 18:00:49,237 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 3640.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 3640.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 18:00:49,237 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMG7
2025-08-02 18:00:49,721 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMG7
2025-08-02 18:00:49,721 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 798.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 798.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-08-02 18:00:49,721 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMH7
2025-08-02 18:00:50,252 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMH7
2025-08-02 18:00:50,252 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1800.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 18:00:50,252 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMI7
2025-08-02 18:00:50,690 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMI7
2025-08-02 18:00:50,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 80}]
2025-08-02 18:00:50,690 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMN7
2025-08-02 18:00:51,190 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMN7
2025-08-02 18:00:51,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1250.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1250.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 18:00:51,190 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMO7
2025-08-02 18:00:51,690 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMO7
2025-08-02 18:00:51,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 12119.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 12119.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 321}]
2025-08-02 18:00:51,690 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMP7
2025-08-02 18:00:52,190 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMP7
2025-08-02 18:00:52,190 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 50000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 18:00:52,190 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMQ7
2025-08-02 18:00:52,690 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMQ7
2025-08-02 18:00:52,690 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 1999.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 1999.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 18:00:52,690 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMR7
2025-08-02 18:00:53,111 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMR7
2025-08-02 18:00:53,111 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4944.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4944.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 394}]
2025-08-02 18:00:53,111 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMS7
2025-08-02 18:00:53,549 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMS7
2025-08-02 18:00:53,549 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 280000.0, 'new_value': 254298.0}, {'field': 'total_amount', 'old_value': 280000.0, 'new_value': 254298.0}, {'field': 'order_count', 'old_value': 4000, 'new_value': 4495}]
2025-08-02 18:00:53,549 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMV7
2025-08-02 18:00:54,065 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMV7
2025-08-02 18:00:54,065 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 4856.15}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 4856.15}, {'field': 'order_count', 'old_value': 0, 'new_value': 270}]
2025-08-02 18:00:54,065 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMW7
2025-08-02 18:00:54,486 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMW7
2025-08-02 18:00:54,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 349.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 349.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 11}]
2025-08-02 18:00:54,486 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMX7
2025-08-02 18:00:54,955 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMX7
2025-08-02 18:00:54,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 9804.35}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 9804.35}, {'field': 'order_count', 'old_value': 0, 'new_value': 466}]
2025-08-02 18:00:54,955 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMY7
2025-08-02 18:00:55,424 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3SAA6QTDMY7
2025-08-02 18:00:55,424 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 14128.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 14128.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 13}]
2025-08-02 18:00:55,424 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM08
2025-08-02 18:00:55,908 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM08
2025-08-02 18:00:55,908 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 500.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 500.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-08-02 18:00:55,908 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM58
2025-08-02 18:00:56,486 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM58
2025-08-02 18:00:56,486 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 25259.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 25259.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}]
2025-08-02 18:00:56,486 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM68
2025-08-02 18:00:56,924 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM68
2025-08-02 18:00:56,924 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 12000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 12000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 18:00:56,924 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM78
2025-08-02 18:00:57,440 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM78
2025-08-02 18:00:57,440 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5232.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5232.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 2}]
2025-08-02 18:00:57,440 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM88
2025-08-02 18:00:57,955 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDM88
2025-08-02 18:00:57,955 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 7000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 7000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 18:00:57,955 - INFO - 开始更新记录 - 表单实例ID: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMA8
2025-08-02 18:00:58,408 - INFO - 更新表单数据成功: FINST-00D66K71B9MXFQ5YA9Z1Z6SJZPZC3TAA6QTDMA8
2025-08-02 18:00:58,408 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 6772.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 6772.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}]
2025-08-02 18:00:58,408 - INFO - 日期 2025-08 处理完成 - 更新: 25 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:58,408 - INFO - 数据同步完成！更新: 25 条，插入: 0 条，错误: 0 条
2025-08-02 18:00:58,408 - INFO - =================同步完成====================
2025-08-02 21:00:02,460 - INFO - =================使用默认全量同步=============
2025-08-02 21:00:04,663 - INFO - MySQL查询成功，共获取 5107 条记录
2025-08-02 21:00:04,663 - INFO - 获取到 8 个日期需要处理: ['2025-01', '2025-02', '2025-03', '2025-04', '2025-05', '2025-06', '2025-07', '2025-08']
2025-08-02 21:00:04,710 - INFO - 开始处理日期: 2025-01
2025-08-02 21:00:04,710 - INFO - Request Parameters - Page 1:
2025-08-02 21:00:04,710 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:04,710 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:06,569 - INFO - Response - Page 1:
2025-08-02 21:00:06,772 - INFO - 第 1 页获取到 100 条记录
2025-08-02 21:00:06,772 - INFO - Request Parameters - Page 2:
2025-08-02 21:00:06,772 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:06,772 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:07,397 - INFO - Response - Page 2:
2025-08-02 21:00:07,600 - INFO - 第 2 页获取到 100 条记录
2025-08-02 21:00:07,600 - INFO - Request Parameters - Page 3:
2025-08-02 21:00:07,600 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:07,600 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:08,178 - INFO - Response - Page 3:
2025-08-02 21:00:08,381 - INFO - 第 3 页获取到 100 条记录
2025-08-02 21:00:08,381 - INFO - Request Parameters - Page 4:
2025-08-02 21:00:08,381 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:08,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:08,944 - INFO - Response - Page 4:
2025-08-02 21:00:09,147 - INFO - 第 4 页获取到 100 条记录
2025-08-02 21:00:09,147 - INFO - Request Parameters - Page 5:
2025-08-02 21:00:09,147 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:09,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:09,678 - INFO - Response - Page 5:
2025-08-02 21:00:09,881 - INFO - 第 5 页获取到 100 条记录
2025-08-02 21:00:09,881 - INFO - Request Parameters - Page 6:
2025-08-02 21:00:09,881 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:09,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:10,537 - INFO - Response - Page 6:
2025-08-02 21:00:10,740 - INFO - 第 6 页获取到 100 条记录
2025-08-02 21:00:10,740 - INFO - Request Parameters - Page 7:
2025-08-02 21:00:10,740 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:10,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1735660800000, 1738339199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:11,272 - INFO - Response - Page 7:
2025-08-02 21:00:11,475 - INFO - 第 7 页获取到 82 条记录
2025-08-02 21:00:11,475 - INFO - 查询完成，共获取到 682 条记录
2025-08-02 21:00:11,475 - INFO - 获取到 682 条表单数据
2025-08-02 21:00:11,475 - INFO - 当前日期 2025-01 有 682 条MySQL数据需要处理
2025-08-02 21:00:11,490 - INFO - 日期 2025-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:11,490 - INFO - 开始处理日期: 2025-02
2025-08-02 21:00:11,490 - INFO - Request Parameters - Page 1:
2025-08-02 21:00:11,490 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:11,490 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:12,037 - INFO - Response - Page 1:
2025-08-02 21:00:12,240 - INFO - 第 1 页获取到 100 条记录
2025-08-02 21:00:12,240 - INFO - Request Parameters - Page 2:
2025-08-02 21:00:12,240 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:12,240 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:12,818 - INFO - Response - Page 2:
2025-08-02 21:00:13,021 - INFO - 第 2 页获取到 100 条记录
2025-08-02 21:00:13,021 - INFO - Request Parameters - Page 3:
2025-08-02 21:00:13,021 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:13,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:13,553 - INFO - Response - Page 3:
2025-08-02 21:00:13,756 - INFO - 第 3 页获取到 100 条记录
2025-08-02 21:00:13,756 - INFO - Request Parameters - Page 4:
2025-08-02 21:00:13,756 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:13,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:14,271 - INFO - Response - Page 4:
2025-08-02 21:00:14,474 - INFO - 第 4 页获取到 100 条记录
2025-08-02 21:00:14,474 - INFO - Request Parameters - Page 5:
2025-08-02 21:00:14,474 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:14,474 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:14,959 - INFO - Response - Page 5:
2025-08-02 21:00:15,162 - INFO - 第 5 页获取到 100 条记录
2025-08-02 21:00:15,162 - INFO - Request Parameters - Page 6:
2025-08-02 21:00:15,162 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:15,162 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:15,662 - INFO - Response - Page 6:
2025-08-02 21:00:15,865 - INFO - 第 6 页获取到 100 条记录
2025-08-02 21:00:15,865 - INFO - Request Parameters - Page 7:
2025-08-02 21:00:15,865 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:15,865 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1738339200000, 1740758399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:16,381 - INFO - Response - Page 7:
2025-08-02 21:00:16,584 - INFO - 第 7 页获取到 70 条记录
2025-08-02 21:00:16,584 - INFO - 查询完成，共获取到 670 条记录
2025-08-02 21:00:16,584 - INFO - 获取到 670 条表单数据
2025-08-02 21:00:16,584 - INFO - 当前日期 2025-02 有 670 条MySQL数据需要处理
2025-08-02 21:00:16,599 - INFO - 日期 2025-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:16,599 - INFO - 开始处理日期: 2025-03
2025-08-02 21:00:16,599 - INFO - Request Parameters - Page 1:
2025-08-02 21:00:16,599 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:16,599 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:17,146 - INFO - Response - Page 1:
2025-08-02 21:00:17,349 - INFO - 第 1 页获取到 100 条记录
2025-08-02 21:00:17,349 - INFO - Request Parameters - Page 2:
2025-08-02 21:00:17,349 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:17,349 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:17,896 - INFO - Response - Page 2:
2025-08-02 21:00:18,099 - INFO - 第 2 页获取到 100 条记录
2025-08-02 21:00:18,099 - INFO - Request Parameters - Page 3:
2025-08-02 21:00:18,099 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:18,099 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:18,693 - INFO - Response - Page 3:
2025-08-02 21:00:18,896 - INFO - 第 3 页获取到 100 条记录
2025-08-02 21:00:18,896 - INFO - Request Parameters - Page 4:
2025-08-02 21:00:18,896 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:18,896 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:19,536 - INFO - Response - Page 4:
2025-08-02 21:00:19,740 - INFO - 第 4 页获取到 100 条记录
2025-08-02 21:00:19,740 - INFO - Request Parameters - Page 5:
2025-08-02 21:00:19,740 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:19,740 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:20,255 - INFO - Response - Page 5:
2025-08-02 21:00:20,458 - INFO - 第 5 页获取到 100 条记录
2025-08-02 21:00:20,458 - INFO - Request Parameters - Page 6:
2025-08-02 21:00:20,458 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:20,458 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:20,958 - INFO - Response - Page 6:
2025-08-02 21:00:21,161 - INFO - 第 6 页获取到 100 条记录
2025-08-02 21:00:21,161 - INFO - Request Parameters - Page 7:
2025-08-02 21:00:21,161 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:21,161 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1740758400000, 1743436799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:21,630 - INFO - Response - Page 7:
2025-08-02 21:00:21,833 - INFO - 第 7 页获取到 61 条记录
2025-08-02 21:00:21,833 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 21:00:21,833 - INFO - 获取到 661 条表单数据
2025-08-02 21:00:21,833 - INFO - 当前日期 2025-03 有 661 条MySQL数据需要处理
2025-08-02 21:00:21,849 - INFO - 日期 2025-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:21,849 - INFO - 开始处理日期: 2025-04
2025-08-02 21:00:21,849 - INFO - Request Parameters - Page 1:
2025-08-02 21:00:21,849 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:21,849 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:22,411 - INFO - Response - Page 1:
2025-08-02 21:00:22,614 - INFO - 第 1 页获取到 100 条记录
2025-08-02 21:00:22,614 - INFO - Request Parameters - Page 2:
2025-08-02 21:00:22,614 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:22,614 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:23,177 - INFO - Response - Page 2:
2025-08-02 21:00:23,380 - INFO - 第 2 页获取到 100 条记录
2025-08-02 21:00:23,380 - INFO - Request Parameters - Page 3:
2025-08-02 21:00:23,380 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:23,380 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:23,927 - INFO - Response - Page 3:
2025-08-02 21:00:24,130 - INFO - 第 3 页获取到 100 条记录
2025-08-02 21:00:24,130 - INFO - Request Parameters - Page 4:
2025-08-02 21:00:24,130 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:24,130 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:24,645 - INFO - Response - Page 4:
2025-08-02 21:00:24,848 - INFO - 第 4 页获取到 100 条记录
2025-08-02 21:00:24,848 - INFO - Request Parameters - Page 5:
2025-08-02 21:00:24,848 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:24,848 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:25,520 - INFO - Response - Page 5:
2025-08-02 21:00:25,723 - INFO - 第 5 页获取到 100 条记录
2025-08-02 21:00:25,723 - INFO - Request Parameters - Page 6:
2025-08-02 21:00:25,723 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:25,723 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:26,192 - INFO - Response - Page 6:
2025-08-02 21:00:26,395 - INFO - 第 6 页获取到 100 条记录
2025-08-02 21:00:26,395 - INFO - Request Parameters - Page 7:
2025-08-02 21:00:26,395 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:26,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1743436800000, 1746028799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:26,895 - INFO - Response - Page 7:
2025-08-02 21:00:27,098 - INFO - 第 7 页获取到 56 条记录
2025-08-02 21:00:27,098 - INFO - 查询完成，共获取到 656 条记录
2025-08-02 21:00:27,098 - INFO - 获取到 656 条表单数据
2025-08-02 21:00:27,098 - INFO - 当前日期 2025-04 有 656 条MySQL数据需要处理
2025-08-02 21:00:27,114 - INFO - 日期 2025-04 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:27,114 - INFO - 开始处理日期: 2025-05
2025-08-02 21:00:27,114 - INFO - Request Parameters - Page 1:
2025-08-02 21:00:27,114 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:27,114 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:27,692 - INFO - Response - Page 1:
2025-08-02 21:00:27,895 - INFO - 第 1 页获取到 100 条记录
2025-08-02 21:00:27,895 - INFO - Request Parameters - Page 2:
2025-08-02 21:00:27,895 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:27,895 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:28,442 - INFO - Response - Page 2:
2025-08-02 21:00:28,645 - INFO - 第 2 页获取到 100 条记录
2025-08-02 21:00:28,645 - INFO - Request Parameters - Page 3:
2025-08-02 21:00:28,645 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:28,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:29,145 - INFO - Response - Page 3:
2025-08-02 21:00:29,348 - INFO - 第 3 页获取到 100 条记录
2025-08-02 21:00:29,348 - INFO - Request Parameters - Page 4:
2025-08-02 21:00:29,348 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:29,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:29,900 - INFO - Response - Page 4:
2025-08-02 21:00:30,109 - INFO - 第 4 页获取到 100 条记录
2025-08-02 21:00:30,109 - INFO - Request Parameters - Page 5:
2025-08-02 21:00:30,109 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:30,109 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:30,624 - INFO - Response - Page 5:
2025-08-02 21:00:30,827 - INFO - 第 5 页获取到 100 条记录
2025-08-02 21:00:30,827 - INFO - Request Parameters - Page 6:
2025-08-02 21:00:30,827 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:30,827 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:31,343 - INFO - Response - Page 6:
2025-08-02 21:00:31,546 - INFO - 第 6 页获取到 100 条记录
2025-08-02 21:00:31,546 - INFO - Request Parameters - Page 7:
2025-08-02 21:00:31,546 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:31,546 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1746028800000, 1748707199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:32,077 - INFO - Response - Page 7:
2025-08-02 21:00:32,280 - INFO - 第 7 页获取到 65 条记录
2025-08-02 21:00:32,280 - INFO - 查询完成，共获取到 665 条记录
2025-08-02 21:00:32,280 - INFO - 获取到 665 条表单数据
2025-08-02 21:00:32,280 - INFO - 当前日期 2025-05 有 665 条MySQL数据需要处理
2025-08-02 21:00:32,296 - INFO - 日期 2025-05 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:32,296 - INFO - 开始处理日期: 2025-06
2025-08-02 21:00:32,296 - INFO - Request Parameters - Page 1:
2025-08-02 21:00:32,296 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:32,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:32,874 - INFO - Response - Page 1:
2025-08-02 21:00:33,077 - INFO - 第 1 页获取到 100 条记录
2025-08-02 21:00:33,077 - INFO - Request Parameters - Page 2:
2025-08-02 21:00:33,077 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:33,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:33,639 - INFO - Response - Page 2:
2025-08-02 21:00:33,843 - INFO - 第 2 页获取到 100 条记录
2025-08-02 21:00:33,843 - INFO - Request Parameters - Page 3:
2025-08-02 21:00:33,843 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:33,843 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:34,405 - INFO - Response - Page 3:
2025-08-02 21:00:34,608 - INFO - 第 3 页获取到 100 条记录
2025-08-02 21:00:34,608 - INFO - Request Parameters - Page 4:
2025-08-02 21:00:34,608 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:34,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:35,077 - INFO - Response - Page 4:
2025-08-02 21:00:35,280 - INFO - 第 4 页获取到 100 条记录
2025-08-02 21:00:35,280 - INFO - Request Parameters - Page 5:
2025-08-02 21:00:35,280 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:35,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:35,842 - INFO - Response - Page 5:
2025-08-02 21:00:36,045 - INFO - 第 5 页获取到 100 条记录
2025-08-02 21:00:36,045 - INFO - Request Parameters - Page 6:
2025-08-02 21:00:36,045 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:36,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:36,592 - INFO - Response - Page 6:
2025-08-02 21:00:36,795 - INFO - 第 6 页获取到 100 条记录
2025-08-02 21:00:36,795 - INFO - Request Parameters - Page 7:
2025-08-02 21:00:36,795 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:36,795 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1748707200000, 1751299199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:37,280 - INFO - Response - Page 7:
2025-08-02 21:00:37,483 - INFO - 第 7 页获取到 61 条记录
2025-08-02 21:00:37,483 - INFO - 查询完成，共获取到 661 条记录
2025-08-02 21:00:37,483 - INFO - 获取到 661 条表单数据
2025-08-02 21:00:37,483 - INFO - 当前日期 2025-06 有 661 条MySQL数据需要处理
2025-08-02 21:00:37,498 - INFO - 日期 2025-06 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:37,498 - INFO - 开始处理日期: 2025-07
2025-08-02 21:00:37,498 - INFO - Request Parameters - Page 1:
2025-08-02 21:00:37,498 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:37,498 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:38,077 - INFO - Response - Page 1:
2025-08-02 21:00:38,280 - INFO - 第 1 页获取到 100 条记录
2025-08-02 21:00:38,280 - INFO - Request Parameters - Page 2:
2025-08-02 21:00:38,280 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:38,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:38,858 - INFO - Response - Page 2:
2025-08-02 21:00:39,061 - INFO - 第 2 页获取到 100 条记录
2025-08-02 21:00:39,061 - INFO - Request Parameters - Page 3:
2025-08-02 21:00:39,061 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:39,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:39,655 - INFO - Response - Page 3:
2025-08-02 21:00:39,858 - INFO - 第 3 页获取到 100 条记录
2025-08-02 21:00:39,858 - INFO - Request Parameters - Page 4:
2025-08-02 21:00:39,858 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:39,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:40,358 - INFO - Response - Page 4:
2025-08-02 21:00:40,561 - INFO - 第 4 页获取到 100 条记录
2025-08-02 21:00:40,561 - INFO - Request Parameters - Page 5:
2025-08-02 21:00:40,561 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:40,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:41,061 - INFO - Response - Page 5:
2025-08-02 21:00:41,264 - INFO - 第 5 页获取到 100 条记录
2025-08-02 21:00:41,264 - INFO - Request Parameters - Page 6:
2025-08-02 21:00:41,264 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:41,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 6, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:41,842 - INFO - Response - Page 6:
2025-08-02 21:00:42,045 - INFO - 第 6 页获取到 100 条记录
2025-08-02 21:00:42,045 - INFO - Request Parameters - Page 7:
2025-08-02 21:00:42,045 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:42,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 7, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1751299200000, 1753977599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:42,389 - INFO - Response - Page 7:
2025-08-02 21:00:42,592 - INFO - 第 7 页获取到 19 条记录
2025-08-02 21:00:42,592 - INFO - 查询完成，共获取到 619 条记录
2025-08-02 21:00:42,592 - INFO - 获取到 619 条表单数据
2025-08-02 21:00:42,592 - INFO - 当前日期 2025-07 有 619 条MySQL数据需要处理
2025-08-02 21:00:42,607 - INFO - 日期 2025-07 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:42,607 - INFO - 开始处理日期: 2025-08
2025-08-02 21:00:42,607 - INFO - Request Parameters - Page 1:
2025-08-02 21:00:42,607 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:42,607 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 1, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:43,217 - INFO - Response - Page 1:
2025-08-02 21:00:43,420 - INFO - 第 1 页获取到 100 条记录
2025-08-02 21:00:43,420 - INFO - Request Parameters - Page 2:
2025-08-02 21:00:43,420 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:43,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 2, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:43,967 - INFO - Response - Page 2:
2025-08-02 21:00:44,170 - INFO - 第 2 页获取到 100 条记录
2025-08-02 21:00:44,170 - INFO - Request Parameters - Page 3:
2025-08-02 21:00:44,170 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:44,170 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 3, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:44,779 - INFO - Response - Page 3:
2025-08-02 21:00:44,982 - INFO - 第 3 页获取到 100 条记录
2025-08-02 21:00:44,982 - INFO - Request Parameters - Page 4:
2025-08-02 21:00:44,982 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:44,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 4, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:45,451 - INFO - Response - Page 4:
2025-08-02 21:00:45,654 - INFO - 第 4 页获取到 100 条记录
2025-08-02 21:00:45,654 - INFO - Request Parameters - Page 5:
2025-08-02 21:00:45,654 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 21:00:45,654 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5', 'pageNumber': 5, 'pageSize': 100, 'searchCondition': '[{"key": "dateField_m9tojheu", "value": [1753977600000, 1756655999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 21:00:46,216 - INFO - Response - Page 5:
2025-08-02 21:00:46,419 - INFO - 第 5 页获取到 93 条记录
2025-08-02 21:00:46,419 - INFO - 查询完成，共获取到 493 条记录
2025-08-02 21:00:46,419 - INFO - 获取到 493 条表单数据
2025-08-02 21:00:46,419 - INFO - 当前日期 2025-08 有 493 条MySQL数据需要处理
2025-08-02 21:00:46,419 - INFO - 开始更新记录 - 表单实例ID: FINST-BTF66DB12KNXTMWZ9L8WR4J2CTDU2BZO0USDMB3
2025-08-02 21:00:46,935 - INFO - 更新表单数据成功: FINST-BTF66DB12KNXTMWZ9L8WR4J2CTDU2BZO0USDMB3
2025-08-02 21:00:46,935 - INFO - 更新记录成功，变更字段: [{'field': 'online_amount', 'old_value': 3122.0, 'new_value': 6245.0}, {'field': 'offline_amount', 'old_value': 4422.0, 'new_value': 8644.0}, {'field': 'total_amount', 'old_value': 7544.0, 'new_value': 14889.0}, {'field': 'order_count', 'old_value': 142, 'new_value': 276}]
2025-08-02 21:00:46,951 - INFO - 日期 2025-08 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:46,951 - INFO - 数据同步完成！更新: 1 条，插入: 0 条，错误: 0 条
2025-08-02 21:00:46,951 - INFO - =================同步完成====================
