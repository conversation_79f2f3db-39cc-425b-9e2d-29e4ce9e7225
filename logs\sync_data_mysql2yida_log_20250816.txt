2025-08-16 01:30:33,503 - INFO - 使用默认增量同步（当天更新数据）
2025-08-16 01:30:33,503 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-16 01:30:33,503 - INFO - 查询参数: ('2025-08-16',)
2025-08-16 01:30:33,659 - INFO - MySQL查询成功，增量数据（日期: 2025-08-16），共获取 3 条记录
2025-08-16 01:30:33,675 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 01:30:33,675 - INFO - 开始处理日期: 2025-08-15
2025-08-16 01:30:33,675 - INFO - Request Parameters - Page 1:
2025-08-16 01:30:33,675 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 01:30:33,675 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 01:30:41,799 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: A376AE06-4630-725F-93AA-BE8CB0A76095 Response: {'code': 'ServiceUnavailable', 'requestid': 'A376AE06-4630-725F-93AA-BE8CB0A76095', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: A376AE06-4630-725F-93AA-BE8CB0A76095)
2025-08-16 01:30:41,799 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-16 01:31:41,810 - INFO - 开始同步昨天与今天的销售数据: 2025-08-15 至 2025-08-16
2025-08-16 01:31:41,810 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-16 01:31:41,810 - INFO - 查询参数: ('2025-08-15', '2025-08-16')
2025-08-16 01:31:41,982 - INFO - MySQL查询成功，时间段: 2025-08-15 至 2025-08-16，共获取 99 条记录
2025-08-16 01:31:41,982 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 01:31:41,982 - INFO - 开始处理日期: 2025-08-15
2025-08-16 01:31:41,982 - INFO - Request Parameters - Page 1:
2025-08-16 01:31:41,982 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 01:31:41,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 01:31:42,779 - INFO - Response - Page 1:
2025-08-16 01:31:42,779 - INFO - 第 1 页获取到 39 条记录
2025-08-16 01:31:43,279 - INFO - 查询完成，共获取到 39 条记录
2025-08-16 01:31:43,279 - INFO - 获取到 39 条表单数据
2025-08-16 01:31:43,279 - INFO - 当前日期 2025-08-15 有 95 条MySQL数据需要处理
2025-08-16 01:31:43,279 - INFO - 开始批量插入 56 条新记录
2025-08-16 01:31:43,513 - INFO - 批量插入响应状态码: 200
2025-08-16 01:31:43,513 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 17:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '223C87E8-CC5A-74D8-AD15-54C7627FF1D2', 'x-acs-trace-id': 'a13eee60ba79734074640b5a4fa1c928', 'etag': '2iHe1NzrPykw0/qRA2rYNnw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 01:31:43,513 - INFO - 批量插入响应体: {'result': ['FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM73', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM83', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM93', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMA3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMB3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMC3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMD3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEME3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMF3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMG3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMH3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMI3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMJ3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMK3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEML3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMM3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMN3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMO3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMP3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMQ3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMR3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMS3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMT3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMU3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMV3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMW3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMX3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMY3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMZ3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM04', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM14', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM24', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM34', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM44', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM54', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM64', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM74', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM84', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM94', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMA4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMB4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMC4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMD4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEME4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMF4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMG4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMH4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMI4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMJ4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMK4']}
2025-08-16 01:31:43,513 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-16 01:31:43,513 - INFO - 成功插入的数据ID: ['FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM73', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM83', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM93', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMA3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMB3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMC3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMD3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEME3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMF3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMG3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMH3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMI3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMJ3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMK3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEML3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMM3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMN3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMO3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMP3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMQ3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMR3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMS3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMT3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMU3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMV3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMW3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMX3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMY3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMZ3', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM04', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM14', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM24', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM34', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM44', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM54', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM64', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM74', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM84', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEM94', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMA4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMB4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMC4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMD4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEME4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMF4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMG4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMH4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMI4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMJ4', 'FINST-0O9664D1BY1YSS4UASZ2OBIXOHQ23FCWU3DEMK4']
2025-08-16 01:31:48,685 - INFO - 批量插入响应状态码: 200
2025-08-16 01:31:48,685 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 15 Aug 2025 17:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '300', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0DE07DE5-BA3F-78F3-AF2A-C0F8DEEF917E', 'x-acs-trace-id': '9aed4ecb773e5adea63e0f429a94e7b0', 'etag': '3R6drl68J7seN29ql5t3SJg0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 01:31:48,685 - INFO - 批量插入响应体: {'result': ['FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEM62', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEM72', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEM82', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEM92', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEMA2', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEMB2']}
2025-08-16 01:31:48,685 - INFO - 批量插入表单数据成功，批次 2，共 6 条记录
2025-08-16 01:31:48,685 - INFO - 成功插入的数据ID: ['FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEM62', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEM72', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEM82', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEM92', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEMA2', 'FINST-2PF66CD17Y1YOU7UEXEJSAKDB0PR21C0V3DEMB2']
2025-08-16 01:31:53,695 - INFO - 批量插入完成，共 56 条记录
2025-08-16 01:31:53,695 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 56 条，错误: 0 条
2025-08-16 01:31:53,695 - INFO - 数据同步完成！更新: 0 条，插入: 56 条，错误: 0 条
2025-08-16 01:31:53,695 - INFO - 同步完成
2025-08-16 04:30:33,659 - INFO - 使用默认增量同步（当天更新数据）
2025-08-16 04:30:33,659 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-16 04:30:33,659 - INFO - 查询参数: ('2025-08-16',)
2025-08-16 04:30:33,830 - INFO - MySQL查询成功，增量数据（日期: 2025-08-16），共获取 3 条记录
2025-08-16 04:30:33,830 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 04:30:33,830 - INFO - 开始处理日期: 2025-08-15
2025-08-16 04:30:33,830 - INFO - Request Parameters - Page 1:
2025-08-16 04:30:33,830 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 04:30:33,830 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 04:30:41,940 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 59F74C5F-6601-7FA3-8DCE-E2462CEFA5DE Response: {'code': 'ServiceUnavailable', 'requestid': '59F74C5F-6601-7FA3-8DCE-E2462CEFA5DE', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 59F74C5F-6601-7FA3-8DCE-E2462CEFA5DE)
2025-08-16 04:30:41,940 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-16 04:31:41,950 - INFO - 开始同步昨天与今天的销售数据: 2025-08-15 至 2025-08-16
2025-08-16 04:31:41,950 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-16 04:31:41,950 - INFO - 查询参数: ('2025-08-15', '2025-08-16')
2025-08-16 04:31:42,106 - INFO - MySQL查询成功，时间段: 2025-08-15 至 2025-08-16，共获取 99 条记录
2025-08-16 04:31:42,106 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 04:31:42,122 - INFO - 开始处理日期: 2025-08-15
2025-08-16 04:31:42,122 - INFO - Request Parameters - Page 1:
2025-08-16 04:31:42,122 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 04:31:42,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 04:31:50,216 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 4E22647F-A6FD-7DBF-9C5B-F41C263605AF Response: {'code': 'ServiceUnavailable', 'requestid': '4E22647F-A6FD-7DBF-9C5B-F41C263605AF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 4E22647F-A6FD-7DBF-9C5B-F41C263605AF)
2025-08-16 04:31:50,216 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-16 04:31:50,216 - INFO - 同步完成
2025-08-16 07:30:33,829 - INFO - 使用默认增量同步（当天更新数据）
2025-08-16 07:30:33,829 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-16 07:30:33,829 - INFO - 查询参数: ('2025-08-16',)
2025-08-16 07:30:34,001 - INFO - MySQL查询成功，增量数据（日期: 2025-08-16），共获取 3 条记录
2025-08-16 07:30:34,001 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 07:30:34,001 - INFO - 开始处理日期: 2025-08-15
2025-08-16 07:30:34,001 - INFO - Request Parameters - Page 1:
2025-08-16 07:30:34,001 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 07:30:34,001 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 07:30:42,110 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 2EB567B2-C06B-763D-829E-47CD961E501E Response: {'code': 'ServiceUnavailable', 'requestid': '2EB567B2-C06B-763D-829E-47CD961E501E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 2EB567B2-C06B-763D-829E-47CD961E501E)
2025-08-16 07:30:42,110 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-16 07:31:42,121 - INFO - 开始同步昨天与今天的销售数据: 2025-08-15 至 2025-08-16
2025-08-16 07:31:42,121 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-16 07:31:42,121 - INFO - 查询参数: ('2025-08-15', '2025-08-16')
2025-08-16 07:31:42,277 - INFO - MySQL查询成功，时间段: 2025-08-15 至 2025-08-16，共获取 99 条记录
2025-08-16 07:31:42,277 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 07:31:42,292 - INFO - 开始处理日期: 2025-08-15
2025-08-16 07:31:42,292 - INFO - Request Parameters - Page 1:
2025-08-16 07:31:42,292 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 07:31:42,292 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 07:31:43,089 - INFO - Response - Page 1:
2025-08-16 07:31:43,089 - INFO - 第 1 页获取到 50 条记录
2025-08-16 07:31:43,605 - INFO - Request Parameters - Page 2:
2025-08-16 07:31:43,605 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 07:31:43,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 07:31:51,730 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7D22B8F0-A320-7DB1-B0FF-3540059C70FD Response: {'code': 'ServiceUnavailable', 'requestid': '7D22B8F0-A320-7DB1-B0FF-3540059C70FD', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7D22B8F0-A320-7DB1-B0FF-3540059C70FD)
2025-08-16 07:31:51,730 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-16 07:31:51,730 - INFO - 同步完成
2025-08-16 10:30:33,672 - INFO - 使用默认增量同步（当天更新数据）
2025-08-16 10:30:33,672 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-16 10:30:33,672 - INFO - 查询参数: ('2025-08-16',)
2025-08-16 10:30:33,844 - INFO - MySQL查询成功，增量数据（日期: 2025-08-16），共获取 111 条记录
2025-08-16 10:30:33,844 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 10:30:33,844 - INFO - 开始处理日期: 2025-08-15
2025-08-16 10:30:33,859 - INFO - Request Parameters - Page 1:
2025-08-16 10:30:33,859 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 10:30:33,859 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 10:30:41,968 - ERROR - 处理日期 2025-08-15 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F4AEBD25-DF43-7FBE-9156-E20FDF5146E4 Response: {'code': 'ServiceUnavailable', 'requestid': 'F4AEBD25-DF43-7FBE-9156-E20FDF5146E4', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F4AEBD25-DF43-7FBE-9156-E20FDF5146E4)
2025-08-16 10:30:41,968 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-16 10:31:41,979 - INFO - 开始同步昨天与今天的销售数据: 2025-08-15 至 2025-08-16
2025-08-16 10:31:41,979 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-16 10:31:41,979 - INFO - 查询参数: ('2025-08-15', '2025-08-16')
2025-08-16 10:31:42,166 - INFO - MySQL查询成功，时间段: 2025-08-15 至 2025-08-16，共获取 424 条记录
2025-08-16 10:31:42,166 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 10:31:42,166 - INFO - 开始处理日期: 2025-08-15
2025-08-16 10:31:42,166 - INFO - Request Parameters - Page 1:
2025-08-16 10:31:42,166 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 10:31:42,166 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 10:31:43,026 - INFO - Response - Page 1:
2025-08-16 10:31:43,026 - INFO - 第 1 页获取到 50 条记录
2025-08-16 10:31:43,541 - INFO - Request Parameters - Page 2:
2025-08-16 10:31:43,541 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 10:31:43,541 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 10:31:44,276 - INFO - Response - Page 2:
2025-08-16 10:31:44,276 - INFO - 第 2 页获取到 45 条记录
2025-08-16 10:31:44,776 - INFO - 查询完成，共获取到 95 条记录
2025-08-16 10:31:44,776 - INFO - 获取到 95 条表单数据
2025-08-16 10:31:44,776 - INFO - 当前日期 2025-08-15 有 409 条MySQL数据需要处理
2025-08-16 10:31:44,776 - INFO - 开始批量插入 314 条新记录
2025-08-16 10:31:45,041 - INFO - 批量插入响应状态码: 200
2025-08-16 10:31:45,041 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 02:31:45 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2405', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B823F073-4FB1-7FEC-858F-E5F598E71898', 'x-acs-trace-id': 'f6c71b2e51653e33fd1f20f71c808f74', 'etag': '20QAi/wxmiiM1wqLN23ZrPw5', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 10:31:45,041 - INFO - 批量插入响应体: {'result': ['FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMT', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMU', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMV', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMW', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMX', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMY', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMZ', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM01', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM11', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM21', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM31', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM41', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM51', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM61', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM71', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM81', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM91', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMA1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMB1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMC1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMD1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEME1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMF1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMG1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMH1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMI1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMJ1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMK1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEML1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMM1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMN1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMO1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMP1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMQ1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMR1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMS1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMT1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMU1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMV1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMW1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMX1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMY1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMZ1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM02', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM12', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM22', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM32', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM42', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM52', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM62']}
2025-08-16 10:31:45,041 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-16 10:31:45,041 - INFO - 成功插入的数据ID: ['FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMT', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMU', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMV', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMW', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMX', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMY', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMZ', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM01', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM11', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM21', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM31', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM41', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM51', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM61', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM71', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM81', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM91', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMA1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMB1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMC1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMD1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEME1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMF1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMG1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMH1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMI1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMJ1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMK1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEML1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMM1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMN1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMO1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMP1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMQ1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMR1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMS1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMT1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMU1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMV1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMW1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMX1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMY1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEMZ1', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM02', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM12', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM22', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM32', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM42', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM52', 'FINST-7I8669810W1YAIWAEZR8VAZTENKC30PD5NDEM62']
2025-08-16 10:31:50,307 - INFO - 批量插入响应状态码: 200
2025-08-16 10:31:50,307 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 02:31:50 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '04F5A57E-B217-767F-BFFA-50F787B6D6B3', 'x-acs-trace-id': '74beca31b5934229b2fa9620546895ed', 'etag': '2HyAWdpxMfTfmaQ0mV2IqMg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 10:31:50,307 - INFO - 批量插入响应体: {'result': ['FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMW6', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMX6', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMY6', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMZ6', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM07', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM17', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM27', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM37', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM47', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM57', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM67', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM77', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM87', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM97', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMA7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMB7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMC7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMD7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEME7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMF7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMG7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMH7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMI7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMJ7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMK7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEML7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMM7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMN7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMO7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMP7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMQ7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMR7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMS7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMT7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMU7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMV7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMW7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMX7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMY7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMZ7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM08', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM18', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM28', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM38', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM48', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM58', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM68', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM78', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM88', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM98']}
2025-08-16 10:31:50,307 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-16 10:31:50,307 - INFO - 成功插入的数据ID: ['FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMW6', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMX6', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMY6', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMZ6', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM07', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM17', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM27', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM37', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM47', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM57', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM67', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM77', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM87', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM97', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMA7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMB7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMC7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMD7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEME7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMF7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMG7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMH7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMI7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMJ7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMK7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEML7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMM7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMN7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMO7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMP7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMQ7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMR7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMS7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMT7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMU7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMV7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMW7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMX7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMY7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEMZ7', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM08', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM18', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM28', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM38', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM48', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM58', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM68', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM78', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM88', 'FINST-NDC66NB1TW1Y4VZPBS8BE6SWN3K339RH5NDEM98']
2025-08-16 10:31:55,541 - INFO - 批量插入响应状态码: 200
2025-08-16 10:31:55,541 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 02:31:55 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DC14FFD9-6519-7175-A2F4-5A53BCF24707', 'x-acs-trace-id': '613a969f9b4171e899a82fbda74ea943', 'etag': '27QGmNoDDxe32Al/XE392ZQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 10:31:55,541 - INFO - 批量插入响应体: {'result': ['FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMJ8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMK8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEML8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMM8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMN8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMO8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMP8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMQ8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMR8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMS8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMT8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMU8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMV8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMW8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMX8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMY8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMZ8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM09', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM19', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM29', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM39', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM49', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM59', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM69', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM79', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM89', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM99', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMA9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMB9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMC9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMD9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEME9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMF9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMG9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMH9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMI9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMJ9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMK9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEML9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMM9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMN9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMO9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMP9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMQ9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMR9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMS9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMT9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMU9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMV9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMW9']}
2025-08-16 10:31:55,541 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-16 10:31:55,541 - INFO - 成功插入的数据ID: ['FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMJ8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMK8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEML8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMM8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMN8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMO8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMP8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMQ8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMR8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMS8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMT8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMU8', 'FINST-4UG662917W1YGULR9UGKV65UH85D33TL5NDEMV8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMW8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMX8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMY8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMZ8', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM09', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM19', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM29', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM39', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM49', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM59', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM69', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM79', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM89', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEM99', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMA9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMB9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMC9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMD9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEME9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMF9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMG9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMH9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMI9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMJ9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMK9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEML9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMM9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMN9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMO9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMP9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMQ9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMR9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMS9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMT9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMU9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMV9', 'FINST-4UG662917W1YGULR9UGKV65UH85D34TL5NDEMW9']
2025-08-16 10:32:00,791 - INFO - 批量插入响应状态码: 200
2025-08-16 10:32:00,791 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 02:32:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D5550997-4FE1-7808-851F-109FF4C77886', 'x-acs-trace-id': '24d1274794ddc96d32dbd37a3cd98aac', 'etag': '28Sm1fQ8Rv/roX9q3GEZ9WQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 10:32:00,791 - INFO - 批量插入响应体: {'result': ['FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMM7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMN7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMO7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMP7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMQ7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMR7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMS7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMT7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMU7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMV7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMW7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMX7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMY7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMZ7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM08', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM18', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM28', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM38', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM48', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM58', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM68', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM78', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM88', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM98', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMA8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMB8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMC8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMD8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEME8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMF8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMG8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMH8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMI8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMJ8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMK8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEML8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMM8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMN8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMO8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMP8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMQ8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMR8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMS8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMT8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMU8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMV8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMW8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMX8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMY8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMZ8']}
2025-08-16 10:32:00,791 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-08-16 10:32:00,791 - INFO - 成功插入的数据ID: ['FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMM7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMN7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMO7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMP7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMQ7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMR7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMS7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMT7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMU7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMV7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMW7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMX7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMY7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMZ7', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM08', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM18', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM28', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM38', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM48', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM58', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM68', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM78', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM88', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEM98', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMA8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMB8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMC8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMD8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEME8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMF8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMG8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMH8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMI8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMJ8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMK8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEML8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMM8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMN8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMO8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMP8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMQ8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMR8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMS8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMT8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMU8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMV8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMW8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMX8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMY8', 'FINST-VEC667D1YV1YBMQK804J1DF4O1AM2TUP5NDEMZ8']
2025-08-16 10:32:06,052 - INFO - 批量插入响应状态码: 200
2025-08-16 10:32:06,052 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 02:32:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C936A2D5-59E4-7296-AB40-D52782B99D81', 'x-acs-trace-id': 'ad3137ce73bd40b22c8d0025a63b6353', 'etag': '2v9PEguv93oXx4E5PFDUGpg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 10:32:06,052 - INFO - 批量插入响应体: {'result': ['FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMPF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMQF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMRF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMSF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMTF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMUF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMVF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMWF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMXF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMYF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMZF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM0G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM1G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM2G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM3G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM4G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM5G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM6G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM7G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM8G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM9G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMAG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMBG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMCG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMDG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMEG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMFG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMGG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMHG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMIG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMJG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMKG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMLG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMMG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMNG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMOG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMPG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMQG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMRG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMSG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMTG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMUG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMVG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMWG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMXG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMYG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMZG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM0H', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM1H', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM2H']}
2025-08-16 10:32:06,052 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-08-16 10:32:06,052 - INFO - 成功插入的数据ID: ['FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMPF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMQF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMRF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMSF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMTF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMUF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMVF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMWF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMXF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMYF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMZF', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM0G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM1G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM2G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM3G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM4G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM5G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM6G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM7G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM8G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM9G', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMAG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMBG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMCG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMDG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMEG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMFG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMGG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMHG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMIG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMJG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMKG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMLG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMMG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMNG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMOG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMPG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMQG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMRG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMSG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMTG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMUG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMVG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMWG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMXG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMYG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEMZG', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM0H', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM1H', 'FINST-737662B1902YWDKK8M0L94RKE1E82NWT5NDEM2H']
2025-08-16 10:32:11,302 - INFO - 批量插入响应状态码: 200
2025-08-16 10:32:11,302 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 02:32:11 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'F0CC1D29-B5FD-7BF3-89BA-AD694B32D92F', 'x-acs-trace-id': 'b5be4942b2df14d2c1343edd7e5ca153', 'etag': '2i9OTKcSPmWbi8+MI7jWoHg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 10:32:11,302 - INFO - 批量插入响应体: {'result': ['FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMWB', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMXB', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMYB', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMZB', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM0C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM1C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM2C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM3C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM4C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM5C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM6C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM7C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM8C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM9C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMAC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMBC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMCC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMDC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMEC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMFC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMGC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMHC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMIC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMJC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMKC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMLC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMMC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMNC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMOC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMPC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMQC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMRC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMSC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMTC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMUC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMVC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMWC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMXC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMYC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMZC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM0D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM1D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM2D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM3D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM4D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM5D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM6D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM7D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM8D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM9D']}
2025-08-16 10:32:11,302 - INFO - 批量插入表单数据成功，批次 6，共 50 条记录
2025-08-16 10:32:11,302 - INFO - 成功插入的数据ID: ['FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMWB', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMXB', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMYB', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMZB', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM0C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM1C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM2C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM3C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM4C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM5C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM6C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM7C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM8C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM9C', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMAC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMBC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMCC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMDC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMEC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMFC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMGC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMHC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMIC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMJC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMKC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMLC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMMC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMNC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMOC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMPC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMQC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMRC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMSC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMTC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMUC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMVC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMWC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMXC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMYC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEMZC', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM0D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM1D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM2D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM3D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM4D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM5D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM6D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM7D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM8D', 'FINST-34B66L913W1YZ2I7AKF8O585RU5R3JYX5NDEM9D']
2025-08-16 10:32:16,489 - INFO - 批量插入响应状态码: 200
2025-08-16 10:32:16,489 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 02:32:16 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '684', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'EC2DE182-E60A-7C43-AB91-63C1EE2351E1', 'x-acs-trace-id': '110a616669a8554b9431ddc360b84a6b', 'etag': '6KIlQIOCtj3Yszr7RHtulQQ4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 10:32:16,489 - INFO - 批量插入响应体: {'result': ['FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMND', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMOD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMPD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMQD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMRD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMSD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMTD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMUD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMVD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMWD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMXD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMYD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73KY16NDEMZD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73KY16NDEM0E']}
2025-08-16 10:32:16,489 - INFO - 批量插入表单数据成功，批次 7，共 14 条记录
2025-08-16 10:32:16,489 - INFO - 成功插入的数据ID: ['FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMND', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMOD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMPD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMQD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMRD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMSD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMTD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMUD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMVD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMWD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMXD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73JY16NDEMYD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73KY16NDEMZD', 'FINST-XL866HB1QY1YMRFFCJVPVAR9O0J73KY16NDEM0E']
2025-08-16 10:32:21,505 - INFO - 批量插入完成，共 314 条记录
2025-08-16 10:32:21,505 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 314 条，错误: 0 条
2025-08-16 10:32:21,505 - INFO - 数据同步完成！更新: 0 条，插入: 314 条，错误: 0 条
2025-08-16 10:32:21,505 - INFO - 同步完成
2025-08-16 13:30:33,561 - INFO - 使用默认增量同步（当天更新数据）
2025-08-16 13:30:33,561 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-16 13:30:33,561 - INFO - 查询参数: ('2025-08-16',)
2025-08-16 13:30:33,733 - INFO - MySQL查询成功，增量数据（日期: 2025-08-16），共获取 153 条记录
2025-08-16 13:30:33,733 - INFO - 获取到 3 个日期需要处理: ['2025-08-04', '2025-08-14', '2025-08-15']
2025-08-16 13:30:33,749 - INFO - 开始处理日期: 2025-08-04
2025-08-16 13:30:33,749 - INFO - Request Parameters - Page 1:
2025-08-16 13:30:33,749 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:30:33,749 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:30:41,874 - ERROR - 处理日期 2025-08-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 00351DF5-82F1-7A50-88FF-40F0F17989E1 Response: {'code': 'ServiceUnavailable', 'requestid': '00351DF5-82F1-7A50-88FF-40F0F17989E1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 00351DF5-82F1-7A50-88FF-40F0F17989E1)
2025-08-16 13:30:41,874 - INFO - 开始处理日期: 2025-08-14
2025-08-16 13:30:41,874 - INFO - Request Parameters - Page 1:
2025-08-16 13:30:41,874 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:30:41,874 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:30:49,983 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 800F6800-7302-7262-8C3A-95C34083C579 Response: {'code': 'ServiceUnavailable', 'requestid': '800F6800-7302-7262-8C3A-95C34083C579', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 800F6800-7302-7262-8C3A-95C34083C579)
2025-08-16 13:30:49,983 - INFO - 开始处理日期: 2025-08-15
2025-08-16 13:30:49,983 - INFO - Request Parameters - Page 1:
2025-08-16 13:30:49,983 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:30:49,983 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:30:50,717 - INFO - Response - Page 1:
2025-08-16 13:30:50,717 - INFO - 第 1 页获取到 50 条记录
2025-08-16 13:30:51,233 - INFO - Request Parameters - Page 2:
2025-08-16 13:30:51,233 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:30:51,233 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:30:56,124 - INFO - Response - Page 2:
2025-08-16 13:30:56,124 - INFO - 第 2 页获取到 50 条记录
2025-08-16 13:30:56,624 - INFO - Request Parameters - Page 3:
2025-08-16 13:30:56,624 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:30:56,624 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:30:57,467 - INFO - Response - Page 3:
2025-08-16 13:30:57,467 - INFO - 第 3 页获取到 50 条记录
2025-08-16 13:30:57,967 - INFO - Request Parameters - Page 4:
2025-08-16 13:30:57,967 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:30:57,967 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:30:58,717 - INFO - Response - Page 4:
2025-08-16 13:30:58,717 - INFO - 第 4 页获取到 50 条记录
2025-08-16 13:30:59,217 - INFO - Request Parameters - Page 5:
2025-08-16 13:30:59,217 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:30:59,217 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:31:00,092 - INFO - Response - Page 5:
2025-08-16 13:31:00,092 - INFO - 第 5 页获取到 50 条记录
2025-08-16 13:31:00,608 - INFO - Request Parameters - Page 6:
2025-08-16 13:31:00,608 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:31:00,608 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:31:01,342 - INFO - Response - Page 6:
2025-08-16 13:31:01,342 - INFO - 第 6 页获取到 50 条记录
2025-08-16 13:31:01,858 - INFO - Request Parameters - Page 7:
2025-08-16 13:31:01,858 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:31:01,858 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:31:02,624 - INFO - Response - Page 7:
2025-08-16 13:31:02,624 - INFO - 第 7 页获取到 50 条记录
2025-08-16 13:31:03,124 - INFO - Request Parameters - Page 8:
2025-08-16 13:31:03,124 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:31:03,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:31:03,920 - INFO - Response - Page 8:
2025-08-16 13:31:03,920 - INFO - 第 8 页获取到 50 条记录
2025-08-16 13:31:04,420 - INFO - Request Parameters - Page 9:
2025-08-16 13:31:04,420 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:31:04,420 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:31:05,014 - INFO - Response - Page 9:
2025-08-16 13:31:05,014 - INFO - 第 9 页获取到 9 条记录
2025-08-16 13:31:05,514 - INFO - 查询完成，共获取到 409 条记录
2025-08-16 13:31:05,514 - INFO - 获取到 409 条表单数据
2025-08-16 13:31:05,514 - INFO - 当前日期 2025-08-15 有 142 条MySQL数据需要处理
2025-08-16 13:31:05,514 - INFO - 开始批量插入 34 条新记录
2025-08-16 13:31:05,764 - INFO - 批量插入响应状态码: 200
2025-08-16 13:31:05,764 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 05:31:06 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1644', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D52246E5-4B07-731A-BF4D-7C17BDDF7C0C', 'x-acs-trace-id': 'b0f98b0d7efe28576aa5b5dc475f5874', 'etag': '1JkOktmY/+aU2AEbvWXuJyw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 13:31:05,764 - INFO - 批量插入响应体: {'result': ['FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMH4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMI4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMJ4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMK4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEML4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMM4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMN4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMO4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMP4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMQ4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMR4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMS4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMT4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMU4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMV4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMW4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMX4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMY4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMZ4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM05', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM15', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM25', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM35', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM45', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM55', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM65', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM75', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEM85', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEM95', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEMA5', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEMB5', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEMC5', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEMD5', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEME5']}
2025-08-16 13:31:05,764 - INFO - 批量插入表单数据成功，批次 1，共 34 条记录
2025-08-16 13:31:05,780 - INFO - 成功插入的数据ID: ['FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMH4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMI4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMJ4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMK4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEML4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMM4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMN4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMO4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMP4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMQ4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMR4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMS4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMT4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMU4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMV4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMW4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMX4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMY4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEMZ4', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM05', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM15', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM25', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM35', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM45', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM55', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM65', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2DS0KTDEM75', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEM85', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEM95', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEMA5', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEMB5', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEMC5', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEMD5', 'FINST-EZD66RB18Y1Y3693B7MQ3AEB883A2ES0KTDEME5']
2025-08-16 13:31:10,791 - INFO - 批量插入完成，共 34 条记录
2025-08-16 13:31:10,791 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 34 条，错误: 0 条
2025-08-16 13:31:10,791 - INFO - 数据同步完成！更新: 0 条，插入: 34 条，错误: 2 条
2025-08-16 13:32:10,801 - INFO - 开始同步昨天与今天的销售数据: 2025-08-15 至 2025-08-16
2025-08-16 13:32:10,801 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-16 13:32:10,801 - INFO - 查询参数: ('2025-08-15', '2025-08-16')
2025-08-16 13:32:10,973 - INFO - MySQL查询成功，时间段: 2025-08-15 至 2025-08-16，共获取 493 条记录
2025-08-16 13:32:10,973 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 13:32:10,988 - INFO - 开始处理日期: 2025-08-15
2025-08-16 13:32:10,988 - INFO - Request Parameters - Page 1:
2025-08-16 13:32:10,988 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:10,988 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:11,723 - INFO - Response - Page 1:
2025-08-16 13:32:11,723 - INFO - 第 1 页获取到 50 条记录
2025-08-16 13:32:12,238 - INFO - Request Parameters - Page 2:
2025-08-16 13:32:12,238 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:12,238 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:12,988 - INFO - Response - Page 2:
2025-08-16 13:32:12,988 - INFO - 第 2 页获取到 50 条记录
2025-08-16 13:32:13,488 - INFO - Request Parameters - Page 3:
2025-08-16 13:32:13,488 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:13,488 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:14,285 - INFO - Response - Page 3:
2025-08-16 13:32:14,285 - INFO - 第 3 页获取到 50 条记录
2025-08-16 13:32:14,801 - INFO - Request Parameters - Page 4:
2025-08-16 13:32:14,801 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:14,801 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:15,645 - INFO - Response - Page 4:
2025-08-16 13:32:15,645 - INFO - 第 4 页获取到 50 条记录
2025-08-16 13:32:16,160 - INFO - Request Parameters - Page 5:
2025-08-16 13:32:16,160 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:16,160 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:16,926 - INFO - Response - Page 5:
2025-08-16 13:32:16,926 - INFO - 第 5 页获取到 50 条记录
2025-08-16 13:32:17,426 - INFO - Request Parameters - Page 6:
2025-08-16 13:32:17,426 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:17,426 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:18,145 - INFO - Response - Page 6:
2025-08-16 13:32:18,145 - INFO - 第 6 页获取到 50 条记录
2025-08-16 13:32:18,660 - INFO - Request Parameters - Page 7:
2025-08-16 13:32:18,660 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:18,660 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:19,410 - INFO - Response - Page 7:
2025-08-16 13:32:19,410 - INFO - 第 7 页获取到 50 条记录
2025-08-16 13:32:19,926 - INFO - Request Parameters - Page 8:
2025-08-16 13:32:19,926 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:19,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:20,738 - INFO - Response - Page 8:
2025-08-16 13:32:20,738 - INFO - 第 8 页获取到 50 条记录
2025-08-16 13:32:21,254 - INFO - Request Parameters - Page 9:
2025-08-16 13:32:21,254 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 13:32:21,254 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 13:32:22,035 - INFO - Response - Page 9:
2025-08-16 13:32:22,035 - INFO - 第 9 页获取到 43 条记录
2025-08-16 13:32:22,551 - INFO - 查询完成，共获取到 443 条记录
2025-08-16 13:32:22,551 - INFO - 获取到 443 条表单数据
2025-08-16 13:32:22,551 - INFO - 当前日期 2025-08-15 有 478 条MySQL数据需要处理
2025-08-16 13:32:22,566 - INFO - 开始批量插入 35 条新记录
2025-08-16 13:32:22,801 - INFO - 批量插入响应状态码: 200
2025-08-16 13:32:22,801 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 05:32:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1692', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BF466FE0-E0D1-7DC0-B854-66644C43F7C9', 'x-acs-trace-id': 'a2ff03fe4ce55744b3b43fc8e4602d0e', 'etag': '1H0ZZg0hbhXgCdz1GR9/fcA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 13:32:22,801 - INFO - 批量插入响应体: {'result': ['FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMMM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMNM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMOM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMPM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMQM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMRM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMSM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMTM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMUM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMVM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMWM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMXM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMYM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMZM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM0N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM1N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM2N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM3N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM4N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM5N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM6N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM7N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM8N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM9N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMAN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMBN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMCN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMDN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMEN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMFN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMGN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMHN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMIN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMJN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMKN']}
2025-08-16 13:32:22,801 - INFO - 批量插入表单数据成功，批次 1，共 35 条记录
2025-08-16 13:32:22,801 - INFO - 成功插入的数据ID: ['FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMMM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMNM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMOM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMPM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMQM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMRM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMSM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMTM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMUM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMVM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMWM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMXM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMYM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMZM', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM0N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM1N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM2N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM3N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM4N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM5N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM6N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM7N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM8N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEM9N', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMAN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMBN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMCN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMDN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMEN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMFN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMGN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMHN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMIN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMJN', 'FINST-SUE66J81SW1YVT7U8NIATAFMVOFV318OLTDEMKN']
2025-08-16 13:32:27,816 - INFO - 批量插入完成，共 35 条记录
2025-08-16 13:32:27,816 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 35 条，错误: 0 条
2025-08-16 13:32:27,816 - INFO - 数据同步完成！更新: 0 条，插入: 35 条，错误: 0 条
2025-08-16 13:32:27,816 - INFO - 同步完成
2025-08-16 16:30:33,763 - INFO - 使用默认增量同步（当天更新数据）
2025-08-16 16:30:33,763 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-16 16:30:33,763 - INFO - 查询参数: ('2025-08-16',)
2025-08-16 16:30:33,935 - INFO - MySQL查询成功，增量数据（日期: 2025-08-16），共获取 153 条记录
2025-08-16 16:30:33,935 - INFO - 获取到 3 个日期需要处理: ['2025-08-04', '2025-08-14', '2025-08-15']
2025-08-16 16:30:33,935 - INFO - 开始处理日期: 2025-08-04
2025-08-16 16:30:33,935 - INFO - Request Parameters - Page 1:
2025-08-16 16:30:33,935 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:30:33,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:30:42,075 - ERROR - 处理日期 2025-08-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7BB0567D-C33D-7728-9EFF-356F7AE0767F Response: {'code': 'ServiceUnavailable', 'requestid': '7BB0567D-C33D-7728-9EFF-356F7AE0767F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7BB0567D-C33D-7728-9EFF-356F7AE0767F)
2025-08-16 16:30:42,075 - INFO - 开始处理日期: 2025-08-14
2025-08-16 16:30:42,075 - INFO - Request Parameters - Page 1:
2025-08-16 16:30:42,075 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:30:42,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:30:42,903 - INFO - Response - Page 1:
2025-08-16 16:30:42,903 - INFO - 第 1 页获取到 50 条记录
2025-08-16 16:30:43,419 - INFO - Request Parameters - Page 2:
2025-08-16 16:30:43,419 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:30:43,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:30:51,528 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 66AFB896-399A-7970-9C49-2CF0AA454289 Response: {'code': 'ServiceUnavailable', 'requestid': '66AFB896-399A-7970-9C49-2CF0AA454289', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 66AFB896-399A-7970-9C49-2CF0AA454289)
2025-08-16 16:30:51,528 - INFO - 开始处理日期: 2025-08-15
2025-08-16 16:30:51,528 - INFO - Request Parameters - Page 1:
2025-08-16 16:30:51,528 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:30:51,528 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:30:56,966 - INFO - Response - Page 1:
2025-08-16 16:30:56,966 - INFO - 第 1 页获取到 50 条记录
2025-08-16 16:30:57,466 - INFO - Request Parameters - Page 2:
2025-08-16 16:30:57,466 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:30:57,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:30:58,185 - INFO - Response - Page 2:
2025-08-16 16:30:58,185 - INFO - 第 2 页获取到 50 条记录
2025-08-16 16:30:58,700 - INFO - Request Parameters - Page 3:
2025-08-16 16:30:58,700 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:30:58,700 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:30:59,466 - INFO - Response - Page 3:
2025-08-16 16:30:59,466 - INFO - 第 3 页获取到 50 条记录
2025-08-16 16:30:59,982 - INFO - Request Parameters - Page 4:
2025-08-16 16:30:59,982 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:30:59,982 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:31:00,763 - INFO - Response - Page 4:
2025-08-16 16:31:00,763 - INFO - 第 4 页获取到 50 条记录
2025-08-16 16:31:01,278 - INFO - Request Parameters - Page 5:
2025-08-16 16:31:01,278 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:31:01,278 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:31:02,013 - INFO - Response - Page 5:
2025-08-16 16:31:02,013 - INFO - 第 5 页获取到 50 条记录
2025-08-16 16:31:02,513 - INFO - Request Parameters - Page 6:
2025-08-16 16:31:02,513 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:31:02,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:31:03,325 - INFO - Response - Page 6:
2025-08-16 16:31:03,325 - INFO - 第 6 页获取到 50 条记录
2025-08-16 16:31:03,825 - INFO - Request Parameters - Page 7:
2025-08-16 16:31:03,825 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:31:03,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:31:04,591 - INFO - Response - Page 7:
2025-08-16 16:31:04,591 - INFO - 第 7 页获取到 50 条记录
2025-08-16 16:31:05,106 - INFO - Request Parameters - Page 8:
2025-08-16 16:31:05,106 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:31:05,106 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:31:05,919 - INFO - Response - Page 8:
2025-08-16 16:31:05,919 - INFO - 第 8 页获取到 50 条记录
2025-08-16 16:31:06,435 - INFO - Request Parameters - Page 9:
2025-08-16 16:31:06,435 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:31:06,435 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:31:07,169 - INFO - Response - Page 9:
2025-08-16 16:31:07,169 - INFO - 第 9 页获取到 50 条记录
2025-08-16 16:31:07,669 - INFO - Request Parameters - Page 10:
2025-08-16 16:31:07,669 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:31:07,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:31:08,341 - INFO - Response - Page 10:
2025-08-16 16:31:08,341 - INFO - 第 10 页获取到 28 条记录
2025-08-16 16:31:08,841 - INFO - 查询完成，共获取到 478 条记录
2025-08-16 16:31:08,841 - INFO - 获取到 478 条表单数据
2025-08-16 16:31:08,841 - INFO - 当前日期 2025-08-15 有 142 条MySQL数据需要处理
2025-08-16 16:31:08,841 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 16:31:08,841 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-16 16:32:08,851 - INFO - 开始同步昨天与今天的销售数据: 2025-08-15 至 2025-08-16
2025-08-16 16:32:08,851 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-16 16:32:08,851 - INFO - 查询参数: ('2025-08-15', '2025-08-16')
2025-08-16 16:32:09,023 - INFO - MySQL查询成功，时间段: 2025-08-15 至 2025-08-16，共获取 493 条记录
2025-08-16 16:32:09,023 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 16:32:09,039 - INFO - 开始处理日期: 2025-08-15
2025-08-16 16:32:09,039 - INFO - Request Parameters - Page 1:
2025-08-16 16:32:09,039 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:09,039 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:09,804 - INFO - Response - Page 1:
2025-08-16 16:32:09,804 - INFO - 第 1 页获取到 50 条记录
2025-08-16 16:32:10,320 - INFO - Request Parameters - Page 2:
2025-08-16 16:32:10,320 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:10,320 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:11,070 - INFO - Response - Page 2:
2025-08-16 16:32:11,070 - INFO - 第 2 页获取到 50 条记录
2025-08-16 16:32:11,585 - INFO - Request Parameters - Page 3:
2025-08-16 16:32:11,585 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:11,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:12,382 - INFO - Response - Page 3:
2025-08-16 16:32:12,382 - INFO - 第 3 页获取到 50 条记录
2025-08-16 16:32:12,898 - INFO - Request Parameters - Page 4:
2025-08-16 16:32:12,898 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:12,898 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:13,632 - INFO - Response - Page 4:
2025-08-16 16:32:13,632 - INFO - 第 4 页获取到 50 条记录
2025-08-16 16:32:14,143 - INFO - Request Parameters - Page 5:
2025-08-16 16:32:14,143 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:14,143 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:14,909 - INFO - Response - Page 5:
2025-08-16 16:32:14,909 - INFO - 第 5 页获取到 50 条记录
2025-08-16 16:32:15,424 - INFO - Request Parameters - Page 6:
2025-08-16 16:32:15,424 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:15,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:16,159 - INFO - Response - Page 6:
2025-08-16 16:32:16,159 - INFO - 第 6 页获取到 50 条记录
2025-08-16 16:32:16,674 - INFO - Request Parameters - Page 7:
2025-08-16 16:32:16,674 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:16,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:17,440 - INFO - Response - Page 7:
2025-08-16 16:32:17,440 - INFO - 第 7 页获取到 50 条记录
2025-08-16 16:32:17,956 - INFO - Request Parameters - Page 8:
2025-08-16 16:32:17,956 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:17,956 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:18,674 - INFO - Response - Page 8:
2025-08-16 16:32:18,674 - INFO - 第 8 页获取到 50 条记录
2025-08-16 16:32:19,174 - INFO - Request Parameters - Page 9:
2025-08-16 16:32:19,174 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:19,174 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:19,956 - INFO - Response - Page 9:
2025-08-16 16:32:19,956 - INFO - 第 9 页获取到 50 条记录
2025-08-16 16:32:20,471 - INFO - Request Parameters - Page 10:
2025-08-16 16:32:20,471 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 16:32:20,471 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 16:32:21,159 - INFO - Response - Page 10:
2025-08-16 16:32:21,159 - INFO - 第 10 页获取到 28 条记录
2025-08-16 16:32:21,674 - INFO - 查询完成，共获取到 478 条记录
2025-08-16 16:32:21,674 - INFO - 获取到 478 条表单数据
2025-08-16 16:32:21,674 - INFO - 当前日期 2025-08-15 有 478 条MySQL数据需要处理
2025-08-16 16:32:21,690 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 16:32:21,690 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 16:32:21,690 - INFO - 同步完成
2025-08-16 19:30:34,058 - INFO - 使用默认增量同步（当天更新数据）
2025-08-16 19:30:34,058 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-16 19:30:34,058 - INFO - 查询参数: ('2025-08-16',)
2025-08-16 19:30:34,246 - INFO - MySQL查询成功，增量数据（日期: 2025-08-16），共获取 153 条记录
2025-08-16 19:30:34,246 - INFO - 获取到 3 个日期需要处理: ['2025-08-04', '2025-08-14', '2025-08-15']
2025-08-16 19:30:34,246 - INFO - 开始处理日期: 2025-08-04
2025-08-16 19:30:34,246 - INFO - Request Parameters - Page 1:
2025-08-16 19:30:34,246 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:30:34,246 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:30:42,356 - ERROR - 处理日期 2025-08-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 62F8E7B4-E2B6-7765-A72D-F71681400DD2 Response: {'code': 'ServiceUnavailable', 'requestid': '62F8E7B4-E2B6-7765-A72D-F71681400DD2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 62F8E7B4-E2B6-7765-A72D-F71681400DD2)
2025-08-16 19:30:42,356 - INFO - 开始处理日期: 2025-08-14
2025-08-16 19:30:42,356 - INFO - Request Parameters - Page 1:
2025-08-16 19:30:42,356 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:30:42,356 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:30:50,482 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 384F3688-FC0B-7E79-8D0B-8FF84E9C784F Response: {'code': 'ServiceUnavailable', 'requestid': '384F3688-FC0B-7E79-8D0B-8FF84E9C784F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 384F3688-FC0B-7E79-8D0B-8FF84E9C784F)
2025-08-16 19:30:50,482 - INFO - 开始处理日期: 2025-08-15
2025-08-16 19:30:50,482 - INFO - Request Parameters - Page 1:
2025-08-16 19:30:50,482 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:30:50,482 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:30:55,842 - INFO - Response - Page 1:
2025-08-16 19:30:55,842 - INFO - 第 1 页获取到 50 条记录
2025-08-16 19:30:56,342 - INFO - Request Parameters - Page 2:
2025-08-16 19:30:56,342 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:30:56,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:30:57,077 - INFO - Response - Page 2:
2025-08-16 19:30:57,077 - INFO - 第 2 页获取到 50 条记录
2025-08-16 19:30:57,577 - INFO - Request Parameters - Page 3:
2025-08-16 19:30:57,577 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:30:57,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:30:58,374 - INFO - Response - Page 3:
2025-08-16 19:30:58,374 - INFO - 第 3 页获取到 50 条记录
2025-08-16 19:30:58,889 - INFO - Request Parameters - Page 4:
2025-08-16 19:30:58,889 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:30:58,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:30:59,655 - INFO - Response - Page 4:
2025-08-16 19:30:59,655 - INFO - 第 4 页获取到 50 条记录
2025-08-16 19:31:00,171 - INFO - Request Parameters - Page 5:
2025-08-16 19:31:00,171 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:31:00,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:31:01,171 - INFO - Response - Page 5:
2025-08-16 19:31:01,171 - INFO - 第 5 页获取到 50 条记录
2025-08-16 19:31:01,671 - INFO - Request Parameters - Page 6:
2025-08-16 19:31:01,671 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:31:01,671 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:31:02,390 - INFO - Response - Page 6:
2025-08-16 19:31:02,390 - INFO - 第 6 页获取到 50 条记录
2025-08-16 19:31:02,890 - INFO - Request Parameters - Page 7:
2025-08-16 19:31:02,890 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:31:02,890 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:31:03,609 - INFO - Response - Page 7:
2025-08-16 19:31:03,609 - INFO - 第 7 页获取到 50 条记录
2025-08-16 19:31:04,124 - INFO - Request Parameters - Page 8:
2025-08-16 19:31:04,124 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:31:04,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:31:04,874 - INFO - Response - Page 8:
2025-08-16 19:31:04,874 - INFO - 第 8 页获取到 50 条记录
2025-08-16 19:31:05,390 - INFO - Request Parameters - Page 9:
2025-08-16 19:31:05,390 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:31:05,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:31:06,093 - INFO - Response - Page 9:
2025-08-16 19:31:06,093 - INFO - 第 9 页获取到 50 条记录
2025-08-16 19:31:06,593 - INFO - Request Parameters - Page 10:
2025-08-16 19:31:06,593 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:31:06,593 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:31:07,250 - INFO - Response - Page 10:
2025-08-16 19:31:07,250 - INFO - 第 10 页获取到 28 条记录
2025-08-16 19:31:07,765 - INFO - 查询完成，共获取到 478 条记录
2025-08-16 19:31:07,765 - INFO - 获取到 478 条表单数据
2025-08-16 19:31:07,765 - INFO - 当前日期 2025-08-15 有 142 条MySQL数据需要处理
2025-08-16 19:31:07,765 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 19:31:07,765 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-16 19:32:07,783 - INFO - 开始同步昨天与今天的销售数据: 2025-08-15 至 2025-08-16
2025-08-16 19:32:07,783 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-16 19:32:07,783 - INFO - 查询参数: ('2025-08-15', '2025-08-16')
2025-08-16 19:32:07,955 - INFO - MySQL查询成功，时间段: 2025-08-15 至 2025-08-16，共获取 493 条记录
2025-08-16 19:32:07,955 - INFO - 获取到 1 个日期需要处理: ['2025-08-15']
2025-08-16 19:32:07,971 - INFO - 开始处理日期: 2025-08-15
2025-08-16 19:32:07,971 - INFO - Request Parameters - Page 1:
2025-08-16 19:32:07,971 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:07,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:08,752 - INFO - Response - Page 1:
2025-08-16 19:32:08,752 - INFO - 第 1 页获取到 50 条记录
2025-08-16 19:32:09,252 - INFO - Request Parameters - Page 2:
2025-08-16 19:32:09,252 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:09,252 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:09,955 - INFO - Response - Page 2:
2025-08-16 19:32:09,955 - INFO - 第 2 页获取到 50 条记录
2025-08-16 19:32:10,455 - INFO - Request Parameters - Page 3:
2025-08-16 19:32:10,455 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:10,455 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:11,174 - INFO - Response - Page 3:
2025-08-16 19:32:11,174 - INFO - 第 3 页获取到 50 条记录
2025-08-16 19:32:11,674 - INFO - Request Parameters - Page 4:
2025-08-16 19:32:11,674 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:11,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:12,393 - INFO - Response - Page 4:
2025-08-16 19:32:12,393 - INFO - 第 4 页获取到 50 条记录
2025-08-16 19:32:12,909 - INFO - Request Parameters - Page 5:
2025-08-16 19:32:12,909 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:12,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:13,612 - INFO - Response - Page 5:
2025-08-16 19:32:13,612 - INFO - 第 5 页获取到 50 条记录
2025-08-16 19:32:14,128 - INFO - Request Parameters - Page 6:
2025-08-16 19:32:14,128 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:14,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:14,878 - INFO - Response - Page 6:
2025-08-16 19:32:14,878 - INFO - 第 6 页获取到 50 条记录
2025-08-16 19:32:15,378 - INFO - Request Parameters - Page 7:
2025-08-16 19:32:15,378 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:15,378 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:16,190 - INFO - Response - Page 7:
2025-08-16 19:32:16,190 - INFO - 第 7 页获取到 50 条记录
2025-08-16 19:32:16,706 - INFO - Request Parameters - Page 8:
2025-08-16 19:32:16,706 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:16,706 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:17,472 - INFO - Response - Page 8:
2025-08-16 19:32:17,472 - INFO - 第 8 页获取到 50 条记录
2025-08-16 19:32:17,972 - INFO - Request Parameters - Page 9:
2025-08-16 19:32:17,972 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:17,972 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:18,753 - INFO - Response - Page 9:
2025-08-16 19:32:18,753 - INFO - 第 9 页获取到 50 条记录
2025-08-16 19:32:19,264 - INFO - Request Parameters - Page 10:
2025-08-16 19:32:19,264 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 19:32:19,264 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 19:32:19,936 - INFO - Response - Page 10:
2025-08-16 19:32:19,936 - INFO - 第 10 页获取到 28 条记录
2025-08-16 19:32:20,452 - INFO - 查询完成，共获取到 478 条记录
2025-08-16 19:32:20,452 - INFO - 获取到 478 条表单数据
2025-08-16 19:32:20,452 - INFO - 当前日期 2025-08-15 有 478 条MySQL数据需要处理
2025-08-16 19:32:20,467 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 19:32:20,467 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 19:32:20,467 - INFO - 同步完成
2025-08-16 22:30:33,892 - INFO - 使用默认增量同步（当天更新数据）
2025-08-16 22:30:33,892 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-16 22:30:33,892 - INFO - 查询参数: ('2025-08-16',)
2025-08-16 22:30:34,064 - INFO - MySQL查询成功，增量数据（日期: 2025-08-16），共获取 203 条记录
2025-08-16 22:30:34,064 - INFO - 获取到 4 个日期需要处理: ['2025-08-04', '2025-08-14', '2025-08-15', '2025-08-16']
2025-08-16 22:30:34,064 - INFO - 开始处理日期: 2025-08-04
2025-08-16 22:30:34,064 - INFO - Request Parameters - Page 1:
2025-08-16 22:30:34,064 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:30:34,064 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754236800000, 1754323199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:30:42,205 - ERROR - 处理日期 2025-08-04 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 579C5EBF-2B90-73B7-A5D9-0DF72F296D50 Response: {'code': 'ServiceUnavailable', 'requestid': '579C5EBF-2B90-73B7-A5D9-0DF72F296D50', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 579C5EBF-2B90-73B7-A5D9-0DF72F296D50)
2025-08-16 22:30:42,205 - INFO - 开始处理日期: 2025-08-14
2025-08-16 22:30:42,205 - INFO - Request Parameters - Page 1:
2025-08-16 22:30:42,205 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:30:42,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755100800000, 1755187199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:30:50,300 - ERROR - 处理日期 2025-08-14 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: D736AFC3-B694-7E5E-831F-93565CE421D2 Response: {'code': 'ServiceUnavailable', 'requestid': 'D736AFC3-B694-7E5E-831F-93565CE421D2', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: D736AFC3-B694-7E5E-831F-93565CE421D2)
2025-08-16 22:30:50,300 - INFO - 开始处理日期: 2025-08-15
2025-08-16 22:30:50,300 - INFO - Request Parameters - Page 1:
2025-08-16 22:30:50,300 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:30:50,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:30:51,128 - INFO - Response - Page 1:
2025-08-16 22:30:51,128 - INFO - 第 1 页获取到 50 条记录
2025-08-16 22:30:51,644 - INFO - Request Parameters - Page 2:
2025-08-16 22:30:51,644 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:30:51,644 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:30:55,457 - INFO - Response - Page 2:
2025-08-16 22:30:55,457 - INFO - 第 2 页获取到 50 条记录
2025-08-16 22:30:55,957 - INFO - Request Parameters - Page 3:
2025-08-16 22:30:55,957 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:30:55,957 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:30:56,629 - INFO - Response - Page 3:
2025-08-16 22:30:56,629 - INFO - 第 3 页获取到 50 条记录
2025-08-16 22:30:57,129 - INFO - Request Parameters - Page 4:
2025-08-16 22:30:57,129 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:30:57,129 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:30:57,848 - INFO - Response - Page 4:
2025-08-16 22:30:57,848 - INFO - 第 4 页获取到 50 条记录
2025-08-16 22:30:58,348 - INFO - Request Parameters - Page 5:
2025-08-16 22:30:58,348 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:30:58,348 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:30:59,176 - INFO - Response - Page 5:
2025-08-16 22:30:59,176 - INFO - 第 5 页获取到 50 条记录
2025-08-16 22:30:59,676 - INFO - Request Parameters - Page 6:
2025-08-16 22:30:59,676 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:30:59,676 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:31:00,426 - INFO - Response - Page 6:
2025-08-16 22:31:00,426 - INFO - 第 6 页获取到 50 条记录
2025-08-16 22:31:00,926 - INFO - Request Parameters - Page 7:
2025-08-16 22:31:00,926 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:31:00,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:31:01,676 - INFO - Response - Page 7:
2025-08-16 22:31:01,676 - INFO - 第 7 页获取到 50 条记录
2025-08-16 22:31:02,176 - INFO - Request Parameters - Page 8:
2025-08-16 22:31:02,176 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:31:02,176 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:31:02,911 - INFO - Response - Page 8:
2025-08-16 22:31:02,911 - INFO - 第 8 页获取到 50 条记录
2025-08-16 22:31:03,427 - INFO - Request Parameters - Page 9:
2025-08-16 22:31:03,427 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:31:03,427 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:31:04,208 - INFO - Response - Page 9:
2025-08-16 22:31:04,208 - INFO - 第 9 页获取到 50 条记录
2025-08-16 22:31:04,724 - INFO - Request Parameters - Page 10:
2025-08-16 22:31:04,724 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:31:04,724 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:31:05,411 - INFO - Response - Page 10:
2025-08-16 22:31:05,411 - INFO - 第 10 页获取到 28 条记录
2025-08-16 22:31:05,927 - INFO - 查询完成，共获取到 478 条记录
2025-08-16 22:31:05,927 - INFO - 获取到 478 条表单数据
2025-08-16 22:31:05,927 - INFO - 当前日期 2025-08-15 有 142 条MySQL数据需要处理
2025-08-16 22:31:05,927 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 22:31:05,927 - INFO - 开始处理日期: 2025-08-16
2025-08-16 22:31:05,927 - INFO - Request Parameters - Page 1:
2025-08-16 22:31:05,927 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:31:05,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:31:06,427 - INFO - Response - Page 1:
2025-08-16 22:31:06,427 - INFO - 查询完成，共获取到 0 条记录
2025-08-16 22:31:06,427 - INFO - 获取到 0 条表单数据
2025-08-16 22:31:06,427 - INFO - 当前日期 2025-08-16 有 50 条MySQL数据需要处理
2025-08-16 22:31:06,427 - INFO - 开始批量插入 50 条新记录
2025-08-16 22:31:06,661 - INFO - 批量插入响应状态码: 200
2025-08-16 22:31:06,661 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 16 Aug 2025 14:31:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '68ED8945-EAC5-7570-9F7D-C40D4037E85B', 'x-acs-trace-id': '88345b9670af00a5c365c7059a258716', 'etag': '2DfCEVbGjZSOLuhzKYQgL/A2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-16 22:31:06,661 - INFO - 批量插入响应体: {'result': ['FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMNG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMOG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMPG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMQG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMRG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMSG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMTG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMUG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMVG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMWG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMXG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMYG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMZG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM0H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM1H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM2H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM3H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM4H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM5H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM6H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM7H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM8H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM9H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMAH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMBH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMCH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMDH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMEH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMFH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMGH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMHH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMIH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMJH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMKH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMLH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMMH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMNH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMOH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMPH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMQH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMRH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMSH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMTH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMUH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMVH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMWH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMXH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMYH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMZH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM0I']}
2025-08-16 22:31:06,661 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-16 22:31:06,661 - INFO - 成功插入的数据ID: ['FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMNG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMOG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMPG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMQG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMRG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMSG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3N7GUCEEMTG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMUG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMVG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMWG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMXG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMYG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMZG', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM0H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM1H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM2H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM3H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM4H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM5H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM6H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM7H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM8H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM9H', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMAH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMBH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMCH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMDH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMEH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMFH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMGH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMHH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMIH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMJH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMKH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMLH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMMH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMNH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMOH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMPH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMQH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMRH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMSH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMTH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMUH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMVH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMWH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMXH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMYH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEMZH', 'FINST-8P666U910W1Y56PC6345L6K0PVFO3O7GUCEEM0I']
2025-08-16 22:31:11,678 - INFO - 批量插入完成，共 50 条记录
2025-08-16 22:31:11,678 - INFO - 日期 2025-08-16 处理完成 - 更新: 0 条，插入: 50 条，错误: 0 条
2025-08-16 22:31:11,678 - INFO - 数据同步完成！更新: 0 条，插入: 50 条，错误: 2 条
2025-08-16 22:32:11,696 - INFO - 开始同步昨天与今天的销售数据: 2025-08-15 至 2025-08-16
2025-08-16 22:32:11,696 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
                        AND b.code<>'100098671'
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-16 22:32:11,696 - INFO - 查询参数: ('2025-08-15', '2025-08-16')
2025-08-16 22:32:11,867 - INFO - MySQL查询成功，时间段: 2025-08-15 至 2025-08-16，共获取 543 条记录
2025-08-16 22:32:11,867 - INFO - 获取到 2 个日期需要处理: ['2025-08-15', '2025-08-16']
2025-08-16 22:32:11,883 - INFO - 开始处理日期: 2025-08-15
2025-08-16 22:32:11,883 - INFO - Request Parameters - Page 1:
2025-08-16 22:32:11,883 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:11,883 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:12,696 - INFO - Response - Page 1:
2025-08-16 22:32:12,696 - INFO - 第 1 页获取到 50 条记录
2025-08-16 22:32:13,211 - INFO - Request Parameters - Page 2:
2025-08-16 22:32:13,211 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:13,211 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:13,961 - INFO - Response - Page 2:
2025-08-16 22:32:13,961 - INFO - 第 2 页获取到 50 条记录
2025-08-16 22:32:14,477 - INFO - Request Parameters - Page 3:
2025-08-16 22:32:14,477 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:14,477 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:15,196 - INFO - Response - Page 3:
2025-08-16 22:32:15,196 - INFO - 第 3 页获取到 50 条记录
2025-08-16 22:32:15,696 - INFO - Request Parameters - Page 4:
2025-08-16 22:32:15,696 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:15,696 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:16,399 - INFO - Response - Page 4:
2025-08-16 22:32:16,399 - INFO - 第 4 页获取到 50 条记录
2025-08-16 22:32:16,899 - INFO - Request Parameters - Page 5:
2025-08-16 22:32:16,899 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:16,899 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:17,649 - INFO - Response - Page 5:
2025-08-16 22:32:17,649 - INFO - 第 5 页获取到 50 条记录
2025-08-16 22:32:18,165 - INFO - Request Parameters - Page 6:
2025-08-16 22:32:18,165 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:18,165 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:18,962 - INFO - Response - Page 6:
2025-08-16 22:32:18,962 - INFO - 第 6 页获取到 50 条记录
2025-08-16 22:32:19,462 - INFO - Request Parameters - Page 7:
2025-08-16 22:32:19,462 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:19,462 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:20,150 - INFO - Response - Page 7:
2025-08-16 22:32:20,150 - INFO - 第 7 页获取到 50 条记录
2025-08-16 22:32:20,665 - INFO - Request Parameters - Page 8:
2025-08-16 22:32:20,665 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:20,665 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:21,447 - INFO - Response - Page 8:
2025-08-16 22:32:21,447 - INFO - 第 8 页获取到 50 条记录
2025-08-16 22:32:21,947 - INFO - Request Parameters - Page 9:
2025-08-16 22:32:21,947 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:21,947 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:22,666 - INFO - Response - Page 9:
2025-08-16 22:32:22,666 - INFO - 第 9 页获取到 50 条记录
2025-08-16 22:32:23,181 - INFO - Request Parameters - Page 10:
2025-08-16 22:32:23,181 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:23,181 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755187200000, 1755273599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:23,853 - INFO - Response - Page 10:
2025-08-16 22:32:23,853 - INFO - 第 10 页获取到 28 条记录
2025-08-16 22:32:24,353 - INFO - 查询完成，共获取到 478 条记录
2025-08-16 22:32:24,353 - INFO - 获取到 478 条表单数据
2025-08-16 22:32:24,353 - INFO - 当前日期 2025-08-15 有 478 条MySQL数据需要处理
2025-08-16 22:32:24,369 - INFO - 日期 2025-08-15 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 22:32:24,369 - INFO - 开始处理日期: 2025-08-16
2025-08-16 22:32:24,369 - INFO - Request Parameters - Page 1:
2025-08-16 22:32:24,369 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:24,369 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:25,135 - INFO - Response - Page 1:
2025-08-16 22:32:25,135 - INFO - 第 1 页获取到 50 条记录
2025-08-16 22:32:25,645 - INFO - Request Parameters - Page 2:
2025-08-16 22:32:25,645 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-16 22:32:25,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1755273600000, 1755359999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-16 22:32:26,130 - INFO - Response - Page 2:
2025-08-16 22:32:26,130 - INFO - 查询完成，共获取到 50 条记录
2025-08-16 22:32:26,130 - INFO - 获取到 50 条表单数据
2025-08-16 22:32:26,130 - INFO - 当前日期 2025-08-16 有 50 条MySQL数据需要处理
2025-08-16 22:32:26,130 - INFO - 日期 2025-08-16 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 22:32:26,130 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-16 22:32:26,130 - INFO - 同步完成
