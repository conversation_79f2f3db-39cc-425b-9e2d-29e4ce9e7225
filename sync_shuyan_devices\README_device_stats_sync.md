# 设备统计数据同步脚本

## 文件说明

`sync_device_stats_mysql2yida.py` - 设备统计数据从MySQL同步到宜搭表单的脚本

## 功能描述

该脚本实现了设备统计数据的自动同步功能，将MySQL数据库中的设备统计信息同步到宜搭表单中。

### 数据源SQL语句

```sql
SELECT 
    project_name AS 项目名称,
    b.shop_entity_name AS 数衍店铺,
    GROUP_CONCAT(DISTINCT device_type ORDER BY device_type) AS 设备类型,
    COUNT(*) AS 数量
FROM device_info a, shop_entity_mapping b
WHERE is_deleted_code = 'N' 
  AND device_state_code = '0'
  AND a.shop_entity_id = b.shop_entity_id
GROUP BY project_name, b.shop_entity_name
ORDER BY project_name, b.shop_entity_name;
```

### 比较字段

脚本只比较以下两个字段的变化：
- **设备类型** (`device_types`) - 多个设备类型用逗号分隔
- **设备数量** (`device_count`) - 该项目+店铺组合下的设备总数

### 数据结构

#### MySQL数据字段
- `project_name` - 项目名称
- `shop_entity_name` - 数衍店铺名称
- `device_types` - 设备类型（GROUP_CONCAT结果）
- `device_count` - 设备数量（COUNT结果）

#### 宜搭表单字段映射
- `textField_project_name` - 项目名称
- `textField_shop_name` - 数衍店铺
- `textField_device_types` - 设备类型
- `numberField_device_count` - 设备数量

## 配置说明

### 数据库配置
```python
DEVICE_STATS_DB_CONFIG = {
    'host': 'localhost',
    'port': 43306,
    'user': 'root',
    'password': 'Hxp@1987!@#',
    'database': 'mydatabase',
    'charset': 'utf8mb4'
}
```

### 宜搭配置
```python
DEVICE_STATS_YIDA_CONFIG = {
    'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
    'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
    'USER_ID': 'hexuepeng',
    'LANGUAGE': 'zh_CN',
    'FORM_UUID': 'FORM-DEVICE-STATS-UUID'  # 需要替换为实际的表单UUID
}
```

## 使用方法

### 前置条件
1. 确保数据库中存在 `device_info` 和 `shop_entity_mapping` 表
2. 确保宜搭表单已创建并获取正确的 `FORM_UUID`
3. 更新配置中的表单字段映射，确保与实际表单字段一致

### 运行脚本
```bash
python sync_device_stats_mysql2yida.py
```

### 日志文件
脚本运行时会生成日志文件：
- 位置：`logs/sync_device_stats_mysql2yida_log_YYYYMMDD.txt`
- 同时在控制台输出日志信息

## 同步逻辑

1. **数据获取**：从MySQL执行统计查询，获取按项目+店铺分组的设备统计数据
2. **数据索引**：使用"项目名称_店铺名称"作为唯一标识建立索引
3. **数据比较**：比较MySQL和宜搭中的设备类型和设备数量
4. **数据更新**：如果发现差异，更新宜搭表单中的对应记录
5. **数据插入**：如果宜搭中不存在对应记录，批量插入新记录

## 注意事项

1. **表单UUID**：需要将 `FORM_UUID` 替换为实际的宜搭表单UUID
2. **字段映射**：需要根据实际宜搭表单的字段ID更新 `DEVICE_STATS_FIELD_MAPPING`
3. **数据库权限**：确保数据库用户有读取相关表的权限
4. **网络连接**：确保能够访问宜搭API服务
5. **数据一致性**：建议在业务低峰期运行同步脚本

## 错误处理

脚本包含完整的错误处理机制：
- MySQL连接失败
- 宜搭API调用失败
- 数据格式转换错误
- 批量操作异常

所有错误信息都会记录在日志文件中，便于问题排查。
