2025-08-02 01:30:33,596 - INFO - 使用默认增量同步（当天更新数据）
2025-08-02 01:30:33,596 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-02 01:30:33,596 - INFO - 查询参数: ('2025-08-02',)
2025-08-02 01:30:33,752 - INFO - MySQL查询成功，增量数据（日期: 2025-08-02），共获取 13 条记录
2025-08-02 01:30:33,752 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 01:30:33,752 - INFO - 开始处理日期: 2025-08-01
2025-08-02 01:30:33,752 - INFO - Request Parameters - Page 1:
2025-08-02 01:30:33,752 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 01:30:33,752 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 01:30:41,861 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 7CF66A29-F921-7BD8-B63E-0320C9BE906E Response: {'code': 'ServiceUnavailable', 'requestid': '7CF66A29-F921-7BD8-B63E-0320C9BE906E', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 7CF66A29-F921-7BD8-B63E-0320C9BE906E)
2025-08-02 01:30:41,861 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-02 01:31:41,873 - INFO - 开始同步昨天与今天的销售数据: 2025-08-01 至 2025-08-02
2025-08-02 01:31:41,873 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-02 01:31:41,873 - INFO - 查询参数: ('2025-08-01', '2025-08-02')
2025-08-02 01:31:42,029 - INFO - MySQL查询成功，时间段: 2025-08-01 至 2025-08-02，共获取 154 条记录
2025-08-02 01:31:42,029 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 01:31:42,029 - INFO - 开始处理日期: 2025-08-01
2025-08-02 01:31:42,029 - INFO - Request Parameters - Page 1:
2025-08-02 01:31:42,029 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 01:31:42,029 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 01:31:49,701 - INFO - Response - Page 1:
2025-08-02 01:31:49,701 - INFO - 第 1 页获取到 50 条记录
2025-08-02 01:31:50,201 - INFO - Request Parameters - Page 2:
2025-08-02 01:31:50,201 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 01:31:50,201 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 01:31:50,701 - INFO - Response - Page 2:
2025-08-02 01:31:50,701 - INFO - 第 2 页获取到 3 条记录
2025-08-02 01:31:51,217 - INFO - 查询完成，共获取到 53 条记录
2025-08-02 01:31:51,217 - INFO - 获取到 53 条表单数据
2025-08-02 01:31:51,217 - INFO - 当前日期 2025-08-01 有 147 条MySQL数据需要处理
2025-08-02 01:31:51,217 - INFO - 开始更新记录 - 表单实例ID: FINST-CJ966Q71UUNX0Q49AA9EUC6HBOGM2JQSAXSDM8
2025-08-02 01:31:51,717 - INFO - 更新表单数据成功: FINST-CJ966Q71UUNX0Q49AA9EUC6HBOGM2JQSAXSDM8
2025-08-02 01:31:51,717 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 3274.0, 'new_value': 3334.0}, {'field': 'total_amount', 'old_value': 3596.0, 'new_value': 3656.0}, {'field': 'order_count', 'old_value': 54, 'new_value': 56}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-02 01:31:51,717 - INFO - 开始批量插入 94 条新记录
2025-08-02 01:31:51,982 - INFO - 批量插入响应状态码: 200
2025-08-02 01:31:51,982 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 17:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '8E325024-6C9E-738C-B631-9DD92E32292C', 'x-acs-trace-id': '0e7ec6e84c8548104a92c491acdafb09', 'etag': '24S2671LHj+kCScL/psQQRA2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 01:31:51,982 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMYC', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMZC', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM0D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM1D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM2D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM3D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM4D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM5D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM6D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM7D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM8D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM9D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMAD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMBD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMCD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMDD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMED', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMFD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMGD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMHD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMID', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMJD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMKD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMLD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMMD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMND', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMOD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMPD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMQD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMRD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMSD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMTD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMUD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMVD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMWD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMXD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMYD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMZD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM0E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM1E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM2E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM3E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM4E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM5E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM6E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM7E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM8E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM9E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMAE', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMBE']}
2025-08-02 01:31:51,982 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-02 01:31:51,982 - INFO - 成功插入的数据ID: ['FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMYC', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMZC', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM0D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM1D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM2D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM3D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM4D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM5D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM6D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM7D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM8D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM9D', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMAD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMBD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMCD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMDD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMED', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMFD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMGD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMHD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMID', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMJD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMKD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMLD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMMD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMND', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMOD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMPD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMQD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMRD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMSD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMTD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMUD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMVD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMWD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMXD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMYD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMZD', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM0E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM1E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM2E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM3E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM4E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM5E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM6E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM7E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM8E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDM9E', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMAE', 'FINST-XMC66R9129LXZXOU5B5K14IX3F5Q31F5P3TDMBE']
2025-08-02 01:31:57,201 - INFO - 批量插入响应状态码: 200
2025-08-02 01:31:57,201 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 17:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2124', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '01340B19-67DA-7774-9D7B-3D0184BC1EBD', 'x-acs-trace-id': 'a44fdb6ffab19b01b38501a6c4899d27', 'etag': '2+J9p3gzV5CAnwSruY+zPbw4', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 01:31:57,201 - INFO - 批量插入响应体: {'result': ['FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMO9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMP9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMQ9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMR9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMS9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMT9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMU9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMV9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMW9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMX9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMY9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMZ9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM0A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM1A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM2A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM3A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM4A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM5A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM6A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM7A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM8A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM9A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMAA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMBA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMCA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMDA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMEA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMFA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMGA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMHA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMIA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMJA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMKA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMLA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMMA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMNA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMOA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMPA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMQA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMRA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMSA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMTA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMUA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMVA']}
2025-08-02 01:31:57,201 - INFO - 批量插入表单数据成功，批次 2，共 44 条记录
2025-08-02 01:31:57,201 - INFO - 成功插入的数据ID: ['FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMO9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMP9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMQ9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMR9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMS9', 'FINST-7I866981FJMX9I10C1D5BA679OTF33G9P3TDMT9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMU9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMV9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMW9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMX9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMY9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMZ9', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM0A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM1A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM2A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM3A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM4A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM5A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM6A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM7A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM8A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDM9A', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMAA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMBA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMCA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMDA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMEA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMFA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMGA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMHA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMIA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMJA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMKA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMLA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMMA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMNA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMOA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMPA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMQA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMRA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMSA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMTA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMUA', 'FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMVA']
2025-08-02 01:32:02,212 - INFO - 批量插入完成，共 94 条记录
2025-08-02 01:32:02,212 - INFO - 日期 2025-08-01 处理完成 - 更新: 1 条，插入: 94 条，错误: 0 条
2025-08-02 01:32:02,212 - INFO - 数据同步完成！更新: 1 条，插入: 94 条，错误: 0 条
2025-08-02 01:32:02,212 - INFO - 同步完成
2025-08-02 04:30:33,604 - INFO - 使用默认增量同步（当天更新数据）
2025-08-02 04:30:33,604 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-02 04:30:33,604 - INFO - 查询参数: ('2025-08-02',)
2025-08-02 04:30:33,776 - INFO - MySQL查询成功，增量数据（日期: 2025-08-02），共获取 13 条记录
2025-08-02 04:30:33,776 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 04:30:33,776 - INFO - 开始处理日期: 2025-08-01
2025-08-02 04:30:33,776 - INFO - Request Parameters - Page 1:
2025-08-02 04:30:33,776 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 04:30:33,776 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 04:30:41,885 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: B8DA75A6-C431-7BD6-9628-BC2C16FAF7C3 Response: {'code': 'ServiceUnavailable', 'requestid': 'B8DA75A6-C431-7BD6-9628-BC2C16FAF7C3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: B8DA75A6-C431-7BD6-9628-BC2C16FAF7C3)
2025-08-02 04:30:41,885 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-02 04:31:41,897 - INFO - 开始同步昨天与今天的销售数据: 2025-08-01 至 2025-08-02
2025-08-02 04:31:41,897 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-02 04:31:41,897 - INFO - 查询参数: ('2025-08-01', '2025-08-02')
2025-08-02 04:31:42,054 - INFO - MySQL查询成功，时间段: 2025-08-01 至 2025-08-02，共获取 154 条记录
2025-08-02 04:31:42,054 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 04:31:42,054 - INFO - 开始处理日期: 2025-08-01
2025-08-02 04:31:42,054 - INFO - Request Parameters - Page 1:
2025-08-02 04:31:42,054 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 04:31:42,054 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 04:31:50,163 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 87C3D253-72F5-75B1-A72B-0863341813CA Response: {'code': 'ServiceUnavailable', 'requestid': '87C3D253-72F5-75B1-A72B-0863341813CA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 87C3D253-72F5-75B1-A72B-0863341813CA)
2025-08-02 04:31:50,163 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-02 04:31:50,163 - INFO - 同步完成
2025-08-02 07:30:33,501 - INFO - 使用默认增量同步（当天更新数据）
2025-08-02 07:30:33,501 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-02 07:30:33,501 - INFO - 查询参数: ('2025-08-02',)
2025-08-02 07:30:33,657 - INFO - MySQL查询成功，增量数据（日期: 2025-08-02），共获取 16 条记录
2025-08-02 07:30:33,657 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 07:30:33,657 - INFO - 开始处理日期: 2025-08-01
2025-08-02 07:30:33,657 - INFO - Request Parameters - Page 1:
2025-08-02 07:30:33,657 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 07:30:33,657 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 07:30:41,641 - INFO - Response - Page 1:
2025-08-02 07:30:41,641 - INFO - 第 1 页获取到 50 条记录
2025-08-02 07:30:42,157 - INFO - Request Parameters - Page 2:
2025-08-02 07:30:42,157 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 07:30:42,157 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 07:30:42,860 - INFO - Response - Page 2:
2025-08-02 07:30:42,860 - INFO - 第 2 页获取到 50 条记录
2025-08-02 07:30:43,376 - INFO - Request Parameters - Page 3:
2025-08-02 07:30:43,376 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 07:30:43,376 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 07:30:44,126 - INFO - Response - Page 3:
2025-08-02 07:30:44,126 - INFO - 第 3 页获取到 47 条记录
2025-08-02 07:30:44,626 - INFO - 查询完成，共获取到 147 条记录
2025-08-02 07:30:44,626 - INFO - 获取到 147 条表单数据
2025-08-02 07:30:44,626 - INFO - 当前日期 2025-08-01 有 16 条MySQL数据需要处理
2025-08-02 07:30:44,626 - INFO - 开始批量插入 3 条新记录
2025-08-02 07:30:44,782 - INFO - 批量插入响应状态码: 200
2025-08-02 07:30:44,782 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Fri, 01 Aug 2025 23:30:47 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DE2FB2FD-1675-7992-B6DE-2F7DCC51B900', 'x-acs-trace-id': 'cb8c75937f0a2fd813f908f3d519daf5', 'etag': '1gVuK5tpxIyve5RhRlAh3jg6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 07:30:44,782 - INFO - 批量插入响应体: {'result': ['FINST-2TG66D912JNXE4HFB44H87FK5CFH24IQIGTDM23', 'FINST-2TG66D912JNXE4HFB44H87FK5CFH24IQIGTDM33', 'FINST-2TG66D912JNXE4HFB44H87FK5CFH24IQIGTDM43']}
2025-08-02 07:30:44,782 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-02 07:30:44,782 - INFO - 成功插入的数据ID: ['FINST-2TG66D912JNXE4HFB44H87FK5CFH24IQIGTDM23', 'FINST-2TG66D912JNXE4HFB44H87FK5CFH24IQIGTDM33', 'FINST-2TG66D912JNXE4HFB44H87FK5CFH24IQIGTDM43']
2025-08-02 07:30:49,797 - INFO - 批量插入完成，共 3 条记录
2025-08-02 07:30:49,797 - INFO - 日期 2025-08-01 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-08-02 07:30:49,797 - INFO - 数据同步完成！更新: 0 条，插入: 3 条，错误: 0 条
2025-08-02 07:31:49,808 - INFO - 开始同步昨天与今天的销售数据: 2025-08-01 至 2025-08-02
2025-08-02 07:31:49,808 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-02 07:31:49,808 - INFO - 查询参数: ('2025-08-01', '2025-08-02')
2025-08-02 07:31:49,964 - INFO - MySQL查询成功，时间段: 2025-08-01 至 2025-08-02，共获取 175 条记录
2025-08-02 07:31:49,964 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 07:31:49,964 - INFO - 开始处理日期: 2025-08-01
2025-08-02 07:31:49,964 - INFO - Request Parameters - Page 1:
2025-08-02 07:31:49,964 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 07:31:49,964 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 07:31:58,073 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 394ECF0E-3B0C-7A5E-8F78-C20E34E733D1 Response: {'code': 'ServiceUnavailable', 'requestid': '394ECF0E-3B0C-7A5E-8F78-C20E34E733D1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 394ECF0E-3B0C-7A5E-8F78-C20E34E733D1)
2025-08-02 07:31:58,073 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-02 07:31:58,073 - INFO - 同步完成
2025-08-02 10:30:33,546 - INFO - 使用默认增量同步（当天更新数据）
2025-08-02 10:30:33,546 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-02 10:30:33,546 - INFO - 查询参数: ('2025-08-02',)
2025-08-02 10:30:33,718 - INFO - MySQL查询成功，增量数据（日期: 2025-08-02），共获取 70 条记录
2025-08-02 10:30:33,718 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 10:30:33,718 - INFO - 开始处理日期: 2025-08-01
2025-08-02 10:30:33,718 - INFO - Request Parameters - Page 1:
2025-08-02 10:30:33,718 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 10:30:33,718 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 10:30:41,843 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 8295FE59-2033-73D1-BFC0-0134DA6313E3 Response: {'code': 'ServiceUnavailable', 'requestid': '8295FE59-2033-73D1-BFC0-0134DA6313E3', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 8295FE59-2033-73D1-BFC0-0134DA6313E3)
2025-08-02 10:30:41,843 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-02 10:31:41,853 - INFO - 开始同步昨天与今天的销售数据: 2025-08-01 至 2025-08-02
2025-08-02 10:31:41,853 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-02 10:31:41,853 - INFO - 查询参数: ('2025-08-01', '2025-08-02')
2025-08-02 10:31:42,009 - INFO - MySQL查询成功，时间段: 2025-08-01 至 2025-08-02，共获取 280 条记录
2025-08-02 10:31:42,009 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 10:31:42,009 - INFO - 开始处理日期: 2025-08-01
2025-08-02 10:31:42,009 - INFO - Request Parameters - Page 1:
2025-08-02 10:31:42,009 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 10:31:42,009 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 10:31:49,978 - INFO - Response - Page 1:
2025-08-02 10:31:49,978 - INFO - 第 1 页获取到 50 条记录
2025-08-02 10:31:50,478 - INFO - Request Parameters - Page 2:
2025-08-02 10:31:50,478 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 10:31:50,478 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 10:31:51,244 - INFO - Response - Page 2:
2025-08-02 10:31:51,244 - INFO - 第 2 页获取到 50 条记录
2025-08-02 10:31:51,744 - INFO - Request Parameters - Page 3:
2025-08-02 10:31:51,744 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 10:31:51,744 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 10:31:52,541 - INFO - Response - Page 3:
2025-08-02 10:31:52,541 - INFO - 第 3 页获取到 50 条记录
2025-08-02 10:31:53,041 - INFO - Request Parameters - Page 4:
2025-08-02 10:31:53,041 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 10:31:53,041 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 10:31:53,541 - INFO - Response - Page 4:
2025-08-02 10:31:53,541 - INFO - 查询完成，共获取到 150 条记录
2025-08-02 10:31:53,541 - INFO - 获取到 150 条表单数据
2025-08-02 10:31:53,556 - INFO - 当前日期 2025-08-01 有 265 条MySQL数据需要处理
2025-08-02 10:31:53,556 - INFO - 开始更新记录 - 表单实例ID: FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM4S
2025-08-02 10:31:54,197 - INFO - 更新表单数据成功: FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM4S
2025-08-02 10:31:54,197 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 5000.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 1}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-02 10:31:54,197 - INFO - 开始批量插入 115 条新记录
2025-08-02 10:31:54,478 - INFO - 批量插入响应状态码: 200
2025-08-02 10:31:54,478 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 02:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '114FBC6F-ED07-7986-AA25-E949D16B1E0D', 'x-acs-trace-id': 'b9808de9853ab6d92795649850ca5abf', 'etag': '2rXH3uJBD0yI9J15PH/aaXw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 10:31:54,478 - INFO - 批量插入响应体: {'result': ['FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM76', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM86', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM96', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMA6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMB6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMC6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMD6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDME6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMF6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMG6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMH6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMI6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMJ6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMK6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDML6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMM6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMN6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMO6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMP6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMQ6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMR6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMS6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMT6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMU6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMV6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMW6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMX6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMY6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMZ6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM07', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM17', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM27', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM37', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM47', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM57', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM67', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM77', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM87', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM97', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMA7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMB7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMC7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMD7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDME7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMF7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMG7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMH7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMI7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMJ7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMK7']}
2025-08-02 10:31:54,478 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-02 10:31:54,478 - INFO - 成功插入的数据ID: ['FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM76', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM86', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM96', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMA6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMB6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMC6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMD6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDME6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMF6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMG6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMH6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMI6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMJ6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMK6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDML6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMM6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMN6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMO6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMP6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMQ6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMR6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMS6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMT6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMU6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMV6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMW6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMX6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMY6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMZ6', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM07', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM17', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM27', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM37', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM47', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM57', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM67', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM77', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM87', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDM97', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMA7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMB7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMC7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMD7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDME7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMF7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMG7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMH7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMI7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMJ7', 'FINST-R8666Q71W9JXQFDD7QYBZ44H63RV38OPZMTDMK7']
2025-08-02 10:31:59,728 - INFO - 批量插入响应状态码: 200
2025-08-02 10:31:59,728 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 02:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5B5145B6-CAE1-7E8F-9A78-6149112D632B', 'x-acs-trace-id': '5c797ea6b1c359e38b6c62e6589f5e07', 'etag': '2q/JCwInUBi6PEgHFEgnhhw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 10:31:59,728 - INFO - 批量插入响应体: {'result': ['FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMI8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMJ8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMK8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDML8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMM8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMN8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMO8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMP8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMQ8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMR8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMS8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMT8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMU8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMV8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMW8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMX8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMY8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMZ8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM09', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM19', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM29', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM39', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM49', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM59', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM69', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM79', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM89', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM99', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMA9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMB9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMC9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMD9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDME9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMF9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMG9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMH9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMI9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMJ9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMK9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDML9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMM9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMN9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMO9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMP9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMQ9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMR9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMS9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMT9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMU9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMV9']}
2025-08-02 10:31:59,728 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-02 10:31:59,728 - INFO - 成功插入的数据ID: ['FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMI8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMJ8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMK8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDML8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMM8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMN8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMO8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMP8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMQ8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMR8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMS8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMT8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMU8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMV8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMW8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMX8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMY8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMZ8', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM09', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM19', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM29', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM39', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM49', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM59', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM69', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM79', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM89', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDM99', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMA9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMB9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMC9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMD9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDME9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMF9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMG9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMH9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMI9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMJ9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMK9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDML9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMM9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMN9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMO9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMP9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMQ9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMR9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMS9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMT9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMU9', 'FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD20QTZMTDMV9']
2025-08-02 10:32:04,900 - INFO - 批量插入响应状态码: 200
2025-08-02 10:32:04,900 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 02:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '732', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '01A7ABD6-051F-7077-AD08-C5D79A5B17C6', 'x-acs-trace-id': '6e8dca7a2b54d6d48aa37956db0be6f0', 'etag': '7qZej66mHYzb64svPDpE4Yg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 10:32:04,900 - INFO - 批量插入响应体: {'result': ['FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMFE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMGE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMHE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMIE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMJE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMKE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMLE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMME', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMNE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMOE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMPE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMQE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMRE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMSE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMTE']}
2025-08-02 10:32:04,900 - INFO - 批量插入表单数据成功，批次 3，共 15 条记录
2025-08-02 10:32:04,900 - INFO - 成功插入的数据ID: ['FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMFE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMGE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMHE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMIE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMJE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMKE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMLE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMME', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMNE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMOE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMPE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMQE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMRE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMSE', 'FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMTE']
2025-08-02 10:32:09,915 - INFO - 批量插入完成，共 115 条记录
2025-08-02 10:32:09,915 - INFO - 日期 2025-08-01 处理完成 - 更新: 1 条，插入: 115 条，错误: 0 条
2025-08-02 10:32:09,915 - INFO - 数据同步完成！更新: 1 条，插入: 115 条，错误: 0 条
2025-08-02 10:32:09,915 - INFO - 同步完成
2025-08-02 13:30:33,560 - INFO - 使用默认增量同步（当天更新数据）
2025-08-02 13:30:33,560 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-02 13:30:33,560 - INFO - 查询参数: ('2025-08-02',)
2025-08-02 13:30:33,731 - INFO - MySQL查询成功，增量数据（日期: 2025-08-02），共获取 130 条记录
2025-08-02 13:30:33,731 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 13:30:33,731 - INFO - 开始处理日期: 2025-08-01
2025-08-02 13:30:33,731 - INFO - Request Parameters - Page 1:
2025-08-02 13:30:33,731 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 13:30:33,731 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 13:30:41,841 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 063EE23A-0CA1-7918-BF78-B3B7C26FFE1F Response: {'code': 'ServiceUnavailable', 'requestid': '063EE23A-0CA1-7918-BF78-B3B7C26FFE1F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 063EE23A-0CA1-7918-BF78-B3B7C26FFE1F)
2025-08-02 13:30:41,841 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-02 13:31:41,851 - INFO - 开始同步昨天与今天的销售数据: 2025-08-01 至 2025-08-02
2025-08-02 13:31:41,851 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-02 13:31:41,851 - INFO - 查询参数: ('2025-08-01', '2025-08-02')
2025-08-02 13:31:42,023 - INFO - MySQL查询成功，时间段: 2025-08-01 至 2025-08-02，共获取 468 条记录
2025-08-02 13:31:42,023 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 13:31:42,023 - INFO - 开始处理日期: 2025-08-01
2025-08-02 13:31:42,023 - INFO - Request Parameters - Page 1:
2025-08-02 13:31:42,023 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 13:31:42,023 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 13:31:42,804 - INFO - Response - Page 1:
2025-08-02 13:31:42,820 - INFO - 第 1 页获取到 50 条记录
2025-08-02 13:31:43,335 - INFO - Request Parameters - Page 2:
2025-08-02 13:31:43,335 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 13:31:43,335 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 13:31:44,070 - INFO - Response - Page 2:
2025-08-02 13:31:44,070 - INFO - 第 2 页获取到 50 条记录
2025-08-02 13:31:44,585 - INFO - Request Parameters - Page 3:
2025-08-02 13:31:44,585 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 13:31:44,585 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 13:31:52,007 - INFO - Response - Page 3:
2025-08-02 13:31:52,007 - INFO - 第 3 页获取到 50 条记录
2025-08-02 13:31:52,523 - INFO - Request Parameters - Page 4:
2025-08-02 13:31:52,523 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 13:31:52,523 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 13:31:53,320 - INFO - Response - Page 4:
2025-08-02 13:31:53,320 - INFO - 第 4 页获取到 50 条记录
2025-08-02 13:31:53,835 - INFO - Request Parameters - Page 5:
2025-08-02 13:31:53,835 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 13:31:53,835 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 13:31:54,570 - INFO - Response - Page 5:
2025-08-02 13:31:54,570 - INFO - 第 5 页获取到 50 条记录
2025-08-02 13:31:55,085 - INFO - Request Parameters - Page 6:
2025-08-02 13:31:55,085 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 13:31:55,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 13:31:55,757 - INFO - Response - Page 6:
2025-08-02 13:31:55,757 - INFO - 第 6 页获取到 15 条记录
2025-08-02 13:31:56,273 - INFO - 查询完成，共获取到 265 条记录
2025-08-02 13:31:56,273 - INFO - 获取到 265 条表单数据
2025-08-02 13:31:56,273 - INFO - 当前日期 2025-08-01 有 448 条MySQL数据需要处理
2025-08-02 13:31:56,273 - INFO - 开始更新记录 - 表单实例ID: FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMTE
2025-08-02 13:31:57,023 - INFO - 更新表单数据成功: FINST-K7666JC13BMXGCMKACVLX692O47G2QPXZMTDMTE
2025-08-02 13:31:57,023 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 30000.0, 'new_value': 1270.0}, {'field': 'total_amount', 'old_value': 30000.0, 'new_value': 1270.0}, {'field': 'order_count', 'old_value': 5, 'new_value': 1}]
2025-08-02 13:31:57,023 - INFO - 开始更新记录 - 表单实例ID: FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMOA
2025-08-02 13:31:57,648 - INFO - 更新表单数据成功: FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMOA
2025-08-02 13:31:57,648 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 50000.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 50000.0}]
2025-08-02 13:31:57,648 - INFO - 开始更新记录 - 表单实例ID: FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMKA
2025-08-02 13:31:58,210 - INFO - 更新表单数据成功: FINST-7I866981FJMX9I10C1D5BA679OTF34G9P3TDMKA
2025-08-02 13:31:58,210 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 200000.0, 'new_value': 150000.0}, {'field': 'total_amount', 'old_value': 200000.0, 'new_value': 150000.0}]
2025-08-02 13:31:58,210 - INFO - 开始更新记录 - 表单实例ID: FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM3S
2025-08-02 13:31:58,851 - INFO - 更新表单数据成功: FINST-DKB66TA1A9KXGF096V5CV5RZOJIC3HI9I7SDM3S
2025-08-02 13:31:58,851 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 10394.19}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 10394.19}, {'field': 'order_count', 'old_value': 0, 'new_value': 1129}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/728f917c495f4f9baf0a9207cfab3440.png?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=XGXLlDm3GZIM5lXgALvZaABqQ6g%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/086e21876a274a7ebde54db5d17d04b2.png?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=6y7JMFIrSZkdVtPz%2BmP498it4MA%3D'}]
2025-08-02 13:31:58,851 - INFO - 开始批量插入 183 条新记录
2025-08-02 13:31:59,117 - INFO - 批量插入响应状态码: 200
2025-08-02 13:31:59,117 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 05:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '0506BFEB-9D34-7187-837A-2F74F17995DC', 'x-acs-trace-id': 'e0396642c34fe4e106a6984af716f768', 'etag': '2eO/iizg6Iy0CBJMHI2iGhQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 13:31:59,117 - INFO - 批量插入响应体: {'result': ['FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMQ3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMR3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMS3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMT3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMU3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMV3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMW3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMX3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMY3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMZ3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM04', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM14', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM24', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM34', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM44', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM54', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM64', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM74', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM84', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM94', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMA4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMB4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMC4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMD4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDME4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMF4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMG4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMH4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMI4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMJ4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMK4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDML4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMM4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMN4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMO4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMP4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMQ4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMR4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMS4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMT4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMU4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMV4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMW4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMX4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMY4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMZ4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM05', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM15', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM25', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM35']}
2025-08-02 13:31:59,117 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-02 13:31:59,117 - INFO - 成功插入的数据ID: ['FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMQ3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMR3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMS3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMT3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMU3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMV3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMW3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMX3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMY3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMZ3', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM04', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM14', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM24', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM34', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM44', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM54', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM64', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM74', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM84', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM94', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMA4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMB4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMC4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMD4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDME4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMF4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMG4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMH4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMI4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMJ4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMK4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDML4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMM4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMN4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMO4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMP4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMQ4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMR4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMS4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMT4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMU4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMV4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMW4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMX4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMY4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDMZ4', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM05', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM15', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM25', 'FINST-W3B66L71TMNX1YF39D0XW40OBIDI24NAFTTDM35']
2025-08-02 13:32:04,351 - INFO - 批量插入响应状态码: 200
2025-08-02 13:32:04,351 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 05:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '28A59C87-B3E1-7C93-9B8C-1AC86E5BACFB', 'x-acs-trace-id': '76c3abf9f503880d7f103f05154c4171', 'etag': '2cfmZWIv7BxBNQxDJpaOeSQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 13:32:04,351 - INFO - 批量插入响应体: {'result': ['FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMK3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDML3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMM3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMN3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMO3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMP3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMQ3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMR3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMS3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMT3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMU3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMV3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMW3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMX3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMY3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMZ3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM04', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM14', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM24', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM34', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM44', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM54', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM64', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM74', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM84', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM94', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMA4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMB4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMC4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMD4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDME4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMF4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMG4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMH4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMI4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMJ4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMK4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDML4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMM4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMN4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMO4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMP4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMQ4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMR4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMS4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMT4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMU4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMV4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMW4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMX4']}
2025-08-02 13:32:04,351 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-02 13:32:04,351 - INFO - 成功插入的数据ID: ['FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMK3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDML3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMM3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMN3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMO3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMP3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMQ3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMR3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMS3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMT3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMU3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMV3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMW3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMX3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMY3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMZ3', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM04', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM14', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM24', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM34', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM44', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM54', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM64', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM74', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM84', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDM94', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMA4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMB4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMC4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMD4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDME4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMF4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMG4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMH4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMI4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMJ4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMK4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDML4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMM4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMN4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMO4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMP4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMQ4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMR4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMS4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMT4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMU4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMV4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMW4', 'FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMX4']
2025-08-02 13:32:09,617 - INFO - 批量插入响应状态码: 200
2025-08-02 13:32:09,617 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 05:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '1AA53553-A6EB-7C78-BAA3-12BA6F4F63E8', 'x-acs-trace-id': 'a5981d189c81aec32a0705e9346b5ad5', 'etag': '23/Hzt8oCMxiBZdyMVTgSoQ2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 13:32:09,617 - INFO - 批量插入响应体: {'result': ['FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMPD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMQD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMRD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMSD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMTD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMUD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMVD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMWD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMXD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMYD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMZD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDM0E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDM1E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM2E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM3E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM4E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM5E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM6E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM7E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM8E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM9E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMAE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMBE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMCE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMDE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMEE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMFE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMGE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMHE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMIE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMJE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMKE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMLE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMME', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMNE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMOE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMPE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMQE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMRE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMSE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMTE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMUE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMVE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMWE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMXE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMYE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMZE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM0F', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM1F', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM2F']}
2025-08-02 13:32:09,617 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-02 13:32:09,617 - INFO - 成功插入的数据ID: ['FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMPD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMQD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMRD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMSD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMTD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMUD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMVD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMWD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMXD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMYD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDMZD', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDM0E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53JQIFTTDM1E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM2E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM3E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM4E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM5E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM6E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM7E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM8E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM9E', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMAE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMBE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMCE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMDE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMEE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMFE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMGE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMHE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMIE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMJE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMKE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMLE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMME', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMNE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMOE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMPE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMQE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMRE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMSE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMTE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMUE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMVE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMWE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMXE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMYE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDMZE', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM0F', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM1F', 'FINST-S0E660A1KGKXK2JI60FLB8XB6PX53KQIFTTDM2F']
2025-08-02 13:32:14,867 - INFO - 批量插入响应状态码: 200
2025-08-02 13:32:14,867 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 05:32:17 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1596', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E9C4A0B7-C45B-7C50-AB7E-C38A9638A3CD', 'x-acs-trace-id': '2de56ca64eb53247ea0f2f05f7585f83', 'etag': '1OMxbnKiNV6uyp2C0HcVLgw6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 13:32:14,867 - INFO - 批量插入响应体: {'result': ['FINST-NDC66NB15MMXI97M9HMR5AIW5ED22JSMFTTDM9K', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMAK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMBK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMCK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMDK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMEK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMFK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMGK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMHK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMIK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMJK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMKK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMLK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMMK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMNK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMOK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMPK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMQK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMRK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMSK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMTK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMUK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMVK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMWK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMXK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMYK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMZK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM0L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM1L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM2L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM3L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM4L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM5L']}
2025-08-02 13:32:14,867 - INFO - 批量插入表单数据成功，批次 4，共 33 条记录
2025-08-02 13:32:14,867 - INFO - 成功插入的数据ID: ['FINST-NDC66NB15MMXI97M9HMR5AIW5ED22JSMFTTDM9K', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMAK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMBK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMCK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMDK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMEK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMFK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMGK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMHK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMIK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMJK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMKK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMLK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMMK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMNK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMOK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMPK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMQK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMRK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMSK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMTK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMUK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMVK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMWK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMXK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMYK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDMZK', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM0L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM1L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM2L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM3L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM4L', 'FINST-NDC66NB15MMXI97M9HMR5AIW5ED22KSMFTTDM5L']
2025-08-02 13:32:19,877 - INFO - 批量插入完成，共 183 条记录
2025-08-02 13:32:19,877 - INFO - 日期 2025-08-01 处理完成 - 更新: 4 条，插入: 183 条，错误: 0 条
2025-08-02 13:32:19,877 - INFO - 数据同步完成！更新: 4 条，插入: 183 条，错误: 0 条
2025-08-02 13:32:19,877 - INFO - 同步完成
2025-08-02 16:30:33,558 - INFO - 使用默认增量同步（当天更新数据）
2025-08-02 16:30:33,558 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-02 16:30:33,558 - INFO - 查询参数: ('2025-08-02',)
2025-08-02 16:30:33,714 - INFO - MySQL查询成功，增量数据（日期: 2025-08-02），共获取 131 条记录
2025-08-02 16:30:33,714 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 16:30:33,714 - INFO - 开始处理日期: 2025-08-01
2025-08-02 16:30:33,730 - INFO - Request Parameters - Page 1:
2025-08-02 16:30:33,730 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 16:30:33,730 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 16:30:41,839 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: E3EDA150-2EF8-77D6-B3C1-2D7724F46E21 Response: {'code': 'ServiceUnavailable', 'requestid': 'E3EDA150-2EF8-77D6-B3C1-2D7724F46E21', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: E3EDA150-2EF8-77D6-B3C1-2D7724F46E21)
2025-08-02 16:30:41,839 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-02 16:31:41,849 - INFO - 开始同步昨天与今天的销售数据: 2025-08-01 至 2025-08-02
2025-08-02 16:31:41,849 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-02 16:31:41,849 - INFO - 查询参数: ('2025-08-01', '2025-08-02')
2025-08-02 16:31:42,021 - INFO - MySQL查询成功，时间段: 2025-08-01 至 2025-08-02，共获取 468 条记录
2025-08-02 16:31:42,021 - INFO - 获取到 1 个日期需要处理: ['2025-08-01']
2025-08-02 16:31:42,021 - INFO - 开始处理日期: 2025-08-01
2025-08-02 16:31:42,021 - INFO - Request Parameters - Page 1:
2025-08-02 16:31:42,021 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 16:31:42,021 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 16:31:50,131 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 614AA299-C3A3-7B19-8929-4EF5449D0F39 Response: {'code': 'ServiceUnavailable', 'requestid': '614AA299-C3A3-7B19-8929-4EF5449D0F39', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 614AA299-C3A3-7B19-8929-4EF5449D0F39)
2025-08-02 16:31:50,131 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-02 16:31:50,131 - INFO - 同步完成
2025-08-02 19:30:33,526 - INFO - 使用默认增量同步（当天更新数据）
2025-08-02 19:30:33,526 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-02 19:30:33,526 - INFO - 查询参数: ('2025-08-02',)
2025-08-02 19:30:33,683 - INFO - MySQL查询成功，增量数据（日期: 2025-08-02），共获取 132 条记录
2025-08-02 19:30:33,698 - INFO - 获取到 2 个日期需要处理: ['2025-08-01', '2025-08-02']
2025-08-02 19:30:33,698 - INFO - 开始处理日期: 2025-08-01
2025-08-02 19:30:33,698 - INFO - Request Parameters - Page 1:
2025-08-02 19:30:33,698 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:30:33,698 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:30:41,822 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1EAFCD17-B229-7285-BC81-CE92B5EAA2BA Response: {'code': 'ServiceUnavailable', 'requestid': '1EAFCD17-B229-7285-BC81-CE92B5EAA2BA', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1EAFCD17-B229-7285-BC81-CE92B5EAA2BA)
2025-08-02 19:30:41,822 - INFO - 开始处理日期: 2025-08-02
2025-08-02 19:30:41,822 - INFO - Request Parameters - Page 1:
2025-08-02 19:30:41,822 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:30:41,822 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:30:49,947 - ERROR - 处理日期 2025-08-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: AD7B9FF6-B2C1-7307-B74D-D380A9385A82 Response: {'code': 'ServiceUnavailable', 'requestid': 'AD7B9FF6-B2C1-7307-B74D-D380A9385A82', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: AD7B9FF6-B2C1-7307-B74D-D380A9385A82)
2025-08-02 19:30:49,947 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 2 条
2025-08-02 19:31:49,951 - INFO - 开始同步昨天与今天的销售数据: 2025-08-01 至 2025-08-02
2025-08-02 19:31:49,951 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-02 19:31:49,951 - INFO - 查询参数: ('2025-08-01', '2025-08-02')
2025-08-02 19:31:50,123 - INFO - MySQL查询成功，时间段: 2025-08-01 至 2025-08-02，共获取 469 条记录
2025-08-02 19:31:50,123 - INFO - 获取到 2 个日期需要处理: ['2025-08-01', '2025-08-02']
2025-08-02 19:31:50,123 - INFO - 开始处理日期: 2025-08-01
2025-08-02 19:31:50,123 - INFO - Request Parameters - Page 1:
2025-08-02 19:31:50,123 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:31:50,123 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:31:50,998 - INFO - Response - Page 1:
2025-08-02 19:31:50,998 - INFO - 第 1 页获取到 50 条记录
2025-08-02 19:31:51,513 - INFO - Request Parameters - Page 2:
2025-08-02 19:31:51,513 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:31:51,513 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:31:52,357 - INFO - Response - Page 2:
2025-08-02 19:31:52,357 - INFO - 第 2 页获取到 50 条记录
2025-08-02 19:31:52,873 - INFO - Request Parameters - Page 3:
2025-08-02 19:31:52,873 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:31:52,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:31:53,607 - INFO - Response - Page 3:
2025-08-02 19:31:53,607 - INFO - 第 3 页获取到 50 条记录
2025-08-02 19:31:54,122 - INFO - Request Parameters - Page 4:
2025-08-02 19:31:54,122 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:31:54,122 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:31:54,888 - INFO - Response - Page 4:
2025-08-02 19:31:54,888 - INFO - 第 4 页获取到 50 条记录
2025-08-02 19:31:55,388 - INFO - Request Parameters - Page 5:
2025-08-02 19:31:55,388 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:31:55,388 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:31:56,154 - INFO - Response - Page 5:
2025-08-02 19:31:56,154 - INFO - 第 5 页获取到 50 条记录
2025-08-02 19:31:56,669 - INFO - Request Parameters - Page 6:
2025-08-02 19:31:56,669 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:31:56,669 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:31:57,419 - INFO - Response - Page 6:
2025-08-02 19:31:57,419 - INFO - 第 6 页获取到 50 条记录
2025-08-02 19:31:57,935 - INFO - Request Parameters - Page 7:
2025-08-02 19:31:57,935 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:31:57,935 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:31:58,685 - INFO - Response - Page 7:
2025-08-02 19:31:58,685 - INFO - 第 7 页获取到 50 条记录
2025-08-02 19:31:59,184 - INFO - Request Parameters - Page 8:
2025-08-02 19:31:59,184 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:31:59,184 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:31:59,966 - INFO - Response - Page 8:
2025-08-02 19:31:59,966 - INFO - 第 8 页获取到 50 条记录
2025-08-02 19:32:00,466 - INFO - Request Parameters - Page 9:
2025-08-02 19:32:00,466 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:32:00,466 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:32:01,231 - INFO - Response - Page 9:
2025-08-02 19:32:01,231 - INFO - 第 9 页获取到 48 条记录
2025-08-02 19:32:01,747 - INFO - 查询完成，共获取到 448 条记录
2025-08-02 19:32:01,747 - INFO - 获取到 448 条表单数据
2025-08-02 19:32:01,747 - INFO - 当前日期 2025-08-01 有 448 条MySQL数据需要处理
2025-08-02 19:32:01,762 - INFO - 开始更新记录 - 表单实例ID: FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMM8
2025-08-02 19:32:02,340 - INFO - 更新表单数据成功: FINST-HXD667B19AMXBGK5CQ4A4BCKPPBD2ZPTZMTDMM8
2025-08-02 19:32:02,340 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 11100.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 11100.0}, {'field': 'order_count', 'old_value': 0, 'new_value': 3}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}, {'field': 'url', 'old_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/519a125c86db445c8644b73835336a3e.jpg?Expires=2067580947&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=c1JyizX06stz0HBEiRAKQRtOJgo%3D', 'new_value': 'http://khtyyy.oss-cn-shenzhen.aliyuncs.com/yxh/b6cd79d8ccdc44f4930efc5d54503429.jpg?Expires=2067581145&OSSAccessKeyId=LTAI5tQ6JaCEKqNXfzn11Hcz&Signature=iE1EZk6EqH2cbGiWEhIr9fc3P%2FU%3D'}]
2025-08-02 19:32:02,340 - INFO - 日期 2025-08-01 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-02 19:32:02,340 - INFO - 开始处理日期: 2025-08-02
2025-08-02 19:32:02,340 - INFO - Request Parameters - Page 1:
2025-08-02 19:32:02,340 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 19:32:02,340 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 19:32:02,809 - INFO - Response - Page 1:
2025-08-02 19:32:02,809 - INFO - 查询完成，共获取到 0 条记录
2025-08-02 19:32:02,809 - INFO - 获取到 0 条表单数据
2025-08-02 19:32:02,809 - INFO - 当前日期 2025-08-02 有 1 条MySQL数据需要处理
2025-08-02 19:32:02,809 - INFO - 开始批量插入 1 条新记录
2025-08-02 19:32:02,965 - INFO - 批量插入响应状态码: 200
2025-08-02 19:32:02,965 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 11:32:03 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '5A740D75-8310-795A-A5E6-34A2C89567B4', 'x-acs-trace-id': '481d34b4148a4017c691e2ef0387105b', 'etag': '6cVA9x41WFI+nHjwnIPHBmw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 19:32:02,965 - INFO - 批量插入响应体: {'result': ['FINST-8LG66D71QLNX4GASF1LAI4G6H6V4321AA6UDMA2']}
2025-08-02 19:32:02,965 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-02 19:32:02,965 - INFO - 成功插入的数据ID: ['FINST-8LG66D71QLNX4GASF1LAI4G6H6V4321AA6UDMA2']
2025-08-02 19:32:07,980 - INFO - 批量插入完成，共 1 条记录
2025-08-02 19:32:07,980 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-02 19:32:07,980 - INFO - 数据同步完成！更新: 1 条，插入: 1 条，错误: 0 条
2025-08-02 19:32:07,980 - INFO - 同步完成
2025-08-02 22:30:33,222 - INFO - 使用默认增量同步（当天更新数据）
2025-08-02 22:30:33,222 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-02 22:30:33,222 - INFO - 查询参数: ('2025-08-02',)
2025-08-02 22:30:33,394 - INFO - MySQL查询成功，增量数据（日期: 2025-08-02），共获取 229 条记录
2025-08-02 22:30:33,394 - INFO - 获取到 2 个日期需要处理: ['2025-08-01', '2025-08-02']
2025-08-02 22:30:33,409 - INFO - 开始处理日期: 2025-08-01
2025-08-02 22:30:33,409 - INFO - Request Parameters - Page 1:
2025-08-02 22:30:33,409 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:30:33,409 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:30:41,455 - INFO - Response - Page 1:
2025-08-02 22:30:41,455 - INFO - 第 1 页获取到 50 条记录
2025-08-02 22:30:41,971 - INFO - Request Parameters - Page 2:
2025-08-02 22:30:41,971 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:30:41,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:30:50,079 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 1CE0AC96-F95D-7955-865B-65B2B976FDCF Response: {'code': 'ServiceUnavailable', 'requestid': '1CE0AC96-F95D-7955-865B-65B2B976FDCF', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 1CE0AC96-F95D-7955-865B-65B2B976FDCF)
2025-08-02 22:30:50,079 - INFO - 开始处理日期: 2025-08-02
2025-08-02 22:30:50,079 - INFO - Request Parameters - Page 1:
2025-08-02 22:30:50,079 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:30:50,079 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:30:54,188 - INFO - Response - Page 1:
2025-08-02 22:30:54,188 - INFO - 第 1 页获取到 1 条记录
2025-08-02 22:30:54,704 - INFO - 查询完成，共获取到 1 条记录
2025-08-02 22:30:54,704 - INFO - 获取到 1 条表单数据
2025-08-02 22:30:54,704 - INFO - 当前日期 2025-08-02 有 77 条MySQL数据需要处理
2025-08-02 22:30:54,704 - INFO - 开始批量插入 76 条新记录
2025-08-02 22:30:54,938 - INFO - 批量插入响应状态码: 200
2025-08-02 22:30:54,938 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 14:30:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '248F0469-05A3-7DCB-9D1E-9B09E2C3D6FE', 'x-acs-trace-id': '125ad64c4dc6509d0fe6f66eca04aa1f', 'etag': '2Dp1fDC+IEQpCqfV5+e/bCg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 22:30:54,938 - INFO - 批量插入响应体: {'result': ['FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMKC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMLC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMMC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMNC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMOC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMPC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMQC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMRC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMSC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMTC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMUC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMVC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMWC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMXC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMYC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMZC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM0D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM1D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM2D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM3D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM4D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM5D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM6D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM7D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM8D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM9D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMAD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMBD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMCD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMDD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMED', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMFD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMGD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMHD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMID', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMJD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMKD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMLD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMMD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMND', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMOD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMPD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMQD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMRD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMSD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMTD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMUD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMVD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMWD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMXD']}
2025-08-02 22:30:54,938 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-02 22:30:54,938 - INFO - 成功插入的数据ID: ['FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMKC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMLC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMMC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMNC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMOC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMPC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMQC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMRC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMSC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMTC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMUC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMVC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMWC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMXC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMYC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMZC', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM0D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM1D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM2D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM3D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM4D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM5D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM6D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM7D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM8D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM9D', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMAD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMBD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMCD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMDD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMED', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMFD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMGD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMHD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMID', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMJD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMKD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMLD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMMD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDMND', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMOD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMPD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMQD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMRD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMSD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMTD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMUD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMVD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMWD', 'FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMXD']
2025-08-02 22:31:00,188 - INFO - 批量插入响应状态码: 200
2025-08-02 22:31:00,188 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 14:31:01 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1260', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '131A3202-9EBB-7578-8A10-D026EA9B53E7', 'x-acs-trace-id': '0a7df7cba2e24f29a439f5f32e84e228', 'etag': '19o4JU9ozXLKgHrz8MNFTzA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 22:31:00,203 - INFO - 批量插入响应体: {'result': ['FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMXL', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMYL', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMZL', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM0M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM1M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM2M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM3M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM4M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM5M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM6M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM7M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM8M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM9M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMAM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMBM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMCM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMDM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMEM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMFM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMGM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMHM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMIM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMJM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMKM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMLM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMMM']}
2025-08-02 22:31:00,203 - INFO - 批量插入表单数据成功，批次 2，共 26 条记录
2025-08-02 22:31:00,203 - INFO - 成功插入的数据ID: ['FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMXL', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMYL', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMZL', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM0M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM1M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM2M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM3M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM4M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM5M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM6M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM7M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM8M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDM9M', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMAM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMBM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMCM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMDM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMEM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMFM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMGM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMHM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMIM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMJM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMKM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMLM', 'FINST-8LC66GC1AAMXXKIZAQXQK9EUJDMG34SFOCUDMMM']
2025-08-02 22:31:05,218 - INFO - 批量插入完成，共 76 条记录
2025-08-02 22:31:05,218 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 76 条，错误: 0 条
2025-08-02 22:31:05,218 - INFO - 数据同步完成！更新: 0 条，插入: 76 条，错误: 1 条
2025-08-02 22:32:05,223 - INFO - 开始同步昨天与今天的销售数据: 2025-08-01 至 2025-08-02
2025-08-02 22:32:05,223 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-02 22:32:05,223 - INFO - 查询参数: ('2025-08-01', '2025-08-02')
2025-08-02 22:32:05,395 - INFO - MySQL查询成功，时间段: 2025-08-01 至 2025-08-02，共获取 586 条记录
2025-08-02 22:32:05,395 - INFO - 获取到 2 个日期需要处理: ['2025-08-01', '2025-08-02']
2025-08-02 22:32:05,395 - INFO - 开始处理日期: 2025-08-01
2025-08-02 22:32:05,395 - INFO - Request Parameters - Page 1:
2025-08-02 22:32:05,395 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:05,395 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:06,145 - INFO - Response - Page 1:
2025-08-02 22:32:06,145 - INFO - 第 1 页获取到 50 条记录
2025-08-02 22:32:06,645 - INFO - Request Parameters - Page 2:
2025-08-02 22:32:06,645 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:06,645 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:07,410 - INFO - Response - Page 2:
2025-08-02 22:32:07,410 - INFO - 第 2 页获取到 50 条记录
2025-08-02 22:32:07,926 - INFO - Request Parameters - Page 3:
2025-08-02 22:32:07,926 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:07,926 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:08,801 - INFO - Response - Page 3:
2025-08-02 22:32:08,801 - INFO - 第 3 页获取到 50 条记录
2025-08-02 22:32:09,316 - INFO - Request Parameters - Page 4:
2025-08-02 22:32:09,316 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:09,316 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:10,019 - INFO - Response - Page 4:
2025-08-02 22:32:10,019 - INFO - 第 4 页获取到 50 条记录
2025-08-02 22:32:10,535 - INFO - Request Parameters - Page 5:
2025-08-02 22:32:10,535 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:10,535 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:11,222 - INFO - Response - Page 5:
2025-08-02 22:32:11,222 - INFO - 第 5 页获取到 50 条记录
2025-08-02 22:32:11,738 - INFO - Request Parameters - Page 6:
2025-08-02 22:32:11,738 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:11,738 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:12,425 - INFO - Response - Page 6:
2025-08-02 22:32:12,425 - INFO - 第 6 页获取到 50 条记录
2025-08-02 22:32:12,941 - INFO - Request Parameters - Page 7:
2025-08-02 22:32:12,941 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:12,941 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:13,628 - INFO - Response - Page 7:
2025-08-02 22:32:13,628 - INFO - 第 7 页获取到 50 条记录
2025-08-02 22:32:14,128 - INFO - Request Parameters - Page 8:
2025-08-02 22:32:14,128 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:14,128 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:14,847 - INFO - Response - Page 8:
2025-08-02 22:32:14,847 - INFO - 第 8 页获取到 50 条记录
2025-08-02 22:32:15,363 - INFO - Request Parameters - Page 9:
2025-08-02 22:32:15,363 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:15,363 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:16,112 - INFO - Response - Page 9:
2025-08-02 22:32:16,112 - INFO - 第 9 页获取到 48 条记录
2025-08-02 22:32:16,628 - INFO - 查询完成，共获取到 448 条记录
2025-08-02 22:32:16,628 - INFO - 获取到 448 条表单数据
2025-08-02 22:32:16,628 - INFO - 当前日期 2025-08-01 有 486 条MySQL数据需要处理
2025-08-02 22:32:16,644 - INFO - 开始批量插入 38 条新记录
2025-08-02 22:32:16,925 - INFO - 批量插入响应状态码: 200
2025-08-02 22:32:16,925 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 14:32:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1836', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B2D3763D-9E4F-7734-9962-50E6E4B3BF11', 'x-acs-trace-id': 'fb3eb6248df1b45e776d0316a45243b3', 'etag': '1iurjN2V8aDvHWJCg8NKVLQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-02 22:32:16,925 - INFO - 批量插入响应体: {'result': ['FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM9F', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMAF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMBF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMCF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMDF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMEF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMFF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMGF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMHF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMIF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMJF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMKF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMLF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMMF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMNF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMOF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMPF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMQF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMRF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMSF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMTF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMUF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMVF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMWF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMXF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMYF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMZF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM0G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM1G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM2G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM3G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM4G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM5G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM6G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM7G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM8G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM9G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMAG']}
2025-08-02 22:32:16,925 - INFO - 批量插入表单数据成功，批次 1，共 38 条记录
2025-08-02 22:32:16,925 - INFO - 成功插入的数据ID: ['FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM9F', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMAF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMBF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMCF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMDF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMEF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMFF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMGF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMHF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMIF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMJF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMKF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMLF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMMF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMNF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMOF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMPF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMQF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMRF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMSF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMTF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMUF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMVF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMWF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMXF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMYF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMZF', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM0G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM1G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM2G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM3G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM4G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM5G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM6G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM7G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM8G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDM9G', 'FINST-OY8665C1XINX8HJS8G08QB0J23IP2QZ2QCUDMAG']
2025-08-02 22:32:21,940 - INFO - 批量插入完成，共 38 条记录
2025-08-02 22:32:21,940 - INFO - 日期 2025-08-01 处理完成 - 更新: 0 条，插入: 38 条，错误: 0 条
2025-08-02 22:32:21,940 - INFO - 开始处理日期: 2025-08-02
2025-08-02 22:32:21,940 - INFO - Request Parameters - Page 1:
2025-08-02 22:32:21,940 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:21,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:22,737 - INFO - Response - Page 1:
2025-08-02 22:32:22,737 - INFO - 第 1 页获取到 50 条记录
2025-08-02 22:32:23,237 - INFO - Request Parameters - Page 2:
2025-08-02 22:32:23,237 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-02 22:32:23,237 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-02 22:32:23,924 - INFO - Response - Page 2:
2025-08-02 22:32:23,924 - INFO - 第 2 页获取到 27 条记录
2025-08-02 22:32:24,424 - INFO - 查询完成，共获取到 77 条记录
2025-08-02 22:32:24,424 - INFO - 获取到 77 条表单数据
2025-08-02 22:32:24,424 - INFO - 当前日期 2025-08-02 有 77 条MySQL数据需要处理
2025-08-02 22:32:24,424 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMQD
2025-08-02 22:32:24,971 - INFO - 更新表单数据成功: FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX52AQBOCUDMQD
2025-08-02 22:32:24,971 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 1118.0, 'new_value': 11118.0}, {'field': 'total_amount', 'old_value': 1118.0, 'new_value': 11118.0}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-02 22:32:24,971 - INFO - 日期 2025-08-02 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-02 22:32:24,971 - INFO - 数据同步完成！更新: 1 条，插入: 38 条，错误: 0 条
2025-08-02 22:32:24,971 - INFO - 同步完成
