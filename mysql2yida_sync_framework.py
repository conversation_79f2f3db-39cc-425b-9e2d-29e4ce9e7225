# -*- coding: utf-8 -*-
"""
MySQL与宜搭数据同步通用框架
支持新增、更新场景的数据同步
"""
import os
import sys
import logging
import pymysql
import json
import time
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
import argparse
from time import sleep

# 设置日志级别为 WARNING，这样就不会显示 DEBUG 信息
logging.getLogger("alibabacloud_credentials").setLevel(logging.WARNING)

from alibabacloud_dingtalk.yida_2_0.client import Client as DingTalkYidaClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_dingtalk.yida_2_0 import models as yida_models
from alibabacloud_tea_util import models as util_models
from alibabacloud_tea_util.client import Client as UtilClient
from get_token import token
from alibabacloud_dingtalk.yida_1_0 import models as dingtalkyida__1__0_models
from alibabacloud_dingtalk.yida_1_0.client import Client as dingtalkyida_1_0Client


class SyncConfig:
    """同步配置类"""
    
    def __init__(self, config_file: str = None):
        """
        初始化同步配置
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        if config_file and os.path.exists(config_file):
            self.load_from_file(config_file)
        else:
            self.load_default_config()
    
    def load_default_config(self):
        """加载默认配置"""
        # 数据库配置
        self.db_config = {
            'host': '**************',
            'port': 3306,
            'user': 'c_hxp_ro_prod',
            'password': 'xm9P06O7ezGi6PZt',
            'database': 'yx_business',
            'charset': 'utf8mb4',
            'cursorclass': 'pymysql.cursors.DictCursor'
        }
        
        # 宜搭配置
        self.yida_config = {
            'APP_TYPE': 'APP_D7E6ZB94ZUL5Q1GUAOLD',
            'SYSTEM_TOKEN': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2',
            'USER_ID': 'hexuepeng',
            'LANGUAGE': 'zh_CN',
            'FORM_UUID': 'FORM-FB065F60E7C04670B0D6A745813391DAMRO5'
        }
        
        # SQL查询语句
        self.sql_query = """
            SELECT 
                a.project_code, b.name as project_name,
                a.store_code, c.name as store_name,
                DATE_FORMAT(a.sales_time, '%Y-%m') as sales_date,
                SUM(a.online_amount) as online_amount,
                SUM(a.offline_amount) as offline_amount,
                SUM(a.total_amount) as total_amount,
                SUM(order_count) as order_count
            FROM yx_b_sales_record a
            JOIN yx_b_projects b ON a.project_code = b.code
            JOIN yx_b_tenants c ON a.store_code = c.code
            WHERE a.status = 2 
                AND a.deleted = 0
                AND c.deleted = 0
                and sales_time>='2025-01-01'
            GROUP BY a.project_code, b.name, a.store_code, c.name, 
                     DATE_FORMAT(a.sales_time, '%Y-%m');
        """
        
        # 字段映射：MySQL字段名 -> 宜搭字段ID
        self.field_mapping = {
            'project_code': 'textField_m9tojheo',
            'project_name': 'textField_m9tojhep', 
            'store_code': 'textField_m9tojheq',
            'store_name': 'textField_m9tojher',
            'sales_date': 'dateField_m9tojheu',
            'online_amount': 'numberField_m9tojhev',
            'offline_amount': 'numberField_m9tojhew', 
            'total_amount': 'numberField_m9tojhex',
            'order_count': 'numberField_m9tojhey'
        }
        
        # 数据对比时的键字段（用于判断是否为同一条记录）
        self.key_fields = ['project_code', 'store_code', 'sales_date']
        
        # 需要比较更新的字段
        self.compare_fields = [
            'online_amount',    # 线上销售额
            'offline_amount',   # 线下销售额
            'total_amount',     # 总销售额
            'order_count'       # 订单数量
        ]
        
        # 日志配置
        self.log_config = {
            'level': logging.INFO,
            'format': '%(asctime)s - %(levelname)s - %(message)s',
            'encoding': 'utf-8',
            'filename_prefix': 'mysql2yida_sync'
        }
        
        # 批处理配置
        self.batch_config = {
            'batch_size': 50,  # 批量处理大小
            'delay_seconds': 1  # 批次间延时秒数
        }
    
    def load_from_file(self, config_file: str):
        """从配置文件加载配置"""
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            
            self.db_config = config_data.get('db_config', {})
            self.yida_config = config_data.get('yida_config', {})
            self.sql_query = config_data.get('sql_query', '')
            self.field_mapping = config_data.get('field_mapping', {})
            self.key_fields = config_data.get('key_fields', [])
            self.compare_fields = config_data.get('compare_fields', [])
            self.log_config = config_data.get('log_config', {})
            self.batch_config = config_data.get('batch_config', {})
            
        except Exception as e:
            logging.error(f"加载配置文件失败: {str(e)}")
            self.load_default_config()
    
    def save_to_file(self, config_file: str):
        """保存配置到文件"""
        config_data = {
            'db_config': self.db_config,
            'yida_config': self.yida_config,
            'sql_query': self.sql_query,
            'field_mapping': self.field_mapping,
            'key_fields': self.key_fields,
            'compare_fields': self.compare_fields,
            'log_config': self.log_config,
            'batch_config': self.batch_config
        }
        
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)
            logging.info(f"配置已保存到文件: {config_file}")
        except Exception as e:
            logging.error(f"保存配置文件失败: {str(e)}")


class YidaFormDataClient:
    """宜搭表单数据客户端"""
    
    def __init__(self, yida_config: Dict):
        self.yida_config = yida_config
        self.access_token = token.get_token()
        self.client = self._create_client()

    def _create_client(self) -> dingtalkyida_1_0Client:
        """初始化宜搭客户端"""
        config = open_api_models.Config(
            protocol='https',
            region_id='central'
        )
        return dingtalkyida_1_0Client(config)

    def get_form_data(self, page_size: int = 100, search_condition: Optional[List[Dict]] = None) -> List[Dict[str, Any]]:
        """获取宜搭表单数据"""
        try:
            all_data = []
            current_page = 1
            
            while True:
                headers = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldHeaders()
                headers.x_acs_dingtalk_access_token = self.access_token
                
                request = dingtalkyida__1__0_models.SearchFormDataSecondGenerationNoTableFieldRequest(
                    page_number=current_page,
                    page_size=page_size,
                    form_uuid=self.yida_config['FORM_UUID'],
                    search_condition=json.dumps(search_condition) if search_condition else None,
                    system_token=self.yida_config['SYSTEM_TOKEN'],
                    user_id=self.yida_config['USER_ID'],
                    app_type=self.yida_config['APP_TYPE']
                )

                response = self.client.search_form_data_second_generation_no_table_field_with_options(request, headers, util_models.RuntimeOptions())

                if response.body and response.body.data:
                    # 提取指定字段
                    for item in response.body.data:
                        filtered_data = {
                            'formInstanceId': item.form_instance_id,
                            'formData': item.form_data
                        }
                        all_data.append(filtered_data)

                    if len(response.body.data) < page_size:
                        break
                    current_page += 1
                else:
                    break
                    
            logging.info(f"成功获取宜搭表单数据，共 {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            logging.error(f"获取宜搭表单数据失败: {str(e)}")
            return []

    def update_form_data(self, form_instance_id: str, form_data: Dict) -> bool:
        """更新宜搭表单数据"""
        try:
            headers = dingtalkyida__1__0_models.UpdateFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token

            request = dingtalkyida__1__0_models.UpdateFormDataRequest(
                app_type=self.yida_config['APP_TYPE'],
                system_token=self.yida_config['SYSTEM_TOKEN'],
                user_id=self.yida_config['USER_ID'],
                language=self.yida_config['LANGUAGE'],
                form_instance_id=form_instance_id,
                update_form_data_json=json.dumps(form_data, ensure_ascii=False)
            )

            response = self.client.update_form_data_with_options(request, headers, util_models.RuntimeOptions())

            if response.status_code == 200:
                logging.info(f"成功更新表单数据，实例ID: {form_instance_id}")
                return True
            else:
                logging.error(f"更新表单数据失败，状态码: {response.status_code}")
                return False

        except Exception as e:
            logging.error(f"更新表单数据失败: {str(e)}")
            return False

    def batch_create_form_data(self, form_data_list: List[Dict], batch_size: int = 50) -> bool:
        """批量创建宜搭表单数据"""
        try:
            headers = dingtalkyida__1__0_models.BatchSaveFormDataHeaders()
            headers.x_acs_dingtalk_access_token = self.access_token

            total_count = len(form_data_list)
            success_count = 0

            for i in range(0, total_count, batch_size):
                batch_data = form_data_list[i:i + batch_size]
                form_data_json_list = [json.dumps(item, ensure_ascii=False) for item in batch_data]

                request = dingtalkyida__1__0_models.BatchSaveFormDataRequest(
                    no_execute_expression=True,
                    form_uuid=self.yida_config['FORM_UUID'],
                    app_type=self.yida_config['APP_TYPE'],
                    asynchronous_execution=True,
                    system_token=self.yida_config['SYSTEM_TOKEN'],
                    keep_running_after_exception=True,
                    user_id=self.yida_config['USER_ID'],
                    form_data_json_list=form_data_json_list
                )

                response = self.client.batch_save_form_data_with_options(request, headers, util_models.RuntimeOptions())

                if response.status_code == 200:
                    success_count += len(batch_data)
                    logging.info(f"批量插入成功，批次 {i//batch_size + 1}，本批次 {len(batch_data)} 条")
                else:
                    logging.error(f"批量插入失败，批次 {i//batch_size + 1}，状态码: {response.status_code}")

                # 添加延时避免请求过于频繁
                time.sleep(1)

            logging.info(f"批量插入完成，总计 {success_count}/{total_count} 条记录成功")
            return success_count == total_count

        except Exception as e:
            logging.error(f"批量插入表单数据失败: {str(e)}")
            return False


class MySQL2YidaSyncClient:
    """MySQL与宜搭数据同步客户端"""

    def __init__(self, config: SyncConfig):
        self.config = config
        self._setup_logging()
        self._init_mysql_connection()
        self._init_yida_client()

    def _setup_logging(self):
        """设置日志配置"""
        current_date = datetime.now().strftime('%Y%m%d')
        log_filename = f"logs/{self.config.log_config['filename_prefix']}_log_{current_date}.txt"

        # 确保logs目录存在
        os.makedirs('logs', exist_ok=True)

        logging.basicConfig(
            filename=log_filename,
            level=self.config.log_config['level'],
            format=self.config.log_config['format'],
            encoding=self.config.log_config['encoding']
        )

        # 同时输出到控制台
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(self.config.log_config['format'])
        console_handler.setFormatter(formatter)
        logging.getLogger().addHandler(console_handler)

        logging.info("MySQL与宜搭数据同步客户端初始化完成")

    def _init_mysql_connection(self):
        """初始化MySQL连接"""
        try:
            # 处理cursorclass配置
            db_config = self.config.db_config.copy()
            if 'cursorclass' in db_config:
                if isinstance(db_config['cursorclass'], str):
                    # 如果是字符串，转换为实际的类
                    if db_config['cursorclass'] == 'pymysql.cursors.DictCursor':
                        db_config['cursorclass'] = pymysql.cursors.DictCursor
                    else:
                        # 移除无效的cursorclass配置
                        del db_config['cursorclass']
            else:
                # 默认使用DictCursor
                db_config['cursorclass'] = pymysql.cursors.DictCursor

            self.mysql_conn = pymysql.connect(**db_config)
            logging.info("MySQL数据库连接成功")
        except Exception as e:
            logging.error(f"MySQL数据库连接失败: {str(e)}")
            raise

    def _init_yida_client(self):
        """初始化宜搭客户端"""
        self.yida_client = YidaFormDataClient(self.config.yida_config)

    def get_mysql_data(self) -> List[Dict]:
        """从MySQL获取数据"""
        try:
            cursor = self.mysql_conn.cursor()
            cursor.execute(self.config.sql_query)
            result = cursor.fetchall()
            logging.info(f"MySQL查询成功，共获取 {len(result)} 条记录")
            return result
        except Exception as e:
            logging.error(f"MySQL查询失败: {str(e)}")
            return []

    def get_yida_data(self, search_condition: Optional[List[Dict]] = None) -> List[Dict]:
        """从宜搭获取数据"""
        try:
            yida_data = self.yida_client.get_form_data(search_condition=search_condition)
            logging.info(f"获取到 {len(yida_data)} 条宜搭表单数据")
            return yida_data
        except Exception as e:
            logging.error(f"获取宜搭数据失败: {str(e)}")
            return []

    def _generate_key(self, data: Dict, is_mysql_data: bool = True) -> str:
        """生成数据记录的唯一键"""
        key_parts = []
        for field in self.config.key_fields:
            if is_mysql_data:
                # MySQL数据直接使用字段名
                value = str(data.get(field, ''))
            else:
                # 宜搭数据需要通过字段映射获取
                yida_field = self.config.field_mapping.get(field)
                if yida_field:
                    form_data = data.get('formData', {})
                    value = str(form_data.get(yida_field, ''))
                else:
                    value = ''
            key_parts.append(value)
        return '_'.join(key_parts)

    def _convert_mysql_to_yida_format(self, mysql_data: Dict) -> Dict:
        """将MySQL数据转换为宜搭表单格式"""
        yida_data = {}
        for mysql_field, yida_field in self.config.field_mapping.items():
            if mysql_field in mysql_data:
                value = mysql_data[mysql_field]

                # 处理日期字段
                if 'date' in mysql_field.lower() and isinstance(value, str):
                    try:
                        # 将日期字符串转换为时间戳（毫秒）
                        if len(value) == 7:  # YYYY-MM格式
                            value = value + '-01'  # 转换为YYYY-MM-DD
                        dt = datetime.strptime(value, '%Y-%m-%d')
                        value = int(dt.timestamp() * 1000)
                    except:
                        pass

                yida_data[yida_field] = value

        return yida_data

    def _is_data_different(self, mysql_data: Dict, yida_data: Dict) -> bool:
        """比较MySQL数据和宜搭数据是否有差异"""
        form_data = yida_data.get('formData', {})

        for field in self.config.compare_fields:
            yida_field = self.config.field_mapping.get(field)
            if not yida_field:
                continue

            mysql_value = mysql_data.get(field)
            yida_value = form_data.get(yida_field)

            # 数值类型比较
            if isinstance(mysql_value, (int, float)) and isinstance(yida_value, (int, float)):
                if abs(mysql_value - yida_value) > 0.01:  # 允许小数点误差
                    logging.debug(f"字段 {field} 数值不同: MySQL={mysql_value}, 宜搭={yida_value}")
                    return True
            # 字符串类型比较
            elif str(mysql_value) != str(yida_value):
                logging.debug(f"字段 {field} 值不同: MySQL={mysql_value}, 宜搭={yida_value}")
                return True

        return False

    def sync_data(self, search_condition: Optional[List[Dict]] = None):
        """执行数据同步"""
        try:
            logging.info("开始数据同步流程...")

            # 1. 获取MySQL数据
            logging.info("正在获取MySQL数据...")
            mysql_data_list = self.get_mysql_data()
            if not mysql_data_list:
                logging.error("未获取到MySQL数据，同步流程终止")
                return

            # 2. 获取宜搭数据
            logging.info("正在获取宜搭数据...")
            yida_data_list = self.get_yida_data(search_condition)

            # 3. 创建宜搭数据索引
            yida_data_dict = {}
            for item in yida_data_list:
                key = self._generate_key(item, is_mysql_data=False)
                yida_data_dict[key] = item

            # 4. 比较数据并分类
            to_create = []  # 需要新增的数据
            to_update = []  # 需要更新的数据

            for mysql_data in mysql_data_list:
                key = self._generate_key(mysql_data, is_mysql_data=True)
                yida_form_data = self._convert_mysql_to_yida_format(mysql_data)

                if key in yida_data_dict:
                    # 数据已存在，检查是否需要更新
                    yida_data = yida_data_dict[key]
                    if self._is_data_different(mysql_data, yida_data):
                        to_update.append({
                            'form_instance_id': yida_data['formInstanceId'],
                            'form_data': yida_form_data
                        })
                        logging.debug(f"标记更新: {key}")
                else:
                    # 数据不存在，需要新增
                    to_create.append(yida_form_data)
                    logging.debug(f"标记新增: {key}")

            # 5. 执行更新操作
            if to_update:
                logging.info(f"开始更新 {len(to_update)} 条记录...")
                update_success_count = 0
                for update_item in to_update:
                    if self.yida_client.update_form_data(
                        update_item['form_instance_id'],
                        update_item['form_data']
                    ):
                        update_success_count += 1
                    time.sleep(0.5)  # 避免请求过于频繁

                logging.info(f"更新完成，成功 {update_success_count}/{len(to_update)} 条记录")

            # 6. 执行新增操作
            if to_create:
                logging.info(f"开始新增 {len(to_create)} 条记录...")
                if self.yida_client.batch_create_form_data(
                    to_create,
                    self.config.batch_config['batch_size']
                ):
                    logging.info("新增操作完成")
                else:
                    logging.error("新增操作失败")

            logging.info("数据同步流程完成")

        except Exception as e:
            logging.error(f"数据同步失败: {str(e)}")
            raise
        finally:
            if hasattr(self, 'mysql_conn'):
                self.mysql_conn.close()
                logging.info("MySQL连接已关闭")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MySQL与宜搭数据同步工具')
    parser.add_argument('--config', '-c', type=str, help='配置文件路径')
    parser.add_argument('--create-config', type=str, help='创建示例配置文件')

    args = parser.parse_args()

    # 创建示例配置文件
    if args.create_config:
        config = SyncConfig()
        config.save_to_file(args.create_config)
        print(f"示例配置文件已创建: {args.create_config}")
        return

    try:
        # 加载配置
        config = SyncConfig(args.config)

        # 创建同步客户端并执行同步
        sync_client = MySQL2YidaSyncClient(config)
        sync_client.sync_data()

    except Exception as e:
        print(f"同步失败: {str(e)}")
        logging.error(f"同步失败: {str(e)}")


if __name__ == "__main__":
    main()
