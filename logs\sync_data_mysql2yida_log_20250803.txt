2025-08-03 01:30:33,344 - INFO - 使用默认增量同步（当天更新数据）
2025-08-03 01:30:33,344 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-03 01:30:33,344 - INFO - 查询参数: ('2025-08-03',)
2025-08-03 01:30:33,438 - INFO - MySQL查询成功，增量数据（日期: 2025-08-03），共获取 0 条记录
2025-08-03 01:30:33,438 - ERROR - 未获取到MySQL数据
2025-08-03 01:31:33,445 - INFO - 开始同步昨天与今天的销售数据: 2025-08-02 至 2025-08-03
2025-08-03 01:31:33,445 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-03 01:31:33,445 - INFO - 查询参数: ('2025-08-02', '2025-08-03')
2025-08-03 01:31:33,601 - INFO - MySQL查询成功，时间段: 2025-08-02 至 2025-08-03，共获取 147 条记录
2025-08-03 01:31:33,601 - INFO - 获取到 1 个日期需要处理: ['2025-08-02']
2025-08-03 01:31:33,601 - INFO - 开始处理日期: 2025-08-02
2025-08-03 01:31:33,601 - INFO - Request Parameters - Page 1:
2025-08-03 01:31:33,601 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 01:31:33,601 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 01:31:41,737 - ERROR - 处理日期 2025-08-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ABD50CAD-F1B2-7E9F-985A-078447FD494F Response: {'code': 'ServiceUnavailable', 'requestid': 'ABD50CAD-F1B2-7E9F-985A-078447FD494F', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ABD50CAD-F1B2-7E9F-985A-078447FD494F)
2025-08-03 01:31:41,737 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-03 01:31:41,737 - INFO - 同步完成
2025-08-03 04:30:33,336 - INFO - 使用默认增量同步（当天更新数据）
2025-08-03 04:30:33,336 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-03 04:30:33,336 - INFO - 查询参数: ('2025-08-03',)
2025-08-03 04:30:33,492 - INFO - MySQL查询成功，增量数据（日期: 2025-08-03），共获取 8 条记录
2025-08-03 04:30:33,492 - INFO - 获取到 1 个日期需要处理: ['2025-08-02']
2025-08-03 04:30:33,492 - INFO - 开始处理日期: 2025-08-02
2025-08-03 04:30:33,492 - INFO - Request Parameters - Page 1:
2025-08-03 04:30:33,492 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 04:30:33,492 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 04:30:41,612 - ERROR - 处理日期 2025-08-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F5294CA4-2041-760B-8D10-28AF7118B7D6 Response: {'code': 'ServiceUnavailable', 'requestid': 'F5294CA4-2041-760B-8D10-28AF7118B7D6', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F5294CA4-2041-760B-8D10-28AF7118B7D6)
2025-08-03 04:30:41,612 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-03 04:31:41,619 - INFO - 开始同步昨天与今天的销售数据: 2025-08-02 至 2025-08-03
2025-08-03 04:31:41,619 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-03 04:31:41,619 - INFO - 查询参数: ('2025-08-02', '2025-08-03')
2025-08-03 04:31:41,775 - INFO - MySQL查询成功，时间段: 2025-08-02 至 2025-08-03，共获取 156 条记录
2025-08-03 04:31:41,775 - INFO - 获取到 1 个日期需要处理: ['2025-08-02']
2025-08-03 04:31:41,775 - INFO - 开始处理日期: 2025-08-02
2025-08-03 04:31:41,775 - INFO - Request Parameters - Page 1:
2025-08-03 04:31:41,775 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 04:31:41,775 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 04:31:49,618 - INFO - Response - Page 1:
2025-08-03 04:31:49,618 - INFO - 第 1 页获取到 50 条记录
2025-08-03 04:31:50,118 - INFO - Request Parameters - Page 2:
2025-08-03 04:31:50,118 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 04:31:50,118 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 04:31:50,962 - INFO - Response - Page 2:
2025-08-03 04:31:50,962 - INFO - 第 2 页获取到 27 条记录
2025-08-03 04:31:51,462 - INFO - 查询完成，共获取到 77 条记录
2025-08-03 04:31:51,462 - INFO - 获取到 77 条表单数据
2025-08-03 04:31:51,462 - INFO - 当前日期 2025-08-02 有 150 条MySQL数据需要处理
2025-08-03 04:31:51,462 - INFO - 开始批量插入 73 条新记录
2025-08-03 04:31:51,712 - INFO - 批量插入响应状态码: 200
2025-08-03 04:31:51,712 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 20:31:52 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DBE34565-2074-7D4D-818E-028369CBFD6A', 'x-acs-trace-id': '730134d7b57bd8d391960ae5e183bd91', 'etag': '2SrnrQDP6mAWd1+Hm08JF1g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 04:31:51,712 - INFO - 批量插入响应体: {'result': ['FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMM7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMN7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMO7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMP7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMQ7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMR7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMS7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMT7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMU7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMV7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMW7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMX7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMY7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMZ7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM08', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM18', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM28', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM38', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM48', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM58', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM68', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM78', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM88', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM98', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMA8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMB8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMC8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMD8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDME8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMF8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMG8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMH8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMI8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMJ8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMK8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDML8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMM8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMN8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMO8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMP8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMQ8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMR8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMS8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMT8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMU8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMV8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMW8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMX8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMY8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMZ8']}
2025-08-03 04:31:51,712 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-03 04:31:51,712 - INFO - 成功插入的数据ID: ['FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMM7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMN7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMO7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMP7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMQ7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMR7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMS7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMT7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMU7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMV7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMW7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMX7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMY7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMZ7', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM08', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM18', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM28', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM38', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM48', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM58', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM68', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM78', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM88', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDM98', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMA8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMB8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMC8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMD8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDME8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMF8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMG8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMH8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMI8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMJ8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMK8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDML8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMM8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMN8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMO8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMP8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMQ8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMR8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMS8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMT8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMU8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMV8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMW8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMX8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMY8', 'FINST-90E66JD1QONXTUC59PIG16G2CLG32E7IKPUDMZ8']
2025-08-03 04:31:56,930 - INFO - 批量插入响应状态码: 200
2025-08-03 04:31:56,930 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sat, 02 Aug 2025 20:31:58 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1116', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'E5E79543-A734-73E8-ACF0-33320F42A107', 'x-acs-trace-id': '5f8f0697e63087d988eb4c641a7c8882', 'etag': '1vwx0hWhugns/3MnQq10ccA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 04:31:56,930 - INFO - 批量插入响应体: {'result': ['FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMNE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMOE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMPE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMQE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMRE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMSE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMTE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMUE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMVE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMWE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMXE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMYE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMZE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDM0F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDM1F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM2F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM3F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM4F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM5F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM6F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM7F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM8F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM9F']}
2025-08-03 04:31:56,930 - INFO - 批量插入表单数据成功，批次 2，共 23 条记录
2025-08-03 04:31:56,930 - INFO - 成功插入的数据ID: ['FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMNE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMOE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMPE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMQE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMRE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMSE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMTE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMUE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMVE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMWE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMXE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMYE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDMZE', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDM0F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3I8MKPUDM1F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM2F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM3F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM4F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM5F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM6F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM7F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM8F', 'FINST-VEC667D1B9LXWTMQBHY0FBB86JQH3J8MKPUDM9F']
2025-08-03 04:32:01,945 - INFO - 批量插入完成，共 73 条记录
2025-08-03 04:32:01,945 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 73 条，错误: 0 条
2025-08-03 04:32:01,945 - INFO - 数据同步完成！更新: 0 条，插入: 73 条，错误: 0 条
2025-08-03 04:32:01,945 - INFO - 同步完成
2025-08-03 07:30:33,548 - INFO - 使用默认增量同步（当天更新数据）
2025-08-03 07:30:33,548 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-03 07:30:33,548 - INFO - 查询参数: ('2025-08-03',)
2025-08-03 07:30:33,720 - INFO - MySQL查询成功，增量数据（日期: 2025-08-03），共获取 8 条记录
2025-08-03 07:30:33,720 - INFO - 获取到 1 个日期需要处理: ['2025-08-02']
2025-08-03 07:30:33,720 - INFO - 开始处理日期: 2025-08-02
2025-08-03 07:30:33,720 - INFO - Request Parameters - Page 1:
2025-08-03 07:30:33,720 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 07:30:33,720 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 07:30:41,829 - ERROR - 处理日期 2025-08-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 41101CF7-088D-7A0D-8EB7-E6A0152C6D73 Response: {'code': 'ServiceUnavailable', 'requestid': '41101CF7-088D-7A0D-8EB7-E6A0152C6D73', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 41101CF7-088D-7A0D-8EB7-E6A0152C6D73)
2025-08-03 07:30:41,829 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-03 07:31:41,839 - INFO - 开始同步昨天与今天的销售数据: 2025-08-02 至 2025-08-03
2025-08-03 07:31:41,839 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-03 07:31:41,839 - INFO - 查询参数: ('2025-08-02', '2025-08-03')
2025-08-03 07:31:41,996 - INFO - MySQL查询成功，时间段: 2025-08-02 至 2025-08-03，共获取 156 条记录
2025-08-03 07:31:41,996 - INFO - 获取到 1 个日期需要处理: ['2025-08-02']
2025-08-03 07:31:41,996 - INFO - 开始处理日期: 2025-08-02
2025-08-03 07:31:41,996 - INFO - Request Parameters - Page 1:
2025-08-03 07:31:41,996 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 07:31:41,996 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 07:31:42,746 - INFO - Response - Page 1:
2025-08-03 07:31:42,746 - INFO - 第 1 页获取到 50 条记录
2025-08-03 07:31:43,261 - INFO - Request Parameters - Page 2:
2025-08-03 07:31:43,261 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 07:31:43,261 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 07:31:44,011 - INFO - Response - Page 2:
2025-08-03 07:31:44,011 - INFO - 第 2 页获取到 50 条记录
2025-08-03 07:31:44,527 - INFO - Request Parameters - Page 3:
2025-08-03 07:31:44,527 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 07:31:44,527 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 07:31:45,246 - INFO - Response - Page 3:
2025-08-03 07:31:45,246 - INFO - 第 3 页获取到 50 条记录
2025-08-03 07:31:45,761 - INFO - Request Parameters - Page 4:
2025-08-03 07:31:45,761 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 07:31:45,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 07:31:46,230 - INFO - Response - Page 4:
2025-08-03 07:31:46,230 - INFO - 查询完成，共获取到 150 条记录
2025-08-03 07:31:46,230 - INFO - 获取到 150 条表单数据
2025-08-03 07:31:46,230 - INFO - 当前日期 2025-08-02 有 150 条MySQL数据需要处理
2025-08-03 07:31:46,230 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 07:31:46,230 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 07:31:46,230 - INFO - 同步完成
2025-08-03 10:30:33,500 - INFO - 使用默认增量同步（当天更新数据）
2025-08-03 10:30:33,500 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-03 10:30:33,500 - INFO - 查询参数: ('2025-08-03',)
2025-08-03 10:30:33,672 - INFO - MySQL查询成功，增量数据（日期: 2025-08-03），共获取 98 条记录
2025-08-03 10:30:33,672 - INFO - 获取到 2 个日期需要处理: ['2025-08-02', '2025-08-03']
2025-08-03 10:30:33,672 - INFO - 开始处理日期: 2025-08-02
2025-08-03 10:30:33,672 - INFO - Request Parameters - Page 1:
2025-08-03 10:30:33,672 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 10:30:33,672 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 10:30:41,812 - ERROR - 处理日期 2025-08-02 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F977BCF7-43EE-7AD8-87EA-4E1191ADF521 Response: {'code': 'ServiceUnavailable', 'requestid': 'F977BCF7-43EE-7AD8-87EA-4E1191ADF521', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F977BCF7-43EE-7AD8-87EA-4E1191ADF521)
2025-08-03 10:30:41,812 - INFO - 开始处理日期: 2025-08-03
2025-08-03 10:30:41,812 - INFO - Request Parameters - Page 1:
2025-08-03 10:30:41,812 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 10:30:41,812 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 10:30:42,281 - INFO - Response - Page 1:
2025-08-03 10:30:42,281 - INFO - 查询完成，共获取到 0 条记录
2025-08-03 10:30:42,281 - INFO - 获取到 0 条表单数据
2025-08-03 10:30:42,281 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-03 10:30:42,281 - INFO - 开始批量插入 1 条新记录
2025-08-03 10:30:42,437 - INFO - 批量插入响应状态码: 200
2025-08-03 10:30:42,437 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 02:30:41 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '7DF20DA7-6C3E-7F26-82BA-83BE0000977D', 'x-acs-trace-id': '43f9568e0c9fae1f48d772448f7b8bfc', 'etag': '688wb4qj5w8s2vh2RVAg2Vw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 10:30:42,437 - INFO - 批量插入响应体: {'result': ['FINST-2ZE66W719HNXP2F6DNXEB8QP05TH2WNXD2VDM11']}
2025-08-03 10:30:42,437 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-03 10:30:42,437 - INFO - 成功插入的数据ID: ['FINST-2ZE66W719HNXP2F6DNXEB8QP05TH2WNXD2VDM11']
2025-08-03 10:30:47,453 - INFO - 批量插入完成，共 1 条记录
2025-08-03 10:30:47,453 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-03 10:30:47,453 - INFO - 数据同步完成！更新: 0 条，插入: 1 条，错误: 1 条
2025-08-03 10:31:47,463 - INFO - 开始同步昨天与今天的销售数据: 2025-08-02 至 2025-08-03
2025-08-03 10:31:47,463 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-03 10:31:47,463 - INFO - 查询参数: ('2025-08-02', '2025-08-03')
2025-08-03 10:31:47,619 - INFO - MySQL查询成功，时间段: 2025-08-02 至 2025-08-03，共获取 423 条记录
2025-08-03 10:31:47,619 - INFO - 获取到 2 个日期需要处理: ['2025-08-02', '2025-08-03']
2025-08-03 10:31:47,635 - INFO - 开始处理日期: 2025-08-02
2025-08-03 10:31:47,635 - INFO - Request Parameters - Page 1:
2025-08-03 10:31:47,635 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 10:31:47,635 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 10:31:48,479 - INFO - Response - Page 1:
2025-08-03 10:31:48,479 - INFO - 第 1 页获取到 50 条记录
2025-08-03 10:31:48,994 - INFO - Request Parameters - Page 2:
2025-08-03 10:31:48,994 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 10:31:48,994 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 10:31:49,791 - INFO - Response - Page 2:
2025-08-03 10:31:49,791 - INFO - 第 2 页获取到 50 条记录
2025-08-03 10:31:50,307 - INFO - Request Parameters - Page 3:
2025-08-03 10:31:50,307 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 10:31:50,307 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 10:31:51,010 - INFO - Response - Page 3:
2025-08-03 10:31:51,010 - INFO - 第 3 页获取到 50 条记录
2025-08-03 10:31:51,510 - INFO - Request Parameters - Page 4:
2025-08-03 10:31:51,510 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 10:31:51,510 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 10:31:51,994 - INFO - Response - Page 4:
2025-08-03 10:31:51,994 - INFO - 查询完成，共获取到 150 条记录
2025-08-03 10:31:51,994 - INFO - 获取到 150 条表单数据
2025-08-03 10:31:51,994 - INFO - 当前日期 2025-08-02 有 403 条MySQL数据需要处理
2025-08-03 10:31:52,010 - INFO - 开始更新记录 - 表单实例ID: FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM8D
2025-08-03 10:31:52,666 - INFO - 更新表单数据成功: FINST-V7966QC1JDJX4Z9BEBI7Q91MBDX529QBOCUDM8D
2025-08-03 10:31:52,666 - INFO - 更新记录成功，变更字段: [{'field': 'order_count', 'old_value': 12547, 'new_value': 2}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-03 10:31:52,666 - INFO - 开始批量插入 253 条新记录
2025-08-03 10:31:52,990 - INFO - 批量插入响应状态码: 200
2025-08-03 10:31:52,990 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 02:31:51 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'CC0AF646-7D36-7D82-B2AE-9C180C65B7C0', 'x-acs-trace-id': '1ebe657c75f733aa696d46a9d2a68663', 'etag': '2D7oMBC5ZzkJtOjJxuISRfg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 10:31:52,990 - INFO - 批量插入响应体: {'result': ['FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMNL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMOL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMPL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMQL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMRL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMSL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMTL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMUL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMVL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMWL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMXL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMYL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMZL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM0M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM1M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM2M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM3M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM4M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM5M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM6M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM7M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM8M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM9M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMAM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMBM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMCM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMDM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMEM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMFM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMGM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMHM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMIM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMJM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMKM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMLM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMMM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMNM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMOM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMPM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMQM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMRM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMSM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMTM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMUM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMVM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMWM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMXM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMYM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMZM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM0N']}
2025-08-03 10:31:52,990 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-03 10:31:52,990 - INFO - 成功插入的数据ID: ['FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMNL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMOL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMPL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMQL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMRL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMSL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMTL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMUL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMVL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMWL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMXL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMYL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMZL', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM0M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM1M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM2M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM3M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM4M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM5M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM6M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM7M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM8M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM9M', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMAM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMBM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMCM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMDM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMEM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMFM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMGM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMHM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMIM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMJM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMKM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMLM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMMM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMNM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMOM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMPM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMQM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMRM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMSM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMTM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMUM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMVM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMWM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMXM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMYM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDMZM', 'FINST-V4G66WC178LX4GZX6VKWY9MZX83L3R3GF2VDM0N']
2025-08-03 10:31:58,208 - INFO - 批量插入响应状态码: 200
2025-08-03 10:31:58,208 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 02:31:57 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BB13461F-6E9B-7358-86EA-0EC5EB35681A', 'x-acs-trace-id': 'b57816e90f514accd3d5a934e185ac53', 'etag': '24CCIHpptgg0y19Kv1PJk/g2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 10:31:58,208 - INFO - 批量插入响应体: {'result': ['FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM69', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM79', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM89', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM99', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMA9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMB9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMC9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMD9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDME9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMF9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMG9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMH9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMI9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMJ9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMK9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDML9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMM9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMN9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMO9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMP9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMQ9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMR9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMS9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMT9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMU9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMV9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMW9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMX9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMY9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMZ9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM0A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM1A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM2A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM3A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM4A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM5A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM6A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM7A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM8A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM9A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMAA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMBA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMCA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMDA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMEA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMFA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMGA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMHA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMIA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMJA']}
2025-08-03 10:31:58,208 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-03 10:31:58,208 - INFO - 成功插入的数据ID: ['FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM69', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM79', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM89', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM99', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMA9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMB9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMC9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMD9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDME9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMF9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMG9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMH9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMI9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMJ9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMK9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDML9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMM9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMN9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMO9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMP9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMQ9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMR9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMS9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMT9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMU9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMV9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMW9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMX9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMY9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMZ9', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM0A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM1A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM2A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM3A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM4A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM5A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM6A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM7A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM8A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDM9A', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMAA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMBA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMCA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMDA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMEA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMFA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMGA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMHA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMIA', 'FINST-6PF666919NMX8Y0D6GX5RD7OIXWS2V4KF2VDMJA']
2025-08-03 10:32:03,474 - INFO - 批量插入响应状态码: 200
2025-08-03 10:32:03,474 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 02:32:02 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B6379646-7123-7F77-970F-3BACBE0A1579', 'x-acs-trace-id': 'dd247f5d0bdede276dadda9f38ace9bc', 'etag': '279iJsNjwcBH3D+7qurWDPg2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 10:32:03,474 - INFO - 批量插入响应体: {'result': ['FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMYT', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMZT', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM0U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM1U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM2U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM3U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM4U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM5U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM6U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM7U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM8U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM9U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMAU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMBU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMCU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMDU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMEU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMFU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMGU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMHU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMIU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMJU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMKU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMLU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMMU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMNU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMOU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMPU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMQU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMRU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMSU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMTU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMUU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMVU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMWU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMXU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMYU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMZU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM0V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM1V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM2V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM3V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM4V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM5V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM6V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM7V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM8V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM9V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMAV', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMBV']}
2025-08-03 10:32:03,474 - INFO - 批量插入表单数据成功，批次 3，共 50 条记录
2025-08-03 10:32:03,474 - INFO - 成功插入的数据ID: ['FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMYT', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMZT', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM0U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM1U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM2U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM3U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM4U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM5U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM6U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM7U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM8U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM9U', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMAU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMBU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMCU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMDU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMEU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMFU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMGU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMHU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMIU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMJU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMKU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMLU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMMU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMNU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMOU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMPU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMQU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMRU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMSU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMTU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMUU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMVU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMWU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMXU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMYU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMZU', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM0V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM1V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM2V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM3V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM4V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM5V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM6V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM7V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM8V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDM9V', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMAV', 'FINST-XMC66R91LAJX3WOVEX56H6MAWT2V3W6OF2VDMBV']
2025-08-03 10:32:08,724 - INFO - 批量插入响应状态码: 200
2025-08-03 10:32:08,724 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 02:32:07 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'D274E646-8DB0-77F3-96C4-C1966C21C439', 'x-acs-trace-id': 'aba0a2e946a05d1385ffbf690fcbf9a3', 'etag': '2LeLGT1YN5wfhBVMC56Lzlw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 10:32:08,724 - INFO - 批量插入响应体: {'result': ['FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMEH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMFH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMGH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMHH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMIH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMJH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMKH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMLH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMMH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMNH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMOH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMPH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMQH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMRH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMSH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMTH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMUH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMVH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMWH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMXH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMYH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMZH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM0I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM1I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM2I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM3I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM4I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM5I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM6I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM7I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM8I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM9I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMAI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMBI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMCI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMDI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMEI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMFI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMGI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMHI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMII', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMJI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMKI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMLI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMMI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMNI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMOI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMPI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMQI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMRI']}
2025-08-03 10:32:08,724 - INFO - 批量插入表单数据成功，批次 4，共 50 条记录
2025-08-03 10:32:08,724 - INFO - 成功插入的数据ID: ['FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMEH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMFH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMGH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMHH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMIH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMJH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMKH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMLH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMMH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMNH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMOH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMPH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMQH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMRH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMSH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMTH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMUH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMVH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMWH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMXH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMYH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMZH', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM0I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM1I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM2I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM3I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM4I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM5I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM6I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM7I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM8I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDM9I', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMAI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMBI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMCI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMDI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMEI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMFI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMGI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMHI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMII', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMJI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMKI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMLI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMMI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMNI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMOI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMPI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMQI', 'FINST-33666PD12LMXU5DQAGXRK8ZUTDYK2S8SF2VDMRI']
2025-08-03 10:32:13,974 - INFO - 批量插入响应状态码: 200
2025-08-03 10:32:13,974 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 02:32:12 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '75AB4FF2-98B5-748B-A204-FD8089668212', 'x-acs-trace-id': 'e15b8a498b71fa942ecdc2518aed3167', 'etag': '2n/KyePpFBZvMHNCCs7Hzuw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 10:32:13,974 - INFO - 批量插入响应体: {'result': ['FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMPA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMQA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMRA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMSA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMTA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMUA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMVA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMWA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMXA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMYA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMZA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM0B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM1B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM2B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM3B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM4B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM5B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM6B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM7B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM8B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM9B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMAB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMBB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMCB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMDB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMEB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMFB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMGB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMHB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMIB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMJB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMKB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMLB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMMB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMNB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMOB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMPB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMQB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMRB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMSB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMTB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMUB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMVB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMWB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMXB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMYB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMZB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM0C', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM1C', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM2C']}
2025-08-03 10:32:13,974 - INFO - 批量插入表单数据成功，批次 5，共 50 条记录
2025-08-03 10:32:13,974 - INFO - 成功插入的数据ID: ['FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMPA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMQA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMRA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMSA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMTA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMUA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMVA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMWA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMXA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMYA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDMZA', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM0B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM1B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM2B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM3B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM4B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM5B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM6B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM7B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2IAWF2VDM8B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM9B', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMAB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMBB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMCB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMDB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMEB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMFB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMGB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMHB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMIB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMJB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMKB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMLB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMMB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMNB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMOB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMPB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMQB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMRB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMSB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMTB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMUB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMVB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMWB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMXB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMYB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDMZB', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM0C', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM1C', 'FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM2C']
2025-08-03 10:32:19,161 - INFO - 批量插入响应状态码: 200
2025-08-03 10:32:19,161 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 02:32:18 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '05494B57-9E26-7D32-BECC-1A241575BA65', 'x-acs-trace-id': 'd69bf8d5b09e25f32d5047c997acb88d', 'etag': '1ADHygCfDYSdBoXUso2Q9xQ6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 10:32:19,161 - INFO - 批量插入响应体: {'result': ['FINST-F8866NC1LNOX7NKTA48D5AQEK0SQ3TA0G2VDMI9', 'FINST-F8866NC1LNOX7NKTA48D5AQEK0SQ3TA0G2VDMJ9', 'FINST-F8866NC1LNOX7NKTA48D5AQEK0SQ3TA0G2VDMK9']}
2025-08-03 10:32:19,161 - INFO - 批量插入表单数据成功，批次 6，共 3 条记录
2025-08-03 10:32:19,161 - INFO - 成功插入的数据ID: ['FINST-F8866NC1LNOX7NKTA48D5AQEK0SQ3TA0G2VDMI9', 'FINST-F8866NC1LNOX7NKTA48D5AQEK0SQ3TA0G2VDMJ9', 'FINST-F8866NC1LNOX7NKTA48D5AQEK0SQ3TA0G2VDMK9']
2025-08-03 10:32:24,177 - INFO - 批量插入完成，共 253 条记录
2025-08-03 10:32:24,177 - INFO - 日期 2025-08-02 处理完成 - 更新: 1 条，插入: 253 条，错误: 0 条
2025-08-03 10:32:24,177 - INFO - 开始处理日期: 2025-08-03
2025-08-03 10:32:24,177 - INFO - Request Parameters - Page 1:
2025-08-03 10:32:24,177 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 10:32:24,177 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 10:32:24,677 - INFO - Response - Page 1:
2025-08-03 10:32:24,677 - INFO - 第 1 页获取到 1 条记录
2025-08-03 10:32:25,177 - INFO - 查询完成，共获取到 1 条记录
2025-08-03 10:32:25,177 - INFO - 获取到 1 条表单数据
2025-08-03 10:32:25,177 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-03 10:32:25,177 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 10:32:25,177 - INFO - 数据同步完成！更新: 1 条，插入: 253 条，错误: 0 条
2025-08-03 10:32:25,177 - INFO - 同步完成
2025-08-03 13:30:33,544 - INFO - 使用默认增量同步（当天更新数据）
2025-08-03 13:30:33,544 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-03 13:30:33,544 - INFO - 查询参数: ('2025-08-03',)
2025-08-03 13:30:33,716 - INFO - MySQL查询成功，增量数据（日期: 2025-08-03），共获取 137 条记录
2025-08-03 13:30:33,716 - INFO - 获取到 3 个日期需要处理: ['2025-08-01', '2025-08-02', '2025-08-03']
2025-08-03 13:30:33,716 - INFO - 开始处理日期: 2025-08-01
2025-08-03 13:30:33,716 - INFO - Request Parameters - Page 1:
2025-08-03 13:30:33,716 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:30:33,716 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:30:41,904 - INFO - Response - Page 1:
2025-08-03 13:30:41,904 - INFO - 第 1 页获取到 50 条记录
2025-08-03 13:30:42,419 - INFO - Request Parameters - Page 2:
2025-08-03 13:30:42,419 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:30:42,419 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:30:50,529 - ERROR - 处理日期 2025-08-01 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 35830DA7-394D-7901-9BC0-7D661E1F7AA9 Response: {'code': 'ServiceUnavailable', 'requestid': '35830DA7-394D-7901-9BC0-7D661E1F7AA9', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 35830DA7-394D-7901-9BC0-7D661E1F7AA9)
2025-08-03 13:30:50,529 - INFO - 开始处理日期: 2025-08-02
2025-08-03 13:30:50,529 - INFO - Request Parameters - Page 1:
2025-08-03 13:30:50,529 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:30:50,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:30:55,013 - INFO - Response - Page 1:
2025-08-03 13:30:55,013 - INFO - 第 1 页获取到 50 条记录
2025-08-03 13:30:55,529 - INFO - Request Parameters - Page 2:
2025-08-03 13:30:55,529 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:30:55,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:30:56,341 - INFO - Response - Page 2:
2025-08-03 13:30:56,341 - INFO - 第 2 页获取到 50 条记录
2025-08-03 13:30:56,841 - INFO - Request Parameters - Page 3:
2025-08-03 13:30:56,841 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:30:56,841 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:30:57,576 - INFO - Response - Page 3:
2025-08-03 13:30:57,576 - INFO - 第 3 页获取到 50 条记录
2025-08-03 13:30:58,086 - INFO - Request Parameters - Page 4:
2025-08-03 13:30:58,086 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:30:58,086 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:30:58,946 - INFO - Response - Page 4:
2025-08-03 13:30:58,946 - INFO - 第 4 页获取到 50 条记录
2025-08-03 13:30:59,446 - INFO - Request Parameters - Page 5:
2025-08-03 13:30:59,446 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:30:59,446 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:31:00,196 - INFO - Response - Page 5:
2025-08-03 13:31:00,196 - INFO - 第 5 页获取到 50 条记录
2025-08-03 13:31:00,711 - INFO - Request Parameters - Page 6:
2025-08-03 13:31:00,711 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:31:00,711 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:31:01,493 - INFO - Response - Page 6:
2025-08-03 13:31:01,493 - INFO - 第 6 页获取到 50 条记录
2025-08-03 13:31:01,993 - INFO - Request Parameters - Page 7:
2025-08-03 13:31:01,993 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:31:01,993 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:31:02,899 - INFO - Response - Page 7:
2025-08-03 13:31:02,899 - INFO - 第 7 页获取到 50 条记录
2025-08-03 13:31:03,399 - INFO - Request Parameters - Page 8:
2025-08-03 13:31:03,399 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:31:03,399 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:31:04,289 - INFO - Response - Page 8:
2025-08-03 13:31:04,289 - INFO - 第 8 页获取到 50 条记录
2025-08-03 13:31:04,805 - INFO - Request Parameters - Page 9:
2025-08-03 13:31:04,805 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:31:04,805 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:31:05,352 - INFO - Response - Page 9:
2025-08-03 13:31:05,352 - INFO - 第 9 页获取到 3 条记录
2025-08-03 13:31:05,868 - INFO - 查询完成，共获取到 403 条记录
2025-08-03 13:31:05,868 - INFO - 获取到 403 条表单数据
2025-08-03 13:31:05,868 - INFO - 当前日期 2025-08-02 有 133 条MySQL数据需要处理
2025-08-03 13:31:05,868 - INFO - 开始更新记录 - 表单实例ID: FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM0C
2025-08-03 13:31:06,461 - INFO - 更新表单数据成功: FINST-NYC66LB1RBNXM9NE8QZIW6K71KWW2JAWF2VDM0C
2025-08-03 13:31:06,461 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 42450.0, 'new_value': 32450.0}, {'field': 'total_amount', 'old_value': 42450.0, 'new_value': 32450.0}]
2025-08-03 13:31:06,461 - INFO - 开始批量插入 36 条新记录
2025-08-03 13:31:06,711 - INFO - 批量插入响应状态码: 200
2025-08-03 13:31:06,711 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 05:31:05 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1740', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'DC271647-6E2C-735F-8609-48CE94E1ADD2', 'x-acs-trace-id': '1b340b1a2df39fa422211360e82025ee', 'etag': '1iDI3qjXvzFXX8ygyDHcNiA0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 13:31:06,711 - INFO - 批量插入响应体: {'result': ['FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMGI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMHI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMII', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMJI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMKI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMLI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMMI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMNI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMOI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMPI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMQI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMRI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMSI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMTI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMUI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMVI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMWI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMXI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMYI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMZI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM0J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM1J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM2J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM3J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM4J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM5J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM6J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM7J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM8J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM9J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMAJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMBJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMCJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMDJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMEJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMFJ']}
2025-08-03 13:31:06,711 - INFO - 批量插入表单数据成功，批次 1，共 36 条记录
2025-08-03 13:31:06,711 - INFO - 成功插入的数据ID: ['FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMGI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMHI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMII', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMJI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMKI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMLI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMMI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMNI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMOI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMPI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMQI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMRI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMSI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMTI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMUI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMVI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMWI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMXI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMYI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMZI', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM0J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM1J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM2J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM3J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM4J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM5J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM6J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM7J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM8J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDM9J', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMAJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMBJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMCJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMDJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMEJ', 'FINST-K7G66FA1BCMX47317QGS4ADF541S3FSXT8VDMFJ']
2025-08-03 13:31:11,727 - INFO - 批量插入完成，共 36 条记录
2025-08-03 13:31:11,727 - INFO - 日期 2025-08-02 处理完成 - 更新: 1 条，插入: 36 条，错误: 0 条
2025-08-03 13:31:11,727 - INFO - 开始处理日期: 2025-08-03
2025-08-03 13:31:11,727 - INFO - Request Parameters - Page 1:
2025-08-03 13:31:11,727 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:31:11,727 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:31:12,227 - INFO - Response - Page 1:
2025-08-03 13:31:12,227 - INFO - 第 1 页获取到 1 条记录
2025-08-03 13:31:12,742 - INFO - 查询完成，共获取到 1 条记录
2025-08-03 13:31:12,742 - INFO - 获取到 1 条表单数据
2025-08-03 13:31:12,742 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-03 13:31:12,742 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 13:31:12,742 - INFO - 数据同步完成！更新: 1 条，插入: 36 条，错误: 1 条
2025-08-03 13:32:12,753 - INFO - 开始同步昨天与今天的销售数据: 2025-08-02 至 2025-08-03
2025-08-03 13:32:12,753 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-03 13:32:12,753 - INFO - 查询参数: ('2025-08-02', '2025-08-03')
2025-08-03 13:32:12,925 - INFO - MySQL查询成功，时间段: 2025-08-02 至 2025-08-03，共获取 499 条记录
2025-08-03 13:32:12,925 - INFO - 获取到 2 个日期需要处理: ['2025-08-02', '2025-08-03']
2025-08-03 13:32:12,925 - INFO - 开始处理日期: 2025-08-02
2025-08-03 13:32:12,925 - INFO - Request Parameters - Page 1:
2025-08-03 13:32:12,925 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:12,925 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:13,690 - INFO - Response - Page 1:
2025-08-03 13:32:13,690 - INFO - 第 1 页获取到 50 条记录
2025-08-03 13:32:14,206 - INFO - Request Parameters - Page 2:
2025-08-03 13:32:14,206 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:14,206 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:14,972 - INFO - Response - Page 2:
2025-08-03 13:32:14,972 - INFO - 第 2 页获取到 50 条记录
2025-08-03 13:32:15,487 - INFO - Request Parameters - Page 3:
2025-08-03 13:32:15,487 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:15,487 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:16,253 - INFO - Response - Page 3:
2025-08-03 13:32:16,253 - INFO - 第 3 页获取到 50 条记录
2025-08-03 13:32:16,768 - INFO - Request Parameters - Page 4:
2025-08-03 13:32:16,768 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:16,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:17,596 - INFO - Response - Page 4:
2025-08-03 13:32:17,596 - INFO - 第 4 页获取到 50 条记录
2025-08-03 13:32:18,112 - INFO - Request Parameters - Page 5:
2025-08-03 13:32:18,112 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:18,112 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:18,862 - INFO - Response - Page 5:
2025-08-03 13:32:18,862 - INFO - 第 5 页获取到 50 条记录
2025-08-03 13:32:19,362 - INFO - Request Parameters - Page 6:
2025-08-03 13:32:19,362 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:19,362 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:20,081 - INFO - Response - Page 6:
2025-08-03 13:32:20,081 - INFO - 第 6 页获取到 50 条记录
2025-08-03 13:32:20,581 - INFO - Request Parameters - Page 7:
2025-08-03 13:32:20,581 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:20,581 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:21,362 - INFO - Response - Page 7:
2025-08-03 13:32:21,362 - INFO - 第 7 页获取到 50 条记录
2025-08-03 13:32:21,878 - INFO - Request Parameters - Page 8:
2025-08-03 13:32:21,878 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:21,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:22,596 - INFO - Response - Page 8:
2025-08-03 13:32:22,596 - INFO - 第 8 页获取到 50 条记录
2025-08-03 13:32:23,096 - INFO - Request Parameters - Page 9:
2025-08-03 13:32:23,096 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:23,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:23,831 - INFO - Response - Page 9:
2025-08-03 13:32:23,831 - INFO - 第 9 页获取到 39 条记录
2025-08-03 13:32:24,346 - INFO - 查询完成，共获取到 439 条记录
2025-08-03 13:32:24,346 - INFO - 获取到 439 条表单数据
2025-08-03 13:32:24,346 - INFO - 当前日期 2025-08-02 有 478 条MySQL数据需要处理
2025-08-03 13:32:24,362 - INFO - 开始批量插入 39 条新记录
2025-08-03 13:32:24,596 - INFO - 批量插入响应状态码: 200
2025-08-03 13:32:24,596 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 05:32:23 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '1873', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '9185368F-1137-7C03-B91A-D3B14C2ED95C', 'x-acs-trace-id': 'd5987feaa32f175993e34068b1376312', 'etag': '13Pxw9OnIQED9zm6L0ByP0Q3', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 13:32:24,596 - INFO - 批量插入响应体: {'result': ['FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMP', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMQ', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMR', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMS', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMT', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMU', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMV', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMW', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMX', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMY', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMZ', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM01', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM11', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM21', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM31', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM41', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM51', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM61', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM71', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM81', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM91', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMA1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMB1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMC1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMD1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDME1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMF1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMG1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMH1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMI1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMJ1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMK1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDML1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMM1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMN1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMO1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMP1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMQ1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMR1']}
2025-08-03 13:32:24,596 - INFO - 批量插入表单数据成功，批次 1，共 39 条记录
2025-08-03 13:32:24,596 - INFO - 成功插入的数据ID: ['FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMP', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMQ', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMR', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMS', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMT', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMU', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMV', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMW', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMX', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMY', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMZ', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM01', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM11', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM21', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM31', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM41', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM51', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM61', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM71', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM81', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDM91', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMA1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMB1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMC1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMD1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDME1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMF1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3VVLV8VDMG1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMH1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMI1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMJ1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMK1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDML1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMM1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMN1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMO1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMP1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMQ1', 'FINST-LLF66O719FPXP8CKD9J1M8YW9YHR3WVLV8VDMR1']
2025-08-03 13:32:29,612 - INFO - 批量插入完成，共 39 条记录
2025-08-03 13:32:29,612 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 39 条，错误: 0 条
2025-08-03 13:32:29,612 - INFO - 开始处理日期: 2025-08-03
2025-08-03 13:32:29,612 - INFO - Request Parameters - Page 1:
2025-08-03 13:32:29,612 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 13:32:29,612 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 13:32:30,128 - INFO - Response - Page 1:
2025-08-03 13:32:30,128 - INFO - 第 1 页获取到 1 条记录
2025-08-03 13:32:30,628 - INFO - 查询完成，共获取到 1 条记录
2025-08-03 13:32:30,628 - INFO - 获取到 1 条表单数据
2025-08-03 13:32:30,628 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-03 13:32:30,628 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 13:32:30,628 - INFO - 数据同步完成！更新: 0 条，插入: 39 条，错误: 0 条
2025-08-03 13:32:30,628 - INFO - 同步完成
2025-08-03 16:30:33,496 - INFO - 使用默认增量同步（当天更新数据）
2025-08-03 16:30:33,496 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-03 16:30:33,496 - INFO - 查询参数: ('2025-08-03',)
2025-08-03 16:30:33,652 - INFO - MySQL查询成功，增量数据（日期: 2025-08-03），共获取 143 条记录
2025-08-03 16:30:33,652 - INFO - 获取到 6 个日期需要处理: ['2025-07-18', '2025-07-21', '2025-07-30', '2025-08-01', '2025-08-02', '2025-08-03']
2025-08-03 16:30:33,668 - INFO - 开始处理日期: 2025-07-18
2025-08-03 16:30:33,668 - INFO - Request Parameters - Page 1:
2025-08-03 16:30:33,668 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:30:33,668 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:30:41,761 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 3E804E9D-831F-79EB-863A-EC24B5EEF7B1 Response: {'code': 'ServiceUnavailable', 'requestid': '3E804E9D-831F-79EB-863A-EC24B5EEF7B1', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 3E804E9D-831F-79EB-863A-EC24B5EEF7B1)
2025-08-03 16:30:41,761 - INFO - 开始处理日期: 2025-07-21
2025-08-03 16:30:41,761 - INFO - Request Parameters - Page 1:
2025-08-03 16:30:41,761 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:30:41,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:30:47,090 - INFO - Response - Page 1:
2025-08-03 16:30:47,090 - INFO - 第 1 页获取到 50 条记录
2025-08-03 16:30:47,590 - INFO - Request Parameters - Page 2:
2025-08-03 16:30:47,590 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:30:47,590 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:30:55,840 - INFO - Response - Page 2:
2025-08-03 16:30:55,840 - INFO - 第 2 页获取到 50 条记录
2025-08-03 16:30:56,355 - INFO - Request Parameters - Page 3:
2025-08-03 16:30:56,355 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:30:56,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:30:57,043 - INFO - Response - Page 3:
2025-08-03 16:30:57,043 - INFO - 第 3 页获取到 50 条记录
2025-08-03 16:30:57,543 - INFO - Request Parameters - Page 4:
2025-08-03 16:30:57,543 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:30:57,543 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:30:58,386 - INFO - Response - Page 4:
2025-08-03 16:30:58,386 - INFO - 第 4 页获取到 50 条记录
2025-08-03 16:30:58,902 - INFO - Request Parameters - Page 5:
2025-08-03 16:30:58,902 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:30:58,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:30:59,668 - INFO - Response - Page 5:
2025-08-03 16:30:59,668 - INFO - 第 5 页获取到 50 条记录
2025-08-03 16:31:00,183 - INFO - Request Parameters - Page 6:
2025-08-03 16:31:00,183 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:00,183 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:00,918 - INFO - Response - Page 6:
2025-08-03 16:31:00,918 - INFO - 第 6 页获取到 50 条记录
2025-08-03 16:31:01,433 - INFO - Request Parameters - Page 7:
2025-08-03 16:31:01,433 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:01,433 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:02,136 - INFO - Response - Page 7:
2025-08-03 16:31:02,136 - INFO - 第 7 页获取到 50 条记录
2025-08-03 16:31:02,647 - INFO - Request Parameters - Page 8:
2025-08-03 16:31:02,647 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:02,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:03,350 - INFO - Response - Page 8:
2025-08-03 16:31:03,350 - INFO - 第 8 页获取到 50 条记录
2025-08-03 16:31:03,866 - INFO - Request Parameters - Page 9:
2025-08-03 16:31:03,866 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:03,866 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:04,600 - INFO - Response - Page 9:
2025-08-03 16:31:04,600 - INFO - 第 9 页获取到 50 条记录
2025-08-03 16:31:05,100 - INFO - Request Parameters - Page 10:
2025-08-03 16:31:05,100 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:05,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:05,866 - INFO - Response - Page 10:
2025-08-03 16:31:05,866 - INFO - 第 10 页获取到 50 条记录
2025-08-03 16:31:06,381 - INFO - Request Parameters - Page 11:
2025-08-03 16:31:06,381 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:06,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:07,069 - INFO - Response - Page 11:
2025-08-03 16:31:07,069 - INFO - 第 11 页获取到 29 条记录
2025-08-03 16:31:07,569 - INFO - 查询完成，共获取到 529 条记录
2025-08-03 16:31:07,569 - INFO - 获取到 529 条表单数据
2025-08-03 16:31:07,569 - INFO - 当前日期 2025-07-21 有 1 条MySQL数据需要处理
2025-08-03 16:31:07,569 - INFO - 开始更新记录 - 表单实例ID: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM62
2025-08-03 16:31:08,241 - INFO - 更新表单数据成功: FINST-737662B1J3DXCCBOCU0EW7AQBH7Q2J6N4XDDM62
2025-08-03 16:31:08,241 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 0.0, 'new_value': 75000.0}, {'field': 'total_amount', 'old_value': 0.0, 'new_value': 75000.0}]
2025-08-03 16:31:08,241 - INFO - 日期 2025-07-21 处理完成 - 更新: 1 条，插入: 0 条，错误: 0 条
2025-08-03 16:31:08,241 - INFO - 开始处理日期: 2025-07-30
2025-08-03 16:31:08,241 - INFO - Request Parameters - Page 1:
2025-08-03 16:31:08,241 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:08,241 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:09,022 - INFO - Response - Page 1:
2025-08-03 16:31:09,022 - INFO - 第 1 页获取到 50 条记录
2025-08-03 16:31:09,538 - INFO - Request Parameters - Page 2:
2025-08-03 16:31:09,538 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:09,538 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:10,241 - INFO - Response - Page 2:
2025-08-03 16:31:10,241 - INFO - 第 2 页获取到 50 条记录
2025-08-03 16:31:10,756 - INFO - Request Parameters - Page 3:
2025-08-03 16:31:10,756 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:10,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:11,491 - INFO - Response - Page 3:
2025-08-03 16:31:11,491 - INFO - 第 3 页获取到 50 条记录
2025-08-03 16:31:12,006 - INFO - Request Parameters - Page 4:
2025-08-03 16:31:12,006 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:12,006 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:12,694 - INFO - Response - Page 4:
2025-08-03 16:31:12,694 - INFO - 第 4 页获取到 50 条记录
2025-08-03 16:31:13,194 - INFO - Request Parameters - Page 5:
2025-08-03 16:31:13,194 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:13,194 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:13,959 - INFO - Response - Page 5:
2025-08-03 16:31:13,959 - INFO - 第 5 页获取到 50 条记录
2025-08-03 16:31:14,459 - INFO - Request Parameters - Page 6:
2025-08-03 16:31:14,459 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:14,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:15,178 - INFO - Response - Page 6:
2025-08-03 16:31:15,178 - INFO - 第 6 页获取到 50 条记录
2025-08-03 16:31:15,694 - INFO - Request Parameters - Page 7:
2025-08-03 16:31:15,694 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:15,694 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:16,366 - INFO - Response - Page 7:
2025-08-03 16:31:16,366 - INFO - 第 7 页获取到 50 条记录
2025-08-03 16:31:16,881 - INFO - Request Parameters - Page 8:
2025-08-03 16:31:16,881 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:16,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:17,569 - INFO - Response - Page 8:
2025-08-03 16:31:17,569 - INFO - 第 8 页获取到 50 条记录
2025-08-03 16:31:18,084 - INFO - Request Parameters - Page 9:
2025-08-03 16:31:18,084 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:18,084 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:18,772 - INFO - Response - Page 9:
2025-08-03 16:31:18,772 - INFO - 第 9 页获取到 50 条记录
2025-08-03 16:31:19,272 - INFO - Request Parameters - Page 10:
2025-08-03 16:31:19,272 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:19,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:19,928 - INFO - Response - Page 10:
2025-08-03 16:31:19,928 - INFO - 第 10 页获取到 50 条记录
2025-08-03 16:31:20,428 - INFO - Request Parameters - Page 11:
2025-08-03 16:31:20,428 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:20,428 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:21,147 - INFO - Response - Page 11:
2025-08-03 16:31:21,147 - INFO - 第 11 页获取到 43 条记录
2025-08-03 16:31:21,663 - INFO - 查询完成，共获取到 543 条记录
2025-08-03 16:31:21,663 - INFO - 获取到 543 条表单数据
2025-08-03 16:31:21,663 - INFO - 当前日期 2025-07-30 有 1 条MySQL数据需要处理
2025-08-03 16:31:21,663 - INFO - 开始批量插入 1 条新记录
2025-08-03 16:31:21,834 - INFO - 批量插入响应状态码: 200
2025-08-03 16:31:21,834 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 08:31:20 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BB8697DA-27E0-7738-B8DE-651BEBBC777D', 'x-acs-trace-id': '2c64bc8f3d9f77cc7bac3b50deb6deaf', 'etag': '6qNQZzMjb65SwLTMi9g/mQw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 16:31:21,834 - INFO - 批量插入响应体: {'result': ['FINST-CK766D71GNNXAO4ZDLFQK5RAD10F3KUQ9FVDMCD']}
2025-08-03 16:31:21,834 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-03 16:31:21,834 - INFO - 成功插入的数据ID: ['FINST-CK766D71GNNXAO4ZDLFQK5RAD10F3KUQ9FVDMCD']
2025-08-03 16:31:26,850 - INFO - 批量插入完成，共 1 条记录
2025-08-03 16:31:26,850 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-03 16:31:26,850 - INFO - 开始处理日期: 2025-08-01
2025-08-03 16:31:26,850 - INFO - Request Parameters - Page 1:
2025-08-03 16:31:26,850 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:26,850 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:27,600 - INFO - Response - Page 1:
2025-08-03 16:31:27,600 - INFO - 第 1 页获取到 50 条记录
2025-08-03 16:31:28,100 - INFO - Request Parameters - Page 2:
2025-08-03 16:31:28,100 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:28,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:28,866 - INFO - Response - Page 2:
2025-08-03 16:31:28,866 - INFO - 第 2 页获取到 50 条记录
2025-08-03 16:31:29,381 - INFO - Request Parameters - Page 3:
2025-08-03 16:31:29,381 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:29,381 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:30,131 - INFO - Response - Page 3:
2025-08-03 16:31:30,131 - INFO - 第 3 页获取到 50 条记录
2025-08-03 16:31:30,647 - INFO - Request Parameters - Page 4:
2025-08-03 16:31:30,647 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:30,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:31,444 - INFO - Response - Page 4:
2025-08-03 16:31:31,444 - INFO - 第 4 页获取到 50 条记录
2025-08-03 16:31:31,959 - INFO - Request Parameters - Page 5:
2025-08-03 16:31:31,959 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:31,959 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:32,694 - INFO - Response - Page 5:
2025-08-03 16:31:32,694 - INFO - 第 5 页获取到 50 条记录
2025-08-03 16:31:33,209 - INFO - Request Parameters - Page 6:
2025-08-03 16:31:33,209 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:33,209 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:33,881 - INFO - Response - Page 6:
2025-08-03 16:31:33,881 - INFO - 第 6 页获取到 50 条记录
2025-08-03 16:31:34,397 - INFO - Request Parameters - Page 7:
2025-08-03 16:31:34,397 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:34,397 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:35,209 - INFO - Response - Page 7:
2025-08-03 16:31:35,209 - INFO - 第 7 页获取到 50 条记录
2025-08-03 16:31:35,709 - INFO - Request Parameters - Page 8:
2025-08-03 16:31:35,709 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:35,709 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:36,428 - INFO - Response - Page 8:
2025-08-03 16:31:36,428 - INFO - 第 8 页获取到 50 条记录
2025-08-03 16:31:36,944 - INFO - Request Parameters - Page 9:
2025-08-03 16:31:36,944 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:36,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:37,741 - INFO - Response - Page 9:
2025-08-03 16:31:37,741 - INFO - 第 9 页获取到 50 条记录
2025-08-03 16:31:38,256 - INFO - Request Parameters - Page 10:
2025-08-03 16:31:38,256 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:38,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:38,975 - INFO - Response - Page 10:
2025-08-03 16:31:38,975 - INFO - 第 10 页获取到 36 条记录
2025-08-03 16:31:39,491 - INFO - 查询完成，共获取到 486 条记录
2025-08-03 16:31:39,491 - INFO - 获取到 486 条表单数据
2025-08-03 16:31:39,491 - INFO - 当前日期 2025-08-01 有 4 条MySQL数据需要处理
2025-08-03 16:31:39,491 - INFO - 开始更新记录 - 表单实例ID: FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMV4
2025-08-03 16:31:40,084 - INFO - 更新表单数据成功: FINST-7PF66N91FKNXXH5890YR397Y38D52ROEFTTDMV4
2025-08-03 16:31:40,084 - INFO - 更新记录成功，变更字段: [{'field': 'offline_amount', 'old_value': 5204.0, 'new_value': 5098.05}, {'field': 'total_amount', 'old_value': 14277.45, 'new_value': 14171.5}, {'field': 'report_source', 'old_value': '商户上报', 'new_value': '运营调整'}]
2025-08-03 16:31:40,084 - INFO - 开始批量插入 3 条新记录
2025-08-03 16:31:40,241 - INFO - 批量插入响应状态码: 200
2025-08-03 16:31:40,241 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 08:31:39 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C19DD3C6-70AD-7247-8774-CBDAF3B72043', 'x-acs-trace-id': 'ae9c7ec3a663885499ef7db92e06ee58', 'etag': '1QQk2VeAZwSNXUWBrV9MH4g6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 16:31:40,241 - INFO - 批量插入响应体: {'result': ['FINST-Y7D660D1YDOXDZT3EXHKZBPYLNR63U15AFVDMU2', 'FINST-Y7D660D1YDOXDZT3EXHKZBPYLNR63U15AFVDMV2', 'FINST-Y7D660D1YDOXDZT3EXHKZBPYLNR63U15AFVDMW2']}
2025-08-03 16:31:40,241 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-03 16:31:40,241 - INFO - 成功插入的数据ID: ['FINST-Y7D660D1YDOXDZT3EXHKZBPYLNR63U15AFVDMU2', 'FINST-Y7D660D1YDOXDZT3EXHKZBPYLNR63U15AFVDMV2', 'FINST-Y7D660D1YDOXDZT3EXHKZBPYLNR63U15AFVDMW2']
2025-08-03 16:31:45,256 - INFO - 批量插入完成，共 3 条记录
2025-08-03 16:31:45,256 - INFO - 日期 2025-08-01 处理完成 - 更新: 1 条，插入: 3 条，错误: 0 条
2025-08-03 16:31:45,256 - INFO - 开始处理日期: 2025-08-02
2025-08-03 16:31:45,256 - INFO - Request Parameters - Page 1:
2025-08-03 16:31:45,256 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:45,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:46,022 - INFO - Response - Page 1:
2025-08-03 16:31:46,022 - INFO - 第 1 页获取到 50 条记录
2025-08-03 16:31:46,537 - INFO - Request Parameters - Page 2:
2025-08-03 16:31:46,537 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:46,537 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:47,241 - INFO - Response - Page 2:
2025-08-03 16:31:47,241 - INFO - 第 2 页获取到 50 条记录
2025-08-03 16:31:47,756 - INFO - Request Parameters - Page 3:
2025-08-03 16:31:47,756 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:47,756 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:48,444 - INFO - Response - Page 3:
2025-08-03 16:31:48,444 - INFO - 第 3 页获取到 50 条记录
2025-08-03 16:31:48,944 - INFO - Request Parameters - Page 4:
2025-08-03 16:31:48,944 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:48,944 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:49,584 - INFO - Response - Page 4:
2025-08-03 16:31:49,584 - INFO - 第 4 页获取到 50 条记录
2025-08-03 16:31:50,100 - INFO - Request Parameters - Page 5:
2025-08-03 16:31:50,100 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:50,100 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:50,756 - INFO - Response - Page 5:
2025-08-03 16:31:50,756 - INFO - 第 5 页获取到 50 条记录
2025-08-03 16:31:51,256 - INFO - Request Parameters - Page 6:
2025-08-03 16:31:51,256 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:51,256 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:51,959 - INFO - Response - Page 6:
2025-08-03 16:31:51,959 - INFO - 第 6 页获取到 50 条记录
2025-08-03 16:31:52,459 - INFO - Request Parameters - Page 7:
2025-08-03 16:31:52,459 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:52,459 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:53,225 - INFO - Response - Page 7:
2025-08-03 16:31:53,225 - INFO - 第 7 页获取到 50 条记录
2025-08-03 16:31:53,725 - INFO - Request Parameters - Page 8:
2025-08-03 16:31:53,725 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:53,725 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:54,506 - INFO - Response - Page 8:
2025-08-03 16:31:54,506 - INFO - 第 8 页获取到 50 条记录
2025-08-03 16:31:55,022 - INFO - Request Parameters - Page 9:
2025-08-03 16:31:55,022 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:55,022 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:55,725 - INFO - Response - Page 9:
2025-08-03 16:31:55,725 - INFO - 第 9 页获取到 50 条记录
2025-08-03 16:31:56,225 - INFO - Request Parameters - Page 10:
2025-08-03 16:31:56,225 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:31:56,225 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:31:56,850 - INFO - Response - Page 10:
2025-08-03 16:31:56,850 - INFO - 第 10 页获取到 28 条记录
2025-08-03 16:31:57,365 - INFO - 查询完成，共获取到 478 条记录
2025-08-03 16:31:57,365 - INFO - 获取到 478 条表单数据
2025-08-03 16:31:57,365 - INFO - 当前日期 2025-08-02 有 134 条MySQL数据需要处理
2025-08-03 16:31:57,365 - INFO - 开始批量插入 1 条新记录
2025-08-03 16:31:57,537 - INFO - 批量插入响应状态码: 200
2025-08-03 16:31:57,537 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 08:31:56 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '60', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'B01AD8C4-E5ED-7C27-8C96-C8B1AD3F9D36', 'x-acs-trace-id': '3139b75ebc74c30b47986ab883fd309f', 'etag': '6olvHDpQgjKS6vIqwuvp5rQ0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 16:31:57,537 - INFO - 批量插入响应体: {'result': ['FINST-2PF66TC1L8JX4EDN93GDE7G4B31X32EIAFVDMCK']}
2025-08-03 16:31:57,537 - INFO - 批量插入表单数据成功，批次 1，共 1 条记录
2025-08-03 16:31:57,537 - INFO - 成功插入的数据ID: ['FINST-2PF66TC1L8JX4EDN93GDE7G4B31X32EIAFVDMCK']
2025-08-03 16:32:02,548 - INFO - 批量插入完成，共 1 条记录
2025-08-03 16:32:02,548 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 1 条，错误: 0 条
2025-08-03 16:32:02,548 - INFO - 开始处理日期: 2025-08-03
2025-08-03 16:32:02,548 - INFO - Request Parameters - Page 1:
2025-08-03 16:32:02,548 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:32:02,548 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:32:03,079 - INFO - Response - Page 1:
2025-08-03 16:32:03,079 - INFO - 第 1 页获取到 1 条记录
2025-08-03 16:32:03,579 - INFO - 查询完成，共获取到 1 条记录
2025-08-03 16:32:03,579 - INFO - 获取到 1 条表单数据
2025-08-03 16:32:03,579 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-03 16:32:03,579 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 16:32:03,579 - INFO - 数据同步完成！更新: 2 条，插入: 5 条，错误: 1 条
2025-08-03 16:33:03,590 - INFO - 开始同步昨天与今天的销售数据: 2025-08-02 至 2025-08-03
2025-08-03 16:33:03,590 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-03 16:33:03,590 - INFO - 查询参数: ('2025-08-02', '2025-08-03')
2025-08-03 16:33:03,761 - INFO - MySQL查询成功，时间段: 2025-08-02 至 2025-08-03，共获取 500 条记录
2025-08-03 16:33:03,761 - INFO - 获取到 2 个日期需要处理: ['2025-08-02', '2025-08-03']
2025-08-03 16:33:03,761 - INFO - 开始处理日期: 2025-08-02
2025-08-03 16:33:03,761 - INFO - Request Parameters - Page 1:
2025-08-03 16:33:03,761 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:03,761 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:04,543 - INFO - Response - Page 1:
2025-08-03 16:33:04,543 - INFO - 第 1 页获取到 50 条记录
2025-08-03 16:33:05,058 - INFO - Request Parameters - Page 2:
2025-08-03 16:33:05,058 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:05,058 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:05,777 - INFO - Response - Page 2:
2025-08-03 16:33:05,777 - INFO - 第 2 页获取到 50 条记录
2025-08-03 16:33:06,277 - INFO - Request Parameters - Page 3:
2025-08-03 16:33:06,277 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:06,277 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:06,933 - INFO - Response - Page 3:
2025-08-03 16:33:06,933 - INFO - 第 3 页获取到 50 条记录
2025-08-03 16:33:07,449 - INFO - Request Parameters - Page 4:
2025-08-03 16:33:07,449 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:07,449 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:08,168 - INFO - Response - Page 4:
2025-08-03 16:33:08,168 - INFO - 第 4 页获取到 50 条记录
2025-08-03 16:33:08,683 - INFO - Request Parameters - Page 5:
2025-08-03 16:33:08,683 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:08,683 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:09,402 - INFO - Response - Page 5:
2025-08-03 16:33:09,402 - INFO - 第 5 页获取到 50 条记录
2025-08-03 16:33:09,918 - INFO - Request Parameters - Page 6:
2025-08-03 16:33:09,918 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:09,918 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:10,636 - INFO - Response - Page 6:
2025-08-03 16:33:10,636 - INFO - 第 6 页获取到 50 条记录
2025-08-03 16:33:11,136 - INFO - Request Parameters - Page 7:
2025-08-03 16:33:11,136 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:11,136 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:11,840 - INFO - Response - Page 7:
2025-08-03 16:33:11,840 - INFO - 第 7 页获取到 50 条记录
2025-08-03 16:33:12,355 - INFO - Request Parameters - Page 8:
2025-08-03 16:33:12,355 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:12,355 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:13,105 - INFO - Response - Page 8:
2025-08-03 16:33:13,105 - INFO - 第 8 页获取到 50 条记录
2025-08-03 16:33:13,605 - INFO - Request Parameters - Page 9:
2025-08-03 16:33:13,605 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:13,605 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:14,386 - INFO - Response - Page 9:
2025-08-03 16:33:14,386 - INFO - 第 9 页获取到 50 条记录
2025-08-03 16:33:14,902 - INFO - Request Parameters - Page 10:
2025-08-03 16:33:14,902 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:14,902 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:15,574 - INFO - Response - Page 10:
2025-08-03 16:33:15,574 - INFO - 第 10 页获取到 29 条记录
2025-08-03 16:33:16,090 - INFO - 查询完成，共获取到 479 条记录
2025-08-03 16:33:16,090 - INFO - 获取到 479 条表单数据
2025-08-03 16:33:16,090 - INFO - 当前日期 2025-08-02 有 479 条MySQL数据需要处理
2025-08-03 16:33:16,121 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 16:33:16,121 - INFO - 开始处理日期: 2025-08-03
2025-08-03 16:33:16,121 - INFO - Request Parameters - Page 1:
2025-08-03 16:33:16,121 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 16:33:16,121 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 16:33:16,605 - INFO - Response - Page 1:
2025-08-03 16:33:16,605 - INFO - 第 1 页获取到 1 条记录
2025-08-03 16:33:17,121 - INFO - 查询完成，共获取到 1 条记录
2025-08-03 16:33:17,121 - INFO - 获取到 1 条表单数据
2025-08-03 16:33:17,121 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-03 16:33:17,121 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 16:33:17,121 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 16:33:17,121 - INFO - 同步完成
2025-08-03 19:30:32,611 - INFO - 使用默认增量同步（当天更新数据）
2025-08-03 19:30:32,611 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-03 19:30:32,611 - INFO - 查询参数: ('2025-08-03',)
2025-08-03 19:30:32,783 - INFO - MySQL查询成功，增量数据（日期: 2025-08-03），共获取 143 条记录
2025-08-03 19:30:32,783 - INFO - 获取到 6 个日期需要处理: ['2025-07-18', '2025-07-21', '2025-07-30', '2025-08-01', '2025-08-02', '2025-08-03']
2025-08-03 19:30:32,783 - INFO - 开始处理日期: 2025-07-18
2025-08-03 19:30:32,783 - INFO - Request Parameters - Page 1:
2025-08-03 19:30:32,783 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:32,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:40,908 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: 584B5863-6180-70B2-A45C-76B701781D91 Response: {'code': 'ServiceUnavailable', 'requestid': '584B5863-6180-70B2-A45C-76B701781D91', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: 584B5863-6180-70B2-A45C-76B701781D91)
2025-08-03 19:30:40,908 - INFO - 开始处理日期: 2025-07-21
2025-08-03 19:30:40,908 - INFO - Request Parameters - Page 1:
2025-08-03 19:30:40,908 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:40,908 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:44,517 - INFO - Response - Page 1:
2025-08-03 19:30:44,517 - INFO - 第 1 页获取到 50 条记录
2025-08-03 19:30:45,017 - INFO - Request Parameters - Page 2:
2025-08-03 19:30:45,017 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:45,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:45,752 - INFO - Response - Page 2:
2025-08-03 19:30:45,752 - INFO - 第 2 页获取到 50 条记录
2025-08-03 19:30:46,267 - INFO - Request Parameters - Page 3:
2025-08-03 19:30:46,267 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:46,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:46,971 - INFO - Response - Page 3:
2025-08-03 19:30:46,971 - INFO - 第 3 页获取到 50 条记录
2025-08-03 19:30:47,486 - INFO - Request Parameters - Page 4:
2025-08-03 19:30:47,486 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:47,486 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:48,268 - INFO - Response - Page 4:
2025-08-03 19:30:48,268 - INFO - 第 4 页获取到 50 条记录
2025-08-03 19:30:48,783 - INFO - Request Parameters - Page 5:
2025-08-03 19:30:48,783 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:48,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:49,486 - INFO - Response - Page 5:
2025-08-03 19:30:49,486 - INFO - 第 5 页获取到 50 条记录
2025-08-03 19:30:49,986 - INFO - Request Parameters - Page 6:
2025-08-03 19:30:49,986 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:49,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:57,440 - INFO - Response - Page 6:
2025-08-03 19:30:57,440 - INFO - 第 6 页获取到 50 条记录
2025-08-03 19:30:57,955 - INFO - Request Parameters - Page 7:
2025-08-03 19:30:57,955 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:57,955 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:58,674 - INFO - Response - Page 7:
2025-08-03 19:30:58,674 - INFO - 第 7 页获取到 50 条记录
2025-08-03 19:30:59,190 - INFO - Request Parameters - Page 8:
2025-08-03 19:30:59,190 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:30:59,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:30:59,909 - INFO - Response - Page 8:
2025-08-03 19:30:59,909 - INFO - 第 8 页获取到 50 条记录
2025-08-03 19:31:00,424 - INFO - Request Parameters - Page 9:
2025-08-03 19:31:00,424 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:00,424 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:01,159 - INFO - Response - Page 9:
2025-08-03 19:31:01,159 - INFO - 第 9 页获取到 50 条记录
2025-08-03 19:31:01,674 - INFO - Request Parameters - Page 10:
2025-08-03 19:31:01,674 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:01,674 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:02,362 - INFO - Response - Page 10:
2025-08-03 19:31:02,362 - INFO - 第 10 页获取到 50 条记录
2025-08-03 19:31:02,878 - INFO - Request Parameters - Page 11:
2025-08-03 19:31:02,878 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:02,878 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:03,581 - INFO - Response - Page 11:
2025-08-03 19:31:03,581 - INFO - 第 11 页获取到 29 条记录
2025-08-03 19:31:04,096 - INFO - 查询完成，共获取到 529 条记录
2025-08-03 19:31:04,096 - INFO - 获取到 529 条表单数据
2025-08-03 19:31:04,096 - INFO - 当前日期 2025-07-21 有 1 条MySQL数据需要处理
2025-08-03 19:31:04,096 - INFO - 日期 2025-07-21 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 19:31:04,096 - INFO - 开始处理日期: 2025-07-30
2025-08-03 19:31:04,096 - INFO - Request Parameters - Page 1:
2025-08-03 19:31:04,096 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:04,096 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:04,815 - INFO - Response - Page 1:
2025-08-03 19:31:04,815 - INFO - 第 1 页获取到 50 条记录
2025-08-03 19:31:05,331 - INFO - Request Parameters - Page 2:
2025-08-03 19:31:05,331 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:05,331 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:06,060 - INFO - Response - Page 2:
2025-08-03 19:31:06,060 - INFO - 第 2 页获取到 50 条记录
2025-08-03 19:31:06,576 - INFO - Request Parameters - Page 3:
2025-08-03 19:31:06,576 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:06,576 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:07,263 - INFO - Response - Page 3:
2025-08-03 19:31:07,263 - INFO - 第 3 页获取到 50 条记录
2025-08-03 19:31:07,779 - INFO - Request Parameters - Page 4:
2025-08-03 19:31:07,779 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:07,779 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:08,545 - INFO - Response - Page 4:
2025-08-03 19:31:08,545 - INFO - 第 4 页获取到 50 条记录
2025-08-03 19:31:09,045 - INFO - Request Parameters - Page 5:
2025-08-03 19:31:09,045 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:09,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:09,873 - INFO - Response - Page 5:
2025-08-03 19:31:09,873 - INFO - 第 5 页获取到 50 条记录
2025-08-03 19:31:10,389 - INFO - Request Parameters - Page 6:
2025-08-03 19:31:10,389 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:10,389 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:11,060 - INFO - Response - Page 6:
2025-08-03 19:31:11,060 - INFO - 第 6 页获取到 50 条记录
2025-08-03 19:31:11,561 - INFO - Request Parameters - Page 7:
2025-08-03 19:31:11,561 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:11,561 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:12,357 - INFO - Response - Page 7:
2025-08-03 19:31:12,357 - INFO - 第 7 页获取到 50 条记录
2025-08-03 19:31:12,873 - INFO - Request Parameters - Page 8:
2025-08-03 19:31:12,873 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:12,873 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:13,561 - INFO - Response - Page 8:
2025-08-03 19:31:13,561 - INFO - 第 8 页获取到 50 条记录
2025-08-03 19:31:14,061 - INFO - Request Parameters - Page 9:
2025-08-03 19:31:14,061 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:14,061 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:14,826 - INFO - Response - Page 9:
2025-08-03 19:31:14,826 - INFO - 第 9 页获取到 50 条记录
2025-08-03 19:31:15,342 - INFO - Request Parameters - Page 10:
2025-08-03 19:31:15,342 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:15,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:16,029 - INFO - Response - Page 10:
2025-08-03 19:31:16,029 - INFO - 第 10 页获取到 50 条记录
2025-08-03 19:31:16,529 - INFO - Request Parameters - Page 11:
2025-08-03 19:31:16,529 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:16,529 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:17,326 - INFO - Response - Page 11:
2025-08-03 19:31:17,326 - INFO - 第 11 页获取到 44 条记录
2025-08-03 19:31:17,826 - INFO - 查询完成，共获取到 544 条记录
2025-08-03 19:31:17,826 - INFO - 获取到 544 条表单数据
2025-08-03 19:31:17,826 - INFO - 当前日期 2025-07-30 有 1 条MySQL数据需要处理
2025-08-03 19:31:17,826 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 19:31:17,826 - INFO - 开始处理日期: 2025-08-01
2025-08-03 19:31:17,826 - INFO - Request Parameters - Page 1:
2025-08-03 19:31:17,826 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:17,826 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:18,530 - INFO - Response - Page 1:
2025-08-03 19:31:18,530 - INFO - 第 1 页获取到 50 条记录
2025-08-03 19:31:19,045 - INFO - Request Parameters - Page 2:
2025-08-03 19:31:19,045 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:19,045 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:19,780 - INFO - Response - Page 2:
2025-08-03 19:31:19,780 - INFO - 第 2 页获取到 50 条记录
2025-08-03 19:31:20,280 - INFO - Request Parameters - Page 3:
2025-08-03 19:31:20,280 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:20,280 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:21,014 - INFO - Response - Page 3:
2025-08-03 19:31:21,014 - INFO - 第 3 页获取到 50 条记录
2025-08-03 19:31:21,514 - INFO - Request Parameters - Page 4:
2025-08-03 19:31:21,514 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:21,514 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:22,217 - INFO - Response - Page 4:
2025-08-03 19:31:22,217 - INFO - 第 4 页获取到 50 条记录
2025-08-03 19:31:22,733 - INFO - Request Parameters - Page 5:
2025-08-03 19:31:22,733 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:22,733 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:23,436 - INFO - Response - Page 5:
2025-08-03 19:31:23,436 - INFO - 第 5 页获取到 50 条记录
2025-08-03 19:31:23,936 - INFO - Request Parameters - Page 6:
2025-08-03 19:31:23,936 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:23,936 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:24,624 - INFO - Response - Page 6:
2025-08-03 19:31:24,624 - INFO - 第 6 页获取到 50 条记录
2025-08-03 19:31:25,124 - INFO - Request Parameters - Page 7:
2025-08-03 19:31:25,124 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:25,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:25,827 - INFO - Response - Page 7:
2025-08-03 19:31:25,827 - INFO - 第 7 页获取到 50 条记录
2025-08-03 19:31:26,342 - INFO - Request Parameters - Page 8:
2025-08-03 19:31:26,342 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:26,342 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:27,124 - INFO - Response - Page 8:
2025-08-03 19:31:27,124 - INFO - 第 8 页获取到 50 条记录
2025-08-03 19:31:27,639 - INFO - Request Parameters - Page 9:
2025-08-03 19:31:27,639 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:27,639 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:28,389 - INFO - Response - Page 9:
2025-08-03 19:31:28,389 - INFO - 第 9 页获取到 50 条记录
2025-08-03 19:31:28,889 - INFO - Request Parameters - Page 10:
2025-08-03 19:31:28,889 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:28,889 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:29,608 - INFO - Response - Page 10:
2025-08-03 19:31:29,608 - INFO - 第 10 页获取到 39 条记录
2025-08-03 19:31:30,124 - INFO - 查询完成，共获取到 489 条记录
2025-08-03 19:31:30,124 - INFO - 获取到 489 条表单数据
2025-08-03 19:31:30,124 - INFO - 当前日期 2025-08-01 有 4 条MySQL数据需要处理
2025-08-03 19:31:30,124 - INFO - 日期 2025-08-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 19:31:30,124 - INFO - 开始处理日期: 2025-08-02
2025-08-03 19:31:30,124 - INFO - Request Parameters - Page 1:
2025-08-03 19:31:30,124 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:30,124 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:30,858 - INFO - Response - Page 1:
2025-08-03 19:31:30,858 - INFO - 第 1 页获取到 50 条记录
2025-08-03 19:31:31,358 - INFO - Request Parameters - Page 2:
2025-08-03 19:31:31,358 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:31,358 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:32,061 - INFO - Response - Page 2:
2025-08-03 19:31:32,061 - INFO - 第 2 页获取到 50 条记录
2025-08-03 19:31:32,577 - INFO - Request Parameters - Page 3:
2025-08-03 19:31:32,577 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:32,577 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:33,296 - INFO - Response - Page 3:
2025-08-03 19:31:33,296 - INFO - 第 3 页获取到 50 条记录
2025-08-03 19:31:33,811 - INFO - Request Parameters - Page 4:
2025-08-03 19:31:33,811 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:33,811 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:34,561 - INFO - Response - Page 4:
2025-08-03 19:31:34,561 - INFO - 第 4 页获取到 50 条记录
2025-08-03 19:31:35,077 - INFO - Request Parameters - Page 5:
2025-08-03 19:31:35,077 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:35,077 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:35,780 - INFO - Response - Page 5:
2025-08-03 19:31:35,780 - INFO - 第 5 页获取到 50 条记录
2025-08-03 19:31:36,296 - INFO - Request Parameters - Page 6:
2025-08-03 19:31:36,296 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:36,296 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:37,046 - INFO - Response - Page 6:
2025-08-03 19:31:37,046 - INFO - 第 6 页获取到 50 条记录
2025-08-03 19:31:37,562 - INFO - Request Parameters - Page 7:
2025-08-03 19:31:37,562 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:37,562 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:38,265 - INFO - Response - Page 7:
2025-08-03 19:31:38,265 - INFO - 第 7 页获取到 50 条记录
2025-08-03 19:31:38,765 - INFO - Request Parameters - Page 8:
2025-08-03 19:31:38,765 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:38,765 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:39,468 - INFO - Response - Page 8:
2025-08-03 19:31:39,468 - INFO - 第 8 页获取到 50 条记录
2025-08-03 19:31:39,968 - INFO - Request Parameters - Page 9:
2025-08-03 19:31:39,968 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:39,968 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:40,671 - INFO - Response - Page 9:
2025-08-03 19:31:40,671 - INFO - 第 9 页获取到 50 条记录
2025-08-03 19:31:41,171 - INFO - Request Parameters - Page 10:
2025-08-03 19:31:41,171 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:41,171 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:41,874 - INFO - Response - Page 10:
2025-08-03 19:31:41,874 - INFO - 第 10 页获取到 29 条记录
2025-08-03 19:31:42,390 - INFO - 查询完成，共获取到 479 条记录
2025-08-03 19:31:42,390 - INFO - 获取到 479 条表单数据
2025-08-03 19:31:42,390 - INFO - 当前日期 2025-08-02 有 134 条MySQL数据需要处理
2025-08-03 19:31:42,390 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 19:31:42,390 - INFO - 开始处理日期: 2025-08-03
2025-08-03 19:31:42,390 - INFO - Request Parameters - Page 1:
2025-08-03 19:31:42,390 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:31:42,390 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:31:42,890 - INFO - Response - Page 1:
2025-08-03 19:31:42,890 - INFO - 第 1 页获取到 1 条记录
2025-08-03 19:31:43,406 - INFO - 查询完成，共获取到 1 条记录
2025-08-03 19:31:43,406 - INFO - 获取到 1 条表单数据
2025-08-03 19:31:43,406 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-03 19:31:43,406 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 19:31:43,406 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 1 条
2025-08-03 19:32:43,419 - INFO - 开始同步昨天与今天的销售数据: 2025-08-02 至 2025-08-03
2025-08-03 19:32:43,419 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-03 19:32:43,419 - INFO - 查询参数: ('2025-08-02', '2025-08-03')
2025-08-03 19:32:43,591 - INFO - MySQL查询成功，时间段: 2025-08-02 至 2025-08-03，共获取 500 条记录
2025-08-03 19:32:43,591 - INFO - 获取到 2 个日期需要处理: ['2025-08-02', '2025-08-03']
2025-08-03 19:32:43,591 - INFO - 开始处理日期: 2025-08-02
2025-08-03 19:32:43,591 - INFO - Request Parameters - Page 1:
2025-08-03 19:32:43,591 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:43,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:44,309 - INFO - Response - Page 1:
2025-08-03 19:32:44,309 - INFO - 第 1 页获取到 50 条记录
2025-08-03 19:32:44,825 - INFO - Request Parameters - Page 2:
2025-08-03 19:32:44,825 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:44,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:45,575 - INFO - Response - Page 2:
2025-08-03 19:32:45,575 - INFO - 第 2 页获取到 50 条记录
2025-08-03 19:32:46,075 - INFO - Request Parameters - Page 3:
2025-08-03 19:32:46,075 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:46,075 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:46,794 - INFO - Response - Page 3:
2025-08-03 19:32:46,794 - INFO - 第 3 页获取到 50 条记录
2025-08-03 19:32:47,310 - INFO - Request Parameters - Page 4:
2025-08-03 19:32:47,310 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:47,310 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:48,028 - INFO - Response - Page 4:
2025-08-03 19:32:48,028 - INFO - 第 4 页获取到 50 条记录
2025-08-03 19:32:48,544 - INFO - Request Parameters - Page 5:
2025-08-03 19:32:48,544 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:48,544 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:49,310 - INFO - Response - Page 5:
2025-08-03 19:32:49,310 - INFO - 第 5 页获取到 50 条记录
2025-08-03 19:32:49,825 - INFO - Request Parameters - Page 6:
2025-08-03 19:32:49,825 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:49,825 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:50,638 - INFO - Response - Page 6:
2025-08-03 19:32:50,638 - INFO - 第 6 页获取到 50 条记录
2025-08-03 19:32:51,138 - INFO - Request Parameters - Page 7:
2025-08-03 19:32:51,138 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:51,138 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:51,888 - INFO - Response - Page 7:
2025-08-03 19:32:51,888 - INFO - 第 7 页获取到 50 条记录
2025-08-03 19:32:52,404 - INFO - Request Parameters - Page 8:
2025-08-03 19:32:52,404 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:52,404 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:53,075 - INFO - Response - Page 8:
2025-08-03 19:32:53,075 - INFO - 第 8 页获取到 50 条记录
2025-08-03 19:32:53,591 - INFO - Request Parameters - Page 9:
2025-08-03 19:32:53,591 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:53,591 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:54,357 - INFO - Response - Page 9:
2025-08-03 19:32:54,357 - INFO - 第 9 页获取到 50 条记录
2025-08-03 19:32:54,872 - INFO - Request Parameters - Page 10:
2025-08-03 19:32:54,872 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:54,872 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:55,497 - INFO - Response - Page 10:
2025-08-03 19:32:55,497 - INFO - 第 10 页获取到 29 条记录
2025-08-03 19:32:55,998 - INFO - 查询完成，共获取到 479 条记录
2025-08-03 19:32:55,998 - INFO - 获取到 479 条表单数据
2025-08-03 19:32:55,998 - INFO - 当前日期 2025-08-02 有 479 条MySQL数据需要处理
2025-08-03 19:32:56,013 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 19:32:56,013 - INFO - 开始处理日期: 2025-08-03
2025-08-03 19:32:56,013 - INFO - Request Parameters - Page 1:
2025-08-03 19:32:56,013 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 19:32:56,013 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 19:32:56,544 - INFO - Response - Page 1:
2025-08-03 19:32:56,544 - INFO - 第 1 页获取到 1 条记录
2025-08-03 19:32:57,060 - INFO - 查询完成，共获取到 1 条记录
2025-08-03 19:32:57,060 - INFO - 获取到 1 条表单数据
2025-08-03 19:32:57,060 - INFO - 当前日期 2025-08-03 有 1 条MySQL数据需要处理
2025-08-03 19:32:57,060 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 19:32:57,060 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 19:32:57,060 - INFO - 同步完成
2025-08-03 22:30:33,645 - INFO - 使用默认增量同步（当天更新数据）
2025-08-03 22:30:33,645 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.updated_time, '%%Y-%%m-%%d') = %s
2025-08-03 22:30:33,645 - INFO - 查询参数: ('2025-08-03',)
2025-08-03 22:30:33,817 - INFO - MySQL查询成功，增量数据（日期: 2025-08-03），共获取 261 条记录
2025-08-03 22:30:33,817 - INFO - 获取到 6 个日期需要处理: ['2025-07-18', '2025-07-21', '2025-07-30', '2025-08-01', '2025-08-02', '2025-08-03']
2025-08-03 22:30:33,817 - INFO - 开始处理日期: 2025-07-18
2025-08-03 22:30:33,817 - INFO - Request Parameters - Page 1:
2025-08-03 22:30:33,817 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:30:33,817 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1752768000000, 1752854399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:30:41,927 - ERROR - 处理日期 2025-07-18 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: ********-5D58-7D5F-96A6-CB58ECCB0999 Response: {'code': 'ServiceUnavailable', 'requestid': '********-5D58-7D5F-96A6-CB58ECCB0999', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: ********-5D58-7D5F-96A6-CB58ECCB0999)
2025-08-03 22:30:41,927 - INFO - 开始处理日期: 2025-07-21
2025-08-03 22:30:41,927 - INFO - Request Parameters - Page 1:
2025-08-03 22:30:41,927 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:30:41,927 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753027200000, 1753113599000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:30:50,037 - ERROR - 处理日期 2025-07-21 时发生错误: 获取表单数据失败: Error: ServiceUnavailable code: 503, The request has failed due to a temporary failure of the server. request id: F47192CD-CACA-79D4-AAD5-9AA910A3D22C Response: {'code': 'ServiceUnavailable', 'requestid': 'F47192CD-CACA-79D4-AAD5-9AA910A3D22C', 'message': 'The request has failed due to a temporary failure of the server.', 'statusCode': 503} (错误码: ServiceUnavailable, 错误信息: code: 503, The request has failed due to a temporary failure of the server. request id: F47192CD-CACA-79D4-AAD5-9AA910A3D22C)
2025-08-03 22:30:50,037 - INFO - 开始处理日期: 2025-07-30
2025-08-03 22:30:50,037 - INFO - Request Parameters - Page 1:
2025-08-03 22:30:50,037 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:30:50,052 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:30:54,990 - INFO - Response - Page 1:
2025-08-03 22:30:54,990 - INFO - 第 1 页获取到 50 条记录
2025-08-03 22:30:55,506 - INFO - Request Parameters - Page 2:
2025-08-03 22:30:55,506 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:30:55,506 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:30:56,193 - INFO - Response - Page 2:
2025-08-03 22:30:56,193 - INFO - 第 2 页获取到 50 条记录
2025-08-03 22:30:56,693 - INFO - Request Parameters - Page 3:
2025-08-03 22:30:56,693 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:30:56,693 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:30:57,412 - INFO - Response - Page 3:
2025-08-03 22:30:57,412 - INFO - 第 3 页获取到 50 条记录
2025-08-03 22:30:57,928 - INFO - Request Parameters - Page 4:
2025-08-03 22:30:57,928 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:30:57,928 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:30:58,678 - INFO - Response - Page 4:
2025-08-03 22:30:58,678 - INFO - 第 4 页获取到 50 条记录
2025-08-03 22:30:59,178 - INFO - Request Parameters - Page 5:
2025-08-03 22:30:59,178 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:30:59,178 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:30:59,897 - INFO - Response - Page 5:
2025-08-03 22:30:59,897 - INFO - 第 5 页获取到 50 条记录
2025-08-03 22:31:00,412 - INFO - Request Parameters - Page 6:
2025-08-03 22:31:00,412 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:00,412 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:01,147 - INFO - Response - Page 6:
2025-08-03 22:31:01,147 - INFO - 第 6 页获取到 50 条记录
2025-08-03 22:31:01,647 - INFO - Request Parameters - Page 7:
2025-08-03 22:31:01,647 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:01,647 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:02,366 - INFO - Response - Page 7:
2025-08-03 22:31:02,366 - INFO - 第 7 页获取到 50 条记录
2025-08-03 22:31:02,881 - INFO - Request Parameters - Page 8:
2025-08-03 22:31:02,881 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:02,881 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:03,631 - INFO - Response - Page 8:
2025-08-03 22:31:03,631 - INFO - 第 8 页获取到 50 条记录
2025-08-03 22:31:04,147 - INFO - Request Parameters - Page 9:
2025-08-03 22:31:04,147 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:04,147 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:04,850 - INFO - Response - Page 9:
2025-08-03 22:31:04,850 - INFO - 第 9 页获取到 50 条记录
2025-08-03 22:31:05,366 - INFO - Request Parameters - Page 10:
2025-08-03 22:31:05,366 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:05,366 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:06,084 - INFO - Response - Page 10:
2025-08-03 22:31:06,084 - INFO - 第 10 页获取到 50 条记录
2025-08-03 22:31:06,584 - INFO - Request Parameters - Page 11:
2025-08-03 22:31:06,584 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:06,584 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 11, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753804800000, 1753891199000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:07,272 - INFO - Response - Page 11:
2025-08-03 22:31:07,272 - INFO - 第 11 页获取到 44 条记录
2025-08-03 22:31:07,788 - INFO - 查询完成，共获取到 544 条记录
2025-08-03 22:31:07,788 - INFO - 获取到 544 条表单数据
2025-08-03 22:31:07,788 - INFO - 当前日期 2025-07-30 有 1 条MySQL数据需要处理
2025-08-03 22:31:07,788 - INFO - 日期 2025-07-30 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 22:31:07,788 - INFO - 开始处理日期: 2025-08-01
2025-08-03 22:31:07,788 - INFO - Request Parameters - Page 1:
2025-08-03 22:31:07,788 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:07,788 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:08,585 - INFO - Response - Page 1:
2025-08-03 22:31:08,585 - INFO - 第 1 页获取到 50 条记录
2025-08-03 22:31:09,085 - INFO - Request Parameters - Page 2:
2025-08-03 22:31:09,085 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:09,085 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:09,756 - INFO - Response - Page 2:
2025-08-03 22:31:09,756 - INFO - 第 2 页获取到 50 条记录
2025-08-03 22:31:10,272 - INFO - Request Parameters - Page 3:
2025-08-03 22:31:10,272 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:10,272 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:11,053 - INFO - Response - Page 3:
2025-08-03 22:31:11,053 - INFO - 第 3 页获取到 50 条记录
2025-08-03 22:31:11,564 - INFO - Request Parameters - Page 4:
2025-08-03 22:31:11,564 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:11,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:12,283 - INFO - Response - Page 4:
2025-08-03 22:31:12,283 - INFO - 第 4 页获取到 50 条记录
2025-08-03 22:31:12,783 - INFO - Request Parameters - Page 5:
2025-08-03 22:31:12,783 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:12,783 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:13,470 - INFO - Response - Page 5:
2025-08-03 22:31:13,470 - INFO - 第 5 页获取到 50 条记录
2025-08-03 22:31:13,971 - INFO - Request Parameters - Page 6:
2025-08-03 22:31:13,971 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:13,971 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:14,674 - INFO - Response - Page 6:
2025-08-03 22:31:14,674 - INFO - 第 6 页获取到 50 条记录
2025-08-03 22:31:15,189 - INFO - Request Parameters - Page 7:
2025-08-03 22:31:15,189 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:15,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:15,846 - INFO - Response - Page 7:
2025-08-03 22:31:15,846 - INFO - 第 7 页获取到 50 条记录
2025-08-03 22:31:16,361 - INFO - Request Parameters - Page 8:
2025-08-03 22:31:16,361 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:16,361 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:17,080 - INFO - Response - Page 8:
2025-08-03 22:31:17,080 - INFO - 第 8 页获取到 50 条记录
2025-08-03 22:31:17,596 - INFO - Request Parameters - Page 9:
2025-08-03 22:31:17,596 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:17,596 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:18,252 - INFO - Response - Page 9:
2025-08-03 22:31:18,252 - INFO - 第 9 页获取到 50 条记录
2025-08-03 22:31:18,768 - INFO - Request Parameters - Page 10:
2025-08-03 22:31:18,768 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:18,768 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1753977600000, 1754063999000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:19,439 - INFO - Response - Page 10:
2025-08-03 22:31:19,439 - INFO - 第 10 页获取到 39 条记录
2025-08-03 22:31:19,940 - INFO - 查询完成，共获取到 489 条记录
2025-08-03 22:31:19,940 - INFO - 获取到 489 条表单数据
2025-08-03 22:31:19,940 - INFO - 当前日期 2025-08-01 有 4 条MySQL数据需要处理
2025-08-03 22:31:19,940 - INFO - 日期 2025-08-01 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 22:31:19,940 - INFO - 开始处理日期: 2025-08-02
2025-08-03 22:31:19,940 - INFO - Request Parameters - Page 1:
2025-08-03 22:31:19,940 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:19,940 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:20,674 - INFO - Response - Page 1:
2025-08-03 22:31:20,674 - INFO - 第 1 页获取到 50 条记录
2025-08-03 22:31:21,190 - INFO - Request Parameters - Page 2:
2025-08-03 22:31:21,190 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:21,190 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:21,893 - INFO - Response - Page 2:
2025-08-03 22:31:21,893 - INFO - 第 2 页获取到 50 条记录
2025-08-03 22:31:22,393 - INFO - Request Parameters - Page 3:
2025-08-03 22:31:22,393 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:22,393 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:23,065 - INFO - Response - Page 3:
2025-08-03 22:31:23,065 - INFO - 第 3 页获取到 50 条记录
2025-08-03 22:31:23,580 - INFO - Request Parameters - Page 4:
2025-08-03 22:31:23,580 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:23,580 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:24,346 - INFO - Response - Page 4:
2025-08-03 22:31:24,346 - INFO - 第 4 页获取到 50 条记录
2025-08-03 22:31:24,846 - INFO - Request Parameters - Page 5:
2025-08-03 22:31:24,846 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:24,846 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:25,518 - INFO - Response - Page 5:
2025-08-03 22:31:25,518 - INFO - 第 5 页获取到 50 条记录
2025-08-03 22:31:26,018 - INFO - Request Parameters - Page 6:
2025-08-03 22:31:26,018 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:26,018 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:26,705 - INFO - Response - Page 6:
2025-08-03 22:31:26,705 - INFO - 第 6 页获取到 50 条记录
2025-08-03 22:31:27,221 - INFO - Request Parameters - Page 7:
2025-08-03 22:31:27,221 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:27,221 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:27,940 - INFO - Response - Page 7:
2025-08-03 22:31:27,940 - INFO - 第 7 页获取到 50 条记录
2025-08-03 22:31:28,440 - INFO - Request Parameters - Page 8:
2025-08-03 22:31:28,440 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:28,440 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:29,143 - INFO - Response - Page 8:
2025-08-03 22:31:29,143 - INFO - 第 8 页获取到 50 条记录
2025-08-03 22:31:29,643 - INFO - Request Parameters - Page 9:
2025-08-03 22:31:29,643 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:29,643 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:30,393 - INFO - Response - Page 9:
2025-08-03 22:31:30,393 - INFO - 第 9 页获取到 50 条记录
2025-08-03 22:31:30,909 - INFO - Request Parameters - Page 10:
2025-08-03 22:31:30,909 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:30,909 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:31,596 - INFO - Response - Page 10:
2025-08-03 22:31:31,596 - INFO - 第 10 页获取到 29 条记录
2025-08-03 22:31:32,112 - INFO - 查询完成，共获取到 479 条记录
2025-08-03 22:31:32,112 - INFO - 获取到 479 条表单数据
2025-08-03 22:31:32,112 - INFO - 当前日期 2025-08-02 有 137 条MySQL数据需要处理
2025-08-03 22:31:32,112 - INFO - 开始批量插入 3 条新记录
2025-08-03 22:31:32,284 - INFO - 批量插入响应状态码: 200
2025-08-03 22:31:32,284 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 14:31:31 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '156', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '83D65985-3874-78FB-8AAD-71E53B6A295A', 'x-acs-trace-id': '6efea09797789bf0225db1a66d2e1d10', 'etag': '1C2Zk5prO3YTQc2G25zegLA6', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 22:31:32,284 - INFO - 批量插入响应体: {'result': ['FINST-VFF66XA14MNX08RLFH65IDIGFD1T3X0Y4SVDMTJ', 'FINST-VFF66XA14MNX08RLFH65IDIGFD1T3X0Y4SVDMUJ', 'FINST-VFF66XA14MNX08RLFH65IDIGFD1T3X0Y4SVDMVJ']}
2025-08-03 22:31:32,284 - INFO - 批量插入表单数据成功，批次 1，共 3 条记录
2025-08-03 22:31:32,284 - INFO - 成功插入的数据ID: ['FINST-VFF66XA14MNX08RLFH65IDIGFD1T3X0Y4SVDMTJ', 'FINST-VFF66XA14MNX08RLFH65IDIGFD1T3X0Y4SVDMUJ', 'FINST-VFF66XA14MNX08RLFH65IDIGFD1T3X0Y4SVDMVJ']
2025-08-03 22:31:37,300 - INFO - 批量插入完成，共 3 条记录
2025-08-03 22:31:37,300 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 3 条，错误: 0 条
2025-08-03 22:31:37,300 - INFO - 开始处理日期: 2025-08-03
2025-08-03 22:31:37,300 - INFO - Request Parameters - Page 1:
2025-08-03 22:31:37,300 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:31:37,300 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:31:37,784 - INFO - Response - Page 1:
2025-08-03 22:31:37,784 - INFO - 第 1 页获取到 1 条记录
2025-08-03 22:31:38,300 - INFO - 查询完成，共获取到 1 条记录
2025-08-03 22:31:38,300 - INFO - 获取到 1 条表单数据
2025-08-03 22:31:38,300 - INFO - 当前日期 2025-08-03 有 112 条MySQL数据需要处理
2025-08-03 22:31:38,300 - INFO - 开始批量插入 111 条新记录
2025-08-03 22:31:38,565 - INFO - 批量插入响应状态码: 200
2025-08-03 22:31:38,565 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 14:31:38 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2381', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': '755E531F-4DB2-727D-847E-3B0AAD48A80A', 'x-acs-trace-id': '77b89c82914b212a4a2fda04de8392ea', 'etag': '2/W+a5EYj0lIV9vH3IDKX0g1', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 22:31:38,565 - INFO - 批量插入响应体: {'result': ['FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM5', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM6', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM7', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM8', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM9', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMA', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMB', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMC', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMD', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDME', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMF', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMG', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMH', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMI', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMJ', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMK', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDML', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMM', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMN', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMO', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMP', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMQ', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMR', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMS', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMT', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMU', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMV', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMW', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMX', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMY', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMZ', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM01', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM11', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM21', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM31', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM41', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM51', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM61', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM71', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM81', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM91', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMA1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMB1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMC1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMD1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDME1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMF1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMG1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMH1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMI1']}
2025-08-03 22:31:38,565 - INFO - 批量插入表单数据成功，批次 1，共 50 条记录
2025-08-03 22:31:38,565 - INFO - 成功插入的数据ID: ['FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM5', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM6', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM7', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM8', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDM9', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMA', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMB', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMC', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMD', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDME', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMF', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMG', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMH', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMI', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMJ', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA38V25SVDMK', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDML', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMM', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMN', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMO', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMP', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMQ', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMR', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMS', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMT', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMU', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMV', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMW', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMX', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMY', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMZ', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM01', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM11', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM21', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM31', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM41', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM51', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM61', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM71', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM81', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDM91', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMA1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMB1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMC1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMD1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDME1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMF1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMG1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMH1', 'FINST-ACB66071XPPXAY0YBXFPDA3195KA39V25SVDMI1']
2025-08-03 22:31:43,800 - INFO - 批量插入响应状态码: 200
2025-08-03 22:31:43,800 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 14:31:43 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '2412', 'connection': 'keep-alive', 'vary': 'Accept-Encoding, Accept-Encoding', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'C21E0B67-796F-7F27-ABAC-D2153E764202', 'x-acs-trace-id': 'c11032c31ec04bb3207f9c4f506b28af', 'etag': '2JfFO6bk9J3MlJ+lcbSIqfw2', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 22:31:43,800 - INFO - 批量插入响应体: {'result': ['FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDM6K', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDM7K', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDM8K', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDM9K', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMAK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMBK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMCK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMDK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMEK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMFK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMGK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMHK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMIK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMJK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMKK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMLK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMMK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMNK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMOK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMPK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMQK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMRK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMSK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMTK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMUK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMVK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMWK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMXK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMYK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMZK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM0L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM1L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM2L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM3L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM4L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM5L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM6L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM7L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM8L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM9L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMAL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMBL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMCL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMDL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMEL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMFL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMGL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMHL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMIL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMJL']}
2025-08-03 22:31:43,800 - INFO - 批量插入表单数据成功，批次 2，共 50 条记录
2025-08-03 22:31:43,800 - INFO - 成功插入的数据ID: ['FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDM6K', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDM7K', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDM8K', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDM9K', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMAK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMBK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMCK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMDK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMEK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMFK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMGK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3UW65SVDMHK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMIK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMJK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMKK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMLK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMMK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMNK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMOK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMPK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMQK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMRK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMSK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMTK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMUK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMVK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMWK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMXK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMYK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMZK', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM0L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM1L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM2L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM3L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM4L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM5L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM6L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM7L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM8L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDM9L', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMAL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMBL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMCL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMDL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMEL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMFL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMGL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMHL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMIL', 'FINST-VEC667D10CMX2PKXELL5UBJ99P3K3VW65SVDMJL']
2025-08-03 22:31:48,956 - INFO - 批量插入响应状态码: 200
2025-08-03 22:31:48,956 - INFO - 批量插入响应头: {'server': 'DingTalk/1.0.0', 'date': 'Sun, 03 Aug 2025 14:31:48 GMT', 'content-type': 'application/json;charset=utf-8', 'content-length': '540', 'connection': 'keep-alive', 'access-control-allow-origin': '*', 'access-control-expose-headers': '*', 'x-acs-request-id': 'BB0B1D55-991F-751B-A469-5B3B41514083', 'x-acs-trace-id': 'a4cd33f3988af9b043274c526f0f31f9', 'etag': '5RxzP+f/DAnvmGbXOj2nNQw0', 'access-control-allow-headers': 'X-Requested-With, X-Sequence, _aop_secret, _aop_signature, x-acs-dingtalk-access-token'}
2025-08-03 22:31:48,956 - INFO - 批量插入响应体: {'result': ['FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMP6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMQ6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMR6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMS6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMT6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMU6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMV6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMW6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMX6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMY6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMZ6']}
2025-08-03 22:31:48,956 - INFO - 批量插入表单数据成功，批次 3，共 11 条记录
2025-08-03 22:31:48,956 - INFO - 成功插入的数据ID: ['FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMP6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMQ6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMR6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMS6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMT6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMU6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMV6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMW6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMX6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMY6', 'FINST-YQ966PD1LMNX90RP880J441CEORJ22WA5SVDMZ6']
2025-08-03 22:31:53,972 - INFO - 批量插入完成，共 111 条记录
2025-08-03 22:31:53,972 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 111 条，错误: 0 条
2025-08-03 22:31:53,972 - INFO - 数据同步完成！更新: 0 条，插入: 114 条，错误: 2 条
2025-08-03 22:32:53,986 - INFO - 开始同步昨天与今天的销售数据: 2025-08-02 至 2025-08-03
2025-08-03 22:32:53,986 - INFO - MySQL查询SQL: 
                SELECT 
                    a.project_code,
                    b.name AS project_name,
                    a.store_code,
                    c.name AS store_name,
                    DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') AS sales_date,
                    a.online_amount AS online_amount,
                    a.offline_amount AS offline_amount,
                    a.total_amount AS total_amount,
                    a.order_count AS order_count,
                    case when a.report_source=0 then '商户上报' when a.report_source=1 then '运营调整' when  a.report_source=0 then '运营补录' end as report_source,
                    d.url
                FROM
                    yx_b_sales_record a
                        JOIN
                    yx_b_projects b ON a.project_code = b.code
                        JOIN
                    yx_b_tenants c ON a.store_code = c.code
                        LEFT JOIN
                    yx_b_annex d ON a.code = d.association_id
                WHERE
                    a.status = 2 AND a.deleted = 0
                        AND c.deleted = 0
                        AND d.deleted = 0
             AND DATE_FORMAT(a.sales_time, '%%Y-%%m-%%d') BETWEEN %s AND %s
2025-08-03 22:32:53,986 - INFO - 查询参数: ('2025-08-02', '2025-08-03')
2025-08-03 22:32:54,158 - INFO - MySQL查询成功，时间段: 2025-08-02 至 2025-08-03，共获取 618 条记录
2025-08-03 22:32:54,158 - INFO - 获取到 2 个日期需要处理: ['2025-08-02', '2025-08-03']
2025-08-03 22:32:54,173 - INFO - 开始处理日期: 2025-08-02
2025-08-03 22:32:54,173 - INFO - Request Parameters - Page 1:
2025-08-03 22:32:54,173 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:32:54,173 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:32:54,970 - INFO - Response - Page 1:
2025-08-03 22:32:54,970 - INFO - 第 1 页获取到 50 条记录
2025-08-03 22:32:55,470 - INFO - Request Parameters - Page 2:
2025-08-03 22:32:55,470 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:32:55,470 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:32:56,267 - INFO - Response - Page 2:
2025-08-03 22:32:56,267 - INFO - 第 2 页获取到 50 条记录
2025-08-03 22:32:56,767 - INFO - Request Parameters - Page 3:
2025-08-03 22:32:56,767 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:32:56,767 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:32:57,486 - INFO - Response - Page 3:
2025-08-03 22:32:57,486 - INFO - 第 3 页获取到 50 条记录
2025-08-03 22:32:57,986 - INFO - Request Parameters - Page 4:
2025-08-03 22:32:57,986 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:32:57,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 4, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:32:58,689 - INFO - Response - Page 4:
2025-08-03 22:32:58,689 - INFO - 第 4 页获取到 50 条记录
2025-08-03 22:32:59,189 - INFO - Request Parameters - Page 5:
2025-08-03 22:32:59,189 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:32:59,189 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 5, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:32:59,892 - INFO - Response - Page 5:
2025-08-03 22:32:59,892 - INFO - 第 5 页获取到 50 条记录
2025-08-03 22:33:00,392 - INFO - Request Parameters - Page 6:
2025-08-03 22:33:00,392 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:33:00,392 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 6, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:33:01,095 - INFO - Response - Page 6:
2025-08-03 22:33:01,095 - INFO - 第 6 页获取到 50 条记录
2025-08-03 22:33:01,611 - INFO - Request Parameters - Page 7:
2025-08-03 22:33:01,611 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:33:01,611 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 7, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:33:02,517 - INFO - Response - Page 7:
2025-08-03 22:33:02,517 - INFO - 第 7 页获取到 50 条记录
2025-08-03 22:33:03,017 - INFO - Request Parameters - Page 8:
2025-08-03 22:33:03,017 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:33:03,017 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 8, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:33:03,752 - INFO - Response - Page 8:
2025-08-03 22:33:03,752 - INFO - 第 8 页获取到 50 条记录
2025-08-03 22:33:04,267 - INFO - Request Parameters - Page 9:
2025-08-03 22:33:04,267 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:33:04,267 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 9, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:33:05,064 - INFO - Response - Page 9:
2025-08-03 22:33:05,064 - INFO - 第 9 页获取到 50 条记录
2025-08-03 22:33:05,564 - INFO - Request Parameters - Page 10:
2025-08-03 22:33:05,564 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:33:05,564 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 10, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754064000000, 1754150399000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:33:06,189 - INFO - Response - Page 10:
2025-08-03 22:33:06,189 - INFO - 第 10 页获取到 32 条记录
2025-08-03 22:33:06,705 - INFO - 查询完成，共获取到 482 条记录
2025-08-03 22:33:06,705 - INFO - 获取到 482 条表单数据
2025-08-03 22:33:06,705 - INFO - 当前日期 2025-08-02 有 482 条MySQL数据需要处理
2025-08-03 22:33:06,721 - INFO - 日期 2025-08-02 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 22:33:06,721 - INFO - 开始处理日期: 2025-08-03
2025-08-03 22:33:06,721 - INFO - Request Parameters - Page 1:
2025-08-03 22:33:06,721 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:33:06,721 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 1, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:33:07,471 - INFO - Response - Page 1:
2025-08-03 22:33:07,471 - INFO - 第 1 页获取到 50 条记录
2025-08-03 22:33:07,986 - INFO - Request Parameters - Page 2:
2025-08-03 22:33:07,986 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:33:07,986 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 2, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:33:08,705 - INFO - Response - Page 2:
2025-08-03 22:33:08,705 - INFO - 第 2 页获取到 50 条记录
2025-08-03 22:33:09,205 - INFO - Request Parameters - Page 3:
2025-08-03 22:33:09,205 - INFO - Headers: {'x-acs-dingtalk-access-token': '1ae86fefe27b3a7fbb07abfbbb38e6d3'}
2025-08-03 22:33:09,205 - INFO - Request: {'appType': 'APP_D7E6ZB94ZUL5Q1GUAOLD', 'formUuid': 'FORM-9AE718C1C466495DA7D1537C946B1F17XE30', 'pageNumber': 3, 'pageSize': 50, 'searchCondition': '[{"key": "dateField_m9nw1k71", "value": [1754150400000, 1754236799000], "type": "DOUBLE", "operator": "between", "componentName": "DateField"}]', 'systemToken': 'BOD66V818WKTBQXWCHH7G4V1FVDL3B5YFJ28MV2', 'userId': 'hexuepeng'}
2025-08-03 22:33:09,830 - INFO - Response - Page 3:
2025-08-03 22:33:09,830 - INFO - 第 3 页获取到 12 条记录
2025-08-03 22:33:10,330 - INFO - 查询完成，共获取到 112 条记录
2025-08-03 22:33:10,330 - INFO - 获取到 112 条表单数据
2025-08-03 22:33:10,330 - INFO - 当前日期 2025-08-03 有 112 条MySQL数据需要处理
2025-08-03 22:33:10,330 - INFO - 日期 2025-08-03 处理完成 - 更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 22:33:10,330 - INFO - 数据同步完成！更新: 0 条，插入: 0 条，错误: 0 条
2025-08-03 22:33:10,330 - INFO - 同步完成
